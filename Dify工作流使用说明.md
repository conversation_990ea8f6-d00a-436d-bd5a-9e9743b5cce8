# Dify容量报告生成器工作流使用说明

## 概述

本工作流支持通过API自动获取IT基础设施容量数据，生成专业的容量分析报告，并导出为Word文档格式。

## 文件说明

- `dify_simple_api_workflow.yml` - 主工作流配置文件（已修复API数据获取问题）
- `app.py` - Flask API服务器
- `test_dify_code_node.py` - 代码节点测试脚本
- `test_word_export.py` - Word导出功能测试脚本

## 使用步骤

### 1. 启动API服务

首先启动Flask API服务：

```bash
cd D:\work\LLM
python app.py
```

服务启动后会在 `http://localhost:5000` 提供API接口。

### 2. 导入工作流到Dify

1. 登录Dify平台 (http://************:980)
2. 进入工作流管理页面
3. 选择"导入工作流"
4. 上传 `dify_simple_api_workflow.yml` 文件
5. 确认导入成功

### 3. 配置工作流参数

在工作流中需要填写以下参数：

- **报告日期**: 格式为 YYYY-MM-DD，例如：2024-01-15
- **系统名称**: 报告标题，例如：生产环境运维资源容量检查报告
- **API服务地址**: Flask API服务地址，例如：http://localhost:5000
- **Word文件保存路径**: 可选，默认为 D:/work/LLM/reports/

### 4. 运行工作流

点击"运行"按钮，工作流将：

1. **获取API数据**: 自动调用四个API端点获取容量数据
   - /api/storage - 存储容量数据
   - /api/database - 数据库容量数据
   - /api/container - 容器资源数据
   - /api/virtualization - 虚拟化资源数据

2. **生成报告**: 使用LLM根据获取的数据生成专业的容量分析报告

3. **导出Word文档**: 将报告转换为HTML格式（可用Word打开）并保存到指定路径

## 故障排除

### 问题1: SyntaxError: 'return' outside function

**原因**: 代码节点中的变量引用格式错误

**解决方案**: 已在最新版本中修复，确保使用 `{{#start.api_base_url#}}` 格式引用变量

### 问题2: API连接失败

**症状**: 工作流显示"连接失败"或"HTTP错误"

**解决方案**:
1. 确认Flask API服务正在运行
2. 检查API服务地址是否正确
3. 运行测试脚本验证API连接：
   ```bash
   python test_dify_code_node.py
   ```

### 问题3: Word文档生成失败

**症状**: 报告生成成功但Word导出失败

**解决方案**:
1. 检查保存路径是否存在写入权限
2. 运行Word导出测试：
   ```bash
   python test_word_export.py
   ```

## 测试脚本使用

### API数据获取测试

```bash
python test_dify_code_node.py
```

此脚本会：
- 测试URL格式处理逻辑
- 验证API数据获取功能
- 显示各端点的连接状态
- 展示数据样例

### Word导出功能测试

```bash
python test_word_export.py
```

此脚本会：
- 生成示例容量报告
- 转换为HTML格式
- 保存到指定目录
- 验证文件生成结果

## 输出文件

### 报告文件

生成的HTML文件命名格式：`{系统名称}_{报告日期}.html`

例如：`生产环境运维资源容量检查报告_20240115.html`

### 文件特点

- 使用HTML格式，可直接用Microsoft Word打开
- 包含完整的表格和格式
- 支持中文字体显示
- 可另存为.docx格式

## 工作流节点说明

1. **开始节点**: 收集用户输入参数
2. **获取API数据节点**: Python代码节点，调用API获取容量数据
3. **生成容量报告节点**: LLM节点，根据数据生成报告
4. **导出Word文档节点**: Python代码节点，转换报告为HTML格式
5. **报告输出节点**: 显示最终结果和文件信息

## 注意事项

1. 确保API服务在工作流运行期间保持可用
2. Word文件保存路径必须具有写入权限
3. 生成的HTML文件可以用Word打开并另存为.docx格式
4. 如需修改报告格式，可调整LLM节点的提示词模板

## 技术支持

如遇到问题，请：

1. 首先运行相关测试脚本进行诊断
2. 检查API服务日志
3. 验证工作流配置参数
4. 查看Dify平台的执行日志
