{"version": "0.1.0", "kind": "workflow", "data": {"title": "容量报告生成器-兼容版", "desc": "通过API获取容量数据并生成标准格式报告", "default_block_configs": {}, "graph": {"edges": [{"id": "start-llm", "source": "start", "target": "llm", "sourceHandle": "source", "targetHandle": "target"}, {"id": "llm-end", "source": "llm", "target": "end", "sourceHandle": "source", "targetHandle": "target"}], "nodes": [{"id": "start", "position": {"x": 80, "y": 282}, "type": "start", "data": {"title": "开始", "type": "start", "variables": [{"variable": "report_date", "label": "报告日期", "type": "text-input", "required": true, "max_length": 50}, {"variable": "system_name", "label": "系统名称", "type": "text-input", "required": true, "max_length": 100}, {"variable": "api_base_url", "label": "API服务地址", "type": "text-input", "required": true, "max_length": 200, "default": "http://localhost:5000"}, {"variable": "storage_info", "label": "存储容量信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入存储容量信息"}, {"variable": "database_info", "label": "数据库容量信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入数据库容量信息"}, {"variable": "container_info", "label": "容器资源信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入容器资源信息"}, {"variable": "vm_info", "label": "虚拟化资源信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入虚拟化资源信息"}]}}, {"id": "llm", "position": {"x": 400, "y": 282}, "type": "llm", "data": {"title": "容量报告生成", "type": "llm", "model": {"provider": "openai", "name": "gpt-4", "mode": "chat", "completion_params": {"temperature": 0.2}}, "prompt_template": [{"role": "system", "text": "你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。你需要首先尝试从API获取数据，如果API不可用则使用手动输入的数据，生成包含存储、数据库、容器、虚拟化四个维度的详细容量分析报告。"}, {"role": "user", "text": "请根据以下信息生成一份专业的容量报告：\n\n**基本信息：**\n- 报告日期：{{#start.report_date#}}\n- 系统名称：{{#start.system_name#}}\n- API服务地址：{{#start.api_base_url#}}\n\n**数据获取说明：**\n请首先尝试从以下API地址获取数据：\n- 存储数据：{{#start.api_base_url#}}/api/storage\n- 数据库数据：{{#start.api_base_url#}}/api/database\n- 容器数据：{{#start.api_base_url#}}/api/container\n- 虚拟化数据：{{#start.api_base_url#}}/api/virtualization\n\n如果API不可用，请使用以下手动输入的数据：\n\n**存储容量信息：**\n{{#start.storage_info#}}\n\n**数据库容量信息：**\n{{#start.database_info#}}\n\n**容器资源信息：**\n{{#start.container_info#}}\n\n**虚拟化资源信息：**\n{{#start.vm_info#}}\n\n请严格按照以下格式生成报告：\n\n# {{#start.system_name#}}\n\n## 1. 存储资源容量及健康度排查\n\n存储资源池本次排查情况如下：\n\n| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|---------------|-------------|------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （存储使用率<90%）运行良好。\n- 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各存储池状态]\n\n**发现问题详情：** [列出发现的问题，如无问题则说明\"今日未发现问题\"]\n\n**应对措施和预案：** [提供措施，如无问题则说明\"不涉及\"]\n\n## 2. 数据库资源容量及健康度排查\n\n数据库资源池本次排查情况如下：\n\n| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|-----------------|-------------|------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （数据库使用率<85%）运行良好。\n- 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各数据库状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n## 3. 容器资源容量及健康度排查\n\n容器资源池本次排查情况如下：\n\n| 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。\n- 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各容器集群状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n## 4. 虚拟化资源容量及健康度排查\n\n虚拟化资源池本次排查情况如下：\n\n| 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。\n- 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各虚拟化集群状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n---\n\n**要求：**\n1. 严格按照上述格式输出\n2. 优先尝试从API获取数据，如果无法获取则使用手动输入的数据\n3. 表格数据必须准确填写\n4. 健康度评估要根据使用率阈值准确判断\n5. 状态描述要具体明确\n6. 输出格式为Markdown\n7. 如果是从API获取的数据，请在报告开头说明\"数据来源：API自动获取\"\n8. 如果使用手动输入的数据，请说明\"数据来源：手动输入\""}], "vision": {"enabled": false}}}, {"id": "end", "position": {"x": 720, "y": 282}, "type": "end", "data": {"title": "结束", "type": "end", "outputs": [{"value_selector": ["llm", "text"], "variable": "capacity_report"}]}}], "viewport": {"x": 0, "y": 0, "zoom": 1}}, "features": {"opening_statement": "欢迎使用容量报告生成器！请提供API服务地址和基本信息，我将为您生成专业的容量分析报告。如果API服务不可用，也可以手动输入容量数据。", "suggested_questions": ["如何启动Flask API服务？", "API服务地址应该怎么填写？", "如果API不可用怎么办？"], "speech_to_text": {"enabled": false}, "text_to_speech": {"enabled": false}, "retrieval": {"enabled": false}, "annotation": {"enabled": false}}}}