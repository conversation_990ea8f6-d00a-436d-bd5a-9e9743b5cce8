{"version": "0.1.0", "kind": "app", "data": {"title": "智能容量报告系统", "description": "通过API获取容量参数，LLM分析后按规定格式生成报告，并导出Word文档", "icon": "📊", "icon_background": "#2563EB", "mode": "workflow", "workflow": {"graph": {"nodes": [{"id": "start", "data": {"type": "start", "title": "开始", "variables": [{"variable": "api_base_url", "type": "text-input", "label": "API服务地址", "required": true, "default": "http://***************:5000"}, {"variable": "report_date", "type": "text-input", "label": "报告日期", "required": true, "default": "2024-01-15"}, {"variable": "system_name", "type": "text-input", "label": "系统名称", "required": true, "default": "生产环境运维资源容量检查报告"}, {"variable": "enable_llm", "type": "select", "label": "启用LLM分析", "required": true, "default": "false", "options": [{"label": "启用LLM智能分析", "value": "true"}, {"label": "使用本地分析", "value": "false"}]}]}, "position": {"x": 100, "y": 200}}, {"id": "generate_report", "data": {"type": "http-request", "title": "生成智能报告", "method": "post", "url": "{{#start.api_base_url#}}/api/generate_smart_report", "headers": "Content-Type: application/json", "body": {"type": "json", "data": "{\n  \"report_date\": \"{{#start.report_date#}}\",\n  \"system_name\": \"{{#start.system_name#}}\",\n  \"llm_config\": {\n    \"enabled\": {{#start.enable_llm#}}\n  }\n}"}, "timeout": {"read": 120}}, "position": {"x": 400, "y": 200}}, {"id": "export_word", "data": {"type": "http-request", "title": "导出Word文档", "method": "post", "url": "{{#start.api_base_url#}}/api/export_word", "headers": "Content-Type: application/json", "body": {"type": "json", "data": "{\n  \"report_content\": \"{{#generate_report.body.report_content#}}\",\n  \"report_date\": \"{{#generate_report.body.report_date#}}\",\n  \"system_name\": \"{{#generate_report.body.system_name#}}\",\n  \"save_path\": \"./reports/\"\n}"}, "timeout": {"read": 60}}, "position": {"x": 700, "y": 200}}, {"id": "end", "data": {"type": "end", "title": "完成", "outputs": [{"variable": "success", "type": "boolean", "value_selector": ["generate_report", "body", "success"]}, {"variable": "report_type", "type": "string", "value_selector": ["generate_report", "body", "report_type"]}, {"variable": "data_source", "type": "string", "value_selector": ["generate_report", "body", "data_source"]}, {"variable": "llm_enabled", "type": "boolean", "value_selector": ["generate_report", "body", "llm_enabled"]}, {"variable": "report_length", "type": "number", "value_selector": ["generate_report", "body", "report_length"]}, {"variable": "reading_time", "type": "string", "value_selector": ["generate_report", "body", "estimated_reading_time"]}, {"variable": "word_file_path", "type": "string", "value_selector": ["export_word", "body", "file_path"]}, {"variable": "word_file_size", "type": "string", "value_selector": ["export_word", "body", "file_size"]}, {"variable": "export_success", "type": "boolean", "value_selector": ["export_word", "body", "success"]}]}, "position": {"x": 1000, "y": 200}}], "edges": [{"source": "start", "target": "generate_report"}, {"source": "generate_report", "target": "export_word"}, {"source": "export_word", "target": "end"}]}}, "model_config": {"opening_statement": "🎉 欢迎使用智能容量报告系统！\n\n✨ 系统功能：\n🔍 自动获取容量参数（存储、数据库、容器、虚拟化）\n🧠 LLM智能分析和风险评估\n📊 按规定格式生成专业报告\n📄 导出为Word文档格式\n\n📋 使用流程：\n1. 配置API服务地址\n2. 设置报告日期和系统名称\n3. 选择是否启用LLM分析\n4. 点击运行开始生成报告\n\n⚡ 预计处理时间：2-3分钟\n📁 文档保存位置：./reports/ 目录", "suggested_questions": ["生成今日的生产环境容量报告", "使用LLM分析生成智能容量报告", "生成包含风险评估的专业报告", "导出Word格式的容量检查报告"]}}}