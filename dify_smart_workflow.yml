# Dify智能容量报告生成工作流配置
# 支持LLM分析和智能报告生成

app:
  mode: workflow
  name: "智能容量报告生成器"
  description: "通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告"
  icon: "📊"
  icon_background: "#3B82F6"

workflow:
  graph:
    nodes:
      - id: start
        data:
          type: start
          title: "开始"
          variables:
            - variable: api_base_url
              type: text-input
              label: "API服务地址"
              description: "Flask API服务的基础URL"
              required: true
              default: "http://localhost:5000"
            - variable: report_date
              type: text-input
              label: "报告日期"
              description: "容量报告的日期，格式：YYYY-MM-DD"
              required: true
              default: "2024-01-15"
            - variable: system_name
              type: text-input
              label: "系统名称"
              description: "容量报告的系统名称"
              required: true
              default: "生产环境运维资源容量检查报告"
            - variable: use_llm
              type: select
              label: "启用LLM分析"
              description: "是否使用LLM进行智能分析"
              required: true
              default: "local"
              options:
                - label: "本地分析"
                  value: "local"
                - label: "LLM增强分析"
                  value: "llm"
            - variable: llm_api_url
              type: text-input
              label: "LLM API地址"
              description: "LLM服务的API地址（可选）"
              required: false
              default: ""
            - variable: llm_api_key
              type: text-input
              label: "LLM API密钥"
              description: "LLM服务的API密钥（可选）"
              required: false
              default: ""
            - variable: save_path
              type: text-input
              label: "保存路径"
              description: "Word文档保存路径"
              required: true
              default: "./reports/"

      - id: generate_smart_report
        data:
          type: http-request
          title: "生成智能容量报告"
          method: POST
          url: "{{#start.api_base_url#}}/api/generate_smart_report"
          headers:
            Content-Type: "application/json"
          body:
            type: json
            data: |
              {
                "report_date": "{{#start.report_date#}}",
                "system_name": "{{#start.system_name#}}",
                "llm_config": {
                  "api_url": "{{#start.llm_api_url#}}",
                  "api_key": "{{#start.llm_api_key#}}"
                }
              }
          timeout: 60

      - id: export_word
        data:
          type: http-request
          title: "导出Word文档"
          method: POST
          url: "{{#start.api_base_url#}}/api/export_word"
          headers:
            Content-Type: "application/json"
          body:
            type: json
            data: |
              {
                "report_content": "{{#generate_smart_report.body.report_content#}}",
                "report_date": "{{#generate_smart_report.body.report_date#}}",
                "system_name": "{{#generate_smart_report.body.system_name#}}",
                "save_path": "{{#start.save_path#}}"
              }
          timeout: 30

      - id: end
        data:
          type: end
          title: "完成"
          outputs:
            - variable: report_success
              type: boolean
              source: "{{#generate_smart_report.body.success#}}"
            - variable: report_type
              type: text
              source: "{{#generate_smart_report.body.report_type#}}"
            - variable: data_source
              type: text
              source: "{{#generate_smart_report.body.data_source#}}"
            - variable: llm_enabled
              type: boolean
              source: "{{#generate_smart_report.body.llm_enabled#}}"
            - variable: report_content_length
              type: number
              source: "{{#generate_smart_report.body.report_content | length#}}"
            - variable: word_file_path
              type: text
              source: "{{#export_word.body.file_path#}}"
            - variable: word_file_size
              type: text
              source: "{{#export_word.body.file_size#}}"
            - variable: word_file_type
              type: text
              source: "{{#export_word.body.file_type#}}"
            - variable: final_result
              type: text
              source: |
                ## 🎉 智能容量报告生成完成！
                
                **报告信息**：
                - 报告类型：{{#generate_smart_report.body.report_type#}}
                - 数据来源：{{#generate_smart_report.body.data_source#}}
                - LLM分析：{{#generate_smart_report.body.llm_enabled#}}
                - 报告长度：{{#generate_smart_report.body.report_content | length#}} 字符
                
                **Word文档信息**：
                - 文件路径：{{#export_word.body.file_path#}}
                - 文件大小：{{#export_word.body.file_size#}}
                - 文件类型：{{#export_word.body.file_type#}}
                
                **使用提示**：
                1. 生成的是专业的Word文档(.docx格式)
                2. 包含LLM智能分析和风险评估
                3. 可以直接用Microsoft Word打开
                4. 包含执行摘要、详细分析和智能建议

    edges:
      - source: start
        target: generate_smart_report
      - source: generate_smart_report
        target: export_word
      - source: export_word
        target: end

  features:
    opening_statement: |
      欢迎使用智能容量报告生成器！
      
      本工具将：
      1. 🔍 自动获取系统容量数据
      2. 🧠 使用LLM进行智能分析
      3. 📊 生成专业的容量报告
      4. 📄 导出为Word文档格式
      
      请填写以下参数开始生成报告：
    
    suggested_questions:
      - "生成今日的生产环境容量报告"
      - "使用LLM分析生成智能容量报告"
      - "生成包含风险评估的容量报告"
      - "导出专业格式的Word容量报告"
    
    retriever_resource:
      enabled: false
    
    annotation_reply:
      enabled: false
    
    more_like_this:
      enabled: false
    
    user_input_form:
      - type: text-input
        variable: api_base_url
        label: "API服务地址"
        required: true
        default: "http://localhost:5000"
      - type: text-input
        variable: report_date
        label: "报告日期"
        required: true
        default: "2024-01-15"
      - type: text-input
        variable: system_name
        label: "系统名称"
        required: true
        default: "生产环境运维资源容量检查报告"
      - type: select
        variable: use_llm
        label: "分析模式"
        required: true
        default: "local"
        options:
          - label: "本地分析（快速）"
            value: "local"
          - label: "LLM增强分析（智能）"
            value: "llm"
      - type: text-input
        variable: llm_api_url
        label: "LLM API地址（可选）"
        required: false
        default: ""
      - type: text-input
        variable: llm_api_key
        label: "LLM API密钥（可选）"
        required: false
        default: ""
      - type: text-input
        variable: save_path
        label: "保存路径"
        required: true
        default: "./reports/"

environment_variables: []

model_config:
  provider: ""
  name: ""
  mode: ""
  completion_params: {}
