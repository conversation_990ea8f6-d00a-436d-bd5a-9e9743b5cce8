#!/usr/bin/env python3
"""
智能报告生成器
结合LLM分析结果生成专业的容量报告
"""

from datetime import datetime
from typing import Dict, List, Any
from llm_analyzer import CapacityAnalyzer

class SmartReportGenerator:
    """智能报告生成器"""
    
    def __init__(self, analyzer: CapacityAnalyzer = None):
        """
        初始化报告生成器
        
        Args:
            analyzer: 容量分析器实例
        """
        self.analyzer = analyzer or CapacityAnalyzer()
    
    def generate_smart_report(self, storage_data: Dict, database_data: Dict, 
                            container_data: Dict, virtualization_data: Dict,
                            report_date: str, system_name: str) -> str:
        """
        生成智能容量报告
        
        Args:
            storage_data: 存储容量数据
            database_data: 数据库容量数据
            container_data: 容器容量数据
            virtualization_data: 虚拟化容量数据
            report_date: 报告日期
            system_name: 系统名称
            
        Returns:
            Markdown格式的智能报告
        """
        
        # 1. 使用LLM分析器分析数据
        analysis_result = self.analyzer.analyze_capacity_data(
            storage_data, database_data, container_data, virtualization_data,
            report_date, system_name
        )
        
        # 2. 生成报告
        report = self._build_report_header(system_name, report_date, analysis_result)
        report += self._build_executive_summary(analysis_result)
        report += self._build_detailed_analysis(storage_data, database_data, container_data, virtualization_data, analysis_result)
        report += self._build_recommendations(analysis_result)
        report += self._build_appendix(analysis_result)
        
        return report
    
    def _build_report_header(self, system_name: str, report_date: str, analysis_result: Dict) -> str:
        """构建报告头部"""
        
        risk_level = analysis_result['risk_assessment']['overall_risk_level']
        risk_emoji = {
            'CRITICAL': '🔴',
            'HIGH': '🟡', 
            'MEDIUM': '🟠',
            'LOW': '🟢'
        }.get(risk_level, '🟢')
        
        risk_text = {
            'CRITICAL': '严重',
            'HIGH': '较高',
            'MEDIUM': '中等', 
            'LOW': '较低'
        }.get(risk_level, '较低')
        
        return f"""# {system_name}

**报告日期**: {report_date}  
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**数据来源**: API自动获取 + LLM智能分析  
**整体风险等级**: {risk_emoji} {risk_text}  

---

"""
    
    def _build_executive_summary(self, analysis_result: Dict) -> str:
        """构建执行摘要"""
        
        analysis_data = analysis_result['analysis_data']
        risk_assessment = analysis_result['risk_assessment']
        llm_insights = analysis_result['llm_insights']
        
        summary = """## 📊 执行摘要

### 整体状况
"""
        
        # 添加LLM洞察
        if llm_insights.get('success') and llm_insights.get('insights'):
            summary += f"""
**智能分析结果**：
{llm_insights['insights']}

"""
        
        # 添加关键指标
        summary += f"""
### 关键指标
- **存储资源**: {analysis_data['storage']['total_pools']}个存储池，总容量{analysis_data['storage']['total_capacity_tb']}TB，平均使用率{analysis_data['storage']['average_usage']}%
- **数据库资源**: {analysis_data['database']['total_instances']}个实例，总容量{analysis_data['database']['total_capacity_tb']}TB，平均使用率{analysis_data['database']['average_usage']}%
- **容器资源**: {analysis_data['container']['total_clusters']}个集群，总CPU{analysis_data['container']['total_cpu_cores']}核心，总内存{analysis_data['container']['total_memory_gb']}GB
- **虚拟化资源**: {analysis_data['virtualization']['total_clusters']}个集群，总CPU{analysis_data['virtualization']['total_cpu_cores']}核心，总内存{analysis_data['virtualization']['total_memory_gb']}GB

### 风险评估
- **风险等级**: {risk_assessment['overall_risk_level']}
- **风险评分**: {risk_assessment['risk_score']}/100
- **严重问题**: {len(risk_assessment['critical_issues'])}个
- **警告问题**: {len(risk_assessment['warning_issues'])}个

"""
        
        # 添加关键问题
        if risk_assessment['critical_issues']:
            summary += "### 🚨 严重问题\n"
            for issue in risk_assessment['critical_issues']:
                summary += f"- {issue}\n"
            summary += "\n"
        
        if risk_assessment['warning_issues']:
            summary += "### ⚠️ 警告问题\n"
            for issue in risk_assessment['warning_issues']:
                summary += f"- {issue}\n"
            summary += "\n"
        
        summary += "---\n\n"
        
        return summary
    
    def _build_detailed_analysis(self, storage_data: Dict, database_data: Dict, 
                               container_data: Dict, virtualization_data: Dict,
                               analysis_result: Dict) -> str:
        """构建详细分析"""
        
        detailed = "## 📋 详细分析\n\n"
        
        # 1. 存储资源分析
        detailed += self._build_storage_analysis(storage_data, analysis_result)
        
        # 2. 数据库资源分析
        detailed += self._build_database_analysis(database_data, analysis_result)
        
        # 3. 容器资源分析
        detailed += self._build_container_analysis(container_data, analysis_result)
        
        # 4. 虚拟化资源分析
        detailed += self._build_virtualization_analysis(virtualization_data, analysis_result)
        
        return detailed
    
    def _build_storage_analysis(self, storage_data: Dict, analysis_result: Dict) -> str:
        """构建存储分析"""
        
        analysis = """### 1. 存储资源容量及健康度排查

存储资源池本次排查情况如下：

| 序号 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|------|---------------|-------------|------------|-------------------|------------------------|----------|"""
        
        if storage_data and 'data' in storage_data:
            storage_pools = storage_data['data'].get('storage_pools', [])
            for i, pool in enumerate(storage_pools, 1):
                usage = pool.get('usage_rate', 0)
                change = pool.get('daily_change', 0)
                change_str = f"{change:+.2f}%" if change != 0 else "0%"
                
                # 智能判断状态和措施
                if usage >= 95:
                    status = "🔴 红色警告"
                    measure = "立即向安监部报备隐患，制定应急处置方案"
                elif usage >= 90:
                    status = "🟡 黄色观察"
                    measure = "向调度部报备，制定调整方案"
                elif usage >= 85:
                    status = "🟠 橙色关注"
                    measure = "加强监控，准备扩容计划"
                else:
                    status = "🟢 绿色正常"
                    measure = "无需措施"
                
                analysis += f"\n| {i} | {pool.get('pool_name', 'N/A')} | {pool.get('total_capacity_gb', 'N/A'):,} | {usage:.2f} | {change_str} | {status} | {measure} |"
        
        # 添加存储分析总结
        storage_analysis = analysis_result['analysis_data']['storage']
        analysis += f"""

**存储资源分析总结**：
- 存储池总数：{storage_analysis['total_pools']}个
- 总存储容量：{storage_analysis['total_capacity_tb']}TB
- 平均使用率：{storage_analysis['average_usage']}%
- 高使用率存储池：{len(storage_analysis['high_usage_pools'])}个
- 严重告警存储池：{len(storage_analysis['critical_pools'])}个

"""
        
        if storage_analysis['critical_pools']:
            analysis += "**严重告警存储池详情**：\n"
            for pool in storage_analysis['critical_pools']:
                analysis += f"- {pool['name']}：使用率{pool['usage']:.2f}%，容量{pool['capacity_gb']:,}GB\n"
            analysis += "\n"
        
        analysis += "---\n\n"
        
        return analysis
    
    def _build_database_analysis(self, database_data: Dict, analysis_result: Dict) -> str:
        """构建数据库分析"""
        
        analysis = """### 2. 数据库资源容量及健康度排查

数据库实例本次排查情况如下：

| 序号 | 数据库实例名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|------|---------------|-------------|------------|-------------------|------------------------|----------|"""
        
        if database_data and 'data' in database_data:
            db_instances = database_data['data'].get('database_instances', [])
            for i, db in enumerate(db_instances, 1):
                usage = db.get('usage_rate', 0)
                change = db.get('daily_change', 0)
                change_str = f"{change:+.2f}%" if change != 0 else "0%"
                
                # 智能判断状态和措施
                if usage >= 95:
                    status = "🔴 红色警告"
                    measure = "立即进行数据清理或扩容"
                elif usage >= 85:
                    status = "🟡 黄色观察"
                    measure = "制定数据库优化方案"
                elif usage >= 80:
                    status = "🟠 橙色关注"
                    measure = "加强监控，评估扩容需求"
                else:
                    status = "🟢 绿色正常"
                    measure = "无需措施"
                
                analysis += f"\n| {i} | {db.get('db_name', 'N/A')} | {db.get('total_capacity_gb', 'N/A'):,} | {usage:.2f} | {change_str} | {status} | {measure} |"
        
        # 添加数据库分析总结
        db_analysis = analysis_result['analysis_data']['database']
        analysis += f"""

**数据库资源分析总结**：
- 数据库实例总数：{db_analysis['total_instances']}个
- 总数据库容量：{db_analysis['total_capacity_tb']}TB
- 平均使用率：{db_analysis['average_usage']}%
- 高使用率实例：{len(db_analysis['high_usage_instances'])}个
- 严重告警实例：{len(db_analysis['critical_instances'])}个

"""
        
        if db_analysis['critical_instances']:
            analysis += "**严重告警数据库详情**：\n"
            for db in db_analysis['critical_instances']:
                analysis += f"- {db['name']}：使用率{db['usage']:.2f}%，容量{db['capacity_gb']:,}GB\n"
            analysis += "\n"
        
        analysis += "---\n\n"
        
        return analysis
    
    def _build_container_analysis(self, container_data: Dict, analysis_result: Dict) -> str:
        """构建容器分析"""
        
        analysis = """### 3. 容器资源容量及健康度排查

容器集群本次排查情况如下：

| 序号 | 集群名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 健康状态 | 对应措施 |
|------|----------|---------------|----------------|----------------|----------|----------|"""
        
        if container_data and 'data' in container_data:
            clusters = container_data['data'].get('container_clusters', [])
            for i, cluster in enumerate(clusters, 1):
                cpu_usage = cluster.get('cpu_usage_rate', 0)
                memory_usage = cluster.get('memory_usage_rate', 0)
                storage_usage = cluster.get('storage_usage_rate', 0)
                
                # 智能判断整体健康状态
                max_usage = max(cpu_usage, memory_usage, storage_usage)
                if max_usage >= 90:
                    status = "🔴 严重告警"
                    measure = "立即进行资源调优或扩容"
                elif max_usage >= 80:
                    status = "🟡 需要关注"
                    measure = "制定资源优化方案"
                elif max_usage >= 70:
                    status = "🟠 轻微告警"
                    measure = "加强监控"
                else:
                    status = "🟢 正常"
                    measure = "无需措施"
                
                analysis += f"\n| {i} | {cluster.get('cluster_name', 'N/A')} | {cpu_usage:.2f} | {memory_usage:.2f} | {storage_usage:.2f} | {status} | {measure} |"
        
        # 添加容器分析总结
        container_analysis = analysis_result['analysis_data']['container']
        analysis += f"""

**容器资源分析总结**：
- 容器集群总数：{container_analysis['total_clusters']}个
- 总CPU核心数：{container_analysis['total_cpu_cores']}核心
- 总内存容量：{container_analysis['total_memory_gb']}GB
- 总存储容量：{container_analysis['total_storage_tb']}TB
- CPU高使用率集群：{len(container_analysis['high_cpu_clusters'])}个
- 内存高使用率集群：{len(container_analysis['high_memory_clusters'])}个
- 存储高使用率集群：{len(container_analysis['high_storage_clusters'])}个

"""
        
        analysis += "---\n\n"
        
        return analysis
    
    def _build_virtualization_analysis(self, virtualization_data: Dict, analysis_result: Dict) -> str:
        """构建虚拟化分析"""
        
        analysis = """### 4. 虚拟化资源容量及健康度排查

虚拟化集群本次排查情况如下：

| 序号 | 集群名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 健康状态 | 对应措施 |
|------|----------|---------------|----------------|----------------|----------|----------|"""
        
        if virtualization_data and 'data' in virtualization_data:
            clusters = virtualization_data['data'].get('vm_clusters', [])
            for i, cluster in enumerate(clusters, 1):
                cpu_usage = cluster.get('cpu_usage_rate', 0)
                memory_usage = cluster.get('memory_usage_rate', 0)
                storage_usage = cluster.get('storage_usage_rate', 0)
                
                # 智能判断整体健康状态
                max_usage = max(cpu_usage, memory_usage, storage_usage)
                if max_usage >= 85:
                    status = "🔴 严重告警"
                    measure = "立即进行虚拟机迁移或扩容"
                elif max_usage >= 75:
                    status = "🟡 需要关注"
                    measure = "制定资源平衡方案"
                elif max_usage >= 65:
                    status = "🟠 轻微告警"
                    measure = "加强监控"
                else:
                    status = "🟢 正常"
                    measure = "无需措施"
                
                analysis += f"\n| {i} | {cluster.get('cluster_name', 'N/A')} | {cpu_usage:.2f} | {memory_usage:.2f} | {storage_usage:.2f} | {status} | {measure} |"
        
        # 添加虚拟化分析总结
        vm_analysis = analysis_result['analysis_data']['virtualization']
        analysis += f"""

**虚拟化资源分析总结**：
- 虚拟化集群总数：{vm_analysis['total_clusters']}个
- 总CPU核心数：{vm_analysis['total_cpu_cores']}核心
- 总内存容量：{vm_analysis['total_memory_gb']}GB
- 总存储容量：{vm_analysis['total_storage_tb']}TB
- CPU高使用率集群：{len(vm_analysis['high_cpu_clusters'])}个
- 内存高使用率集群：{len(vm_analysis['high_memory_clusters'])}个
- 存储高使用率集群：{len(vm_analysis['high_storage_clusters'])}个

"""
        
        analysis += "---\n\n"
        
        return analysis
    
    def _build_recommendations(self, analysis_result: Dict) -> str:
        """构建建议部分"""
        
        recommendations = analysis_result['recommendations']
        
        section = """## 💡 智能建议

基于LLM分析和专业经验，提出以下建议：

"""
        
        for i, rec in enumerate(recommendations, 1):
            section += f"{i}. {rec}\n"
        
        section += "\n---\n\n"
        
        return section
    
    def _build_appendix(self, analysis_result: Dict) -> str:
        """构建附录"""
        
        appendix = f"""## 📎 附录

### 分析方法说明
- **数据来源**: 通过API接口实时获取各系统容量数据
- **分析引擎**: 结合规则引擎和LLM智能分析
- **风险评估**: 基于行业最佳实践的多维度风险评估模型
- **建议生成**: 结合历史经验和智能算法生成个性化建议

### 健康度阈值标准
- **存储**: 绿色<85%, 黄色85-90%, 橙色90-95%, 红色>95%
- **数据库**: 绿色<80%, 黄色80-85%, 橙色85-95%, 红色>95%
- **容器**: CPU/内存绿色<70%, 黄色70-80%, 橙色80-90%, 红色>90%
- **虚拟化**: CPU/内存绿色<65%, 黄色65-75%, 橙色75-85%, 红色>85%

### 报告生成信息
- **生成时间**: {analysis_result['analysis_timestamp']}
- **分析引擎**: {analysis_result['llm_insights']['source']}
- **风险评分**: {analysis_result['risk_assessment']['risk_score']}/100
- **数据时效性**: 实时数据

---

*本报告由智能容量分析系统自动生成，结合了专业规则和LLM分析能力*
"""
        
        return appendix

# 创建全局智能报告生成器实例
smart_report_generator = SmartReportGenerator()
