app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 分析
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.7@8b9d2f57d314120744c245b6fe4f8701e1a7490a500d9fb74e9e9dceeaea5f70
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1742195614939-source-1742195618934-target
      source: '1742195614939'
      sourceHandle: source
      target: '1742195618934'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: end
      id: 1742195618934-source-1742195625349-target
      source: '1742195618934'
      sourceHandle: source
      target: '1742195625349'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: question
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: question
      height: 90
      id: '1742195614939'
      position:
        x: 79
        y: 282
      positionAbsolute:
        x: 79
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2.5-72B-Instruct-128K
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: d8020f79-c4d0-40cd-9197-85116c8fe253
          role: system
          text: '# 你会接收{{#1742195614939.question#}}中的段落内容。该内容包括”【用户的问题】、【流程】、【执行过程】“三个部分。

            # 你需要理解【用户的问题】，并分析【流程】、【执行过程】中的结果。

            # 你需要将这些内容整理成一份标准Markdown格式的报告，结构为：

            【任务名称】

            【事件解决过程】（包括总结性的概要和具体的操作步骤）

            【分析过程】（包括对每一步执行的过程分析，包含每一步的执行的结果）

            【总结】

            # 适当排版格式，结构清晰，内容严谨。

            # 你不能编造【用户的问题】、【流程】、【执行过程】。

            '
        selected: true
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1742195618934'
      position:
        x: 384
        y: 290
      positionAbsolute:
        x: 384
        y: 290
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1742195618934'
          - text
          variable: exec_return
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1742195625349'
      position:
        x: 687
        y: 282
      positionAbsolute:
        x: 687
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -58
      y: 10
      zoom: 1
