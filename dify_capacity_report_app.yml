app:
  description: '专业的IT容量报告生成器，参照报告模板格式生成存储、数据库、容器、虚拟化四部分内容的容量报告'
  icon: 📊
  icon_background: '#F3F4F6'
  mode: workflow
  name: 容量报告生成器
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: '欢迎使用容量报告生成器！请提供系统容量信息，我将参照标准模板为您生成专业的容量分析报告。'
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '如何填写各项容量信息？'
    - '报告会包含哪些分析内容？'
    - '生成的报告格式是什么样的？'
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        sourceType: start
        targetType: http-request
      id: start-http1
      source: start
      sourceHandle: source
      target: http-storage
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: http-request
      id: http1-http2
      source: http-storage
      sourceHandle: source
      target: http-database
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: http-request
      id: http2-http3
      source: http-database
      sourceHandle: source
      target: http-container
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: http-request
      id: http3-http4
      source: http-container
      sourceHandle: source
      target: http-virtualization
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: llm
      id: http4-llm
      source: http-virtualization
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: '输入容量报告的基本信息'
        selected: false
        title: 开始
        type: start
        variables:
        - label: 报告日期
          max_length: 50
          options: []
          required: true
          type: text-input
          variable: report_date
        - label: 系统名称
          max_length: 100
          options: []
          required: true
          type: text-input
          variable: system_name
        - label: API服务地址
          max_length: 200
          options: []
          required: false
          type: text-input
          variable: api_base_url
      height: 89
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '获取存储容量信息'
        selected: false
        title: 获取存储数据
        type: http-request
        authorization:
          config: null
          type: no-auth
        body:
          data: ''
          type: none
        headers: ''
        method: get
        timeout:
          connect: 30
          read: 60
          write: 60
        url: '{{#start.api_base_url#}}/api/storage'
      height: 89
      id: http-storage
      position:
        x: 400
        y: 200
      positionAbsolute:
        x: 400
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '获取数据库容量信息'
        selected: false
        title: 获取数据库数据
        type: http-request
        authorization:
          config: null
          type: no-auth
        body:
          data: ''
          type: none
        headers: ''
        method: get
        timeout:
          connect: 30
          read: 60
          write: 60
        url: '{{#start.api_base_url#}}/api/database'
      height: 89
      id: http-database
      position:
        x: 720
        y: 200
      positionAbsolute:
        x: 720
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '获取容器容量信息'
        selected: false
        title: 获取容器数据
        type: http-request
        authorization:
          config: null
          type: no-auth
        body:
          data: ''
          type: none
        headers: ''
        method: get
        timeout:
          connect: 30
          read: 60
          write: 60
        url: '{{#start.api_base_url#}}/api/container'
      height: 89
      id: http-container
      position:
        x: 1040
        y: 200
      positionAbsolute:
        x: 1040
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: '获取虚拟化容量信息'
        selected: false
        title: 获取虚拟化数据
        type: http-request
        authorization:
          config: null
          type: no-auth
        body:
          data: ''
          type: none
        headers: ''
        method: get
        timeout:
          connect: 30
          read: 60
          write: 60
        url: '{{#start.api_base_url#}}/api/virtualization'
      height: 89
      id: http-virtualization
      position:
        x: 1360
        y: 200
      positionAbsolute:
        x: 1360
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: '根据模板格式生成专业容量报告'
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: gpt-4
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。
            你需要参照标准的容量报告模板格式，生成包含存储、数据库、容器、虚拟化四个维度的详细容量分析报告。
            报告必须专业、准确，包含具体的数值分析、风险评估和优化建议。
        - id: user-prompt
          role: user
          text: |
            请根据以下API返回的数据生成一份专业的容量报告，严格按照指定格式：

            **基本信息：**
            - 报告日期：{{#start.report_date#}}
            - 系统名称：{{#start.system_name#}}

            **存储容量API数据：**
            {{#http-storage.body#}}

            **数据库容量API数据：**
            {{#http-database.body#}}

            **容器资源API数据：**
            {{#http-container.body#}}

            **虚拟化资源API数据：**
            {{#http-virtualization.body#}}

            请严格按照以下格式生成报告，并从API返回的JSON数据中提取相关信息：

            # {{#start.system_name#}}

            ## 1. 存储资源容量及健康度排查

            存储资源池本次排查情况如下：

            | 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|-------------|------------|-------------------|------------------------|----------|
            [从存储API数据的storage_pools数组中提取每个存储池的信息，包括pool_name、pool_id、total_capacity_gb、usage_rate、daily_change、has_anomaly、measures等字段]

            **健康度说明：**
            - 绿色：正常值 （存储使用率<90%）运行良好。
            - 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [根据API返回的存储数据分析各存储池状态，列出每个存储池的健康状态]

            **发现问题详情：** [根据API数据中的has_anomaly字段判断是否有问题，如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [根据API数据中的measures字段提供措施，如无问题则说明"不涉及"]

            ## 2. 数据库资源容量及健康度排查

            数据库资源池本次排查情况如下：

            | 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|-------------|------------|-------------------|------------------------|----------|
            [从数据库API数据的database_instances数组中提取每个数据库的信息，包括db_name、db_id、total_capacity_gb、usage_rate、daily_change、has_anomaly、measures等字段]

            **健康度说明：**
            - 绿色：正常值 （数据库使用率<85%）运行良好。
            - 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [根据API返回的数据库数据分析各数据库状态，列出每个数据库的健康状态]

            **发现问题详情：** [根据API数据中的has_anomaly字段判断是否有问题，如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [根据API数据中的measures字段提供措施，如无问题则说明"不涉及"]

            ## 3. 容器资源容量及健康度排查

            容器资源池本次排查情况如下：

            | 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [从容器API数据的container_clusters数组中提取每个集群的信息，包括cluster_name、cluster_id、cpu_usage_rate、memory_usage_rate、storage_usage_rate、cpu_daily_change、memory_daily_change、storage_daily_change、has_anomaly、measures等字段]

            **健康度说明：**
            - 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。
            - 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [根据API返回的容器数据分析各容器集群状态，列出每个集群的健康状态]

            **发现问题详情：** [根据API数据中的has_anomaly字段判断是否有问题，如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [根据API数据中的measures字段提供措施，如无问题则说明"不涉及"]

            ## 4. 虚拟化资源容量及健康度排查

            虚拟化资源池本次排查情况如下：

            | 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [从虚拟化API数据的vm_clusters数组中提取每个集群的信息，包括cluster_name、cluster_id、cpu_usage_rate、memory_usage_rate、storage_usage_rate、cpu_daily_change、memory_daily_change、storage_daily_change、has_anomaly、measures等字段]

            **健康度说明：**
            - 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。
            - 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [根据API返回的虚拟化数据分析各虚拟化集群状态，列出每个集群的健康状态]

            **发现问题详情：** [根据API数据中的has_anomaly字段判断是否有问题，如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [根据API数据中的measures字段提供措施，如无问题则说明"不涉及"]

            ---

            **要求：**
            1. 严格按照上述格式输出，不要添加额外的章节
            2. 表格数据必须根据输入信息准确填写
            3. 健康度评估要根据使用率阈值准确判断
            4. 状态描述要具体明确
            5. 如果某个维度信息不足，在表格中标注"信息不足"
            6. 输出格式为Markdown
            7. 变化情况可以用百分比表示，如"+2.1%"或"-0.72%"
        selected: false
        title: 容量报告生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: llm
      position:
        x: 1680
        y: 282
      positionAbsolute:
        x: 1680
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#llm.text#}}'
        desc: '输出生成的容量报告'
        selected: false
        title: 容量报告输出
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 2000
        y: 282
      positionAbsolute:
        x: 2000
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1
