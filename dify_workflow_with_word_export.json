{"version": "0.1.0", "kind": "workflow", "data": {"title": "容量报告生成器-Word导出版", "desc": "通过API获取容量数据，生成标准格式报告并保存为Word文件", "default_block_configs": {}, "graph": {"edges": [{"id": "start-llm", "source": "start", "target": "llm", "sourceHandle": "source", "targetHandle": "target"}, {"id": "llm-code", "source": "llm", "target": "word_export", "sourceHandle": "source", "targetHandle": "target"}, {"id": "code-end", "source": "word_export", "target": "end", "sourceHandle": "source", "targetHandle": "target"}], "nodes": [{"id": "start", "position": {"x": 80, "y": 282}, "type": "start", "data": {"title": "开始", "type": "start", "variables": [{"variable": "report_date", "label": "报告日期", "type": "text-input", "required": true, "max_length": 50}, {"variable": "system_name", "label": "系统名称", "type": "text-input", "required": true, "max_length": 100}, {"variable": "api_base_url", "label": "API服务地址", "type": "text-input", "required": true, "max_length": 200, "default": "http://localhost:5000"}, {"variable": "save_path", "label": "Word文件保存路径", "type": "text-input", "required": false, "max_length": 500, "default": "D:/work/LLM/reports/"}, {"variable": "storage_info", "label": "存储容量信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入存储容量信息"}, {"variable": "database_info", "label": "数据库容量信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入数据库容量信息"}, {"variable": "container_info", "label": "容器资源信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入容器资源信息"}, {"variable": "vm_info", "label": "虚拟化资源信息（如API不可用时使用）", "type": "paragraph", "required": false, "max_length": 3000, "default": "请先启动Flask API服务，或手动输入虚拟化资源信息"}]}}, {"id": "llm", "position": {"x": 400, "y": 282}, "type": "llm", "data": {"title": "容量报告生成", "type": "llm", "model": {"provider": "openai", "name": "gpt-4", "mode": "chat", "completion_params": {"temperature": 0.2}}, "prompt_template": [{"role": "system", "text": "你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。你需要首先尝试从API获取数据，如果API不可用则使用手动输入的数据，生成包含存储、数据库、容器、虚拟化四个维度的详细容量分析报告。"}, {"role": "user", "text": "请根据以下信息生成一份专业的容量报告：\n\n**基本信息：**\n- 报告日期：{{#start.report_date#}}\n- 系统名称：{{#start.system_name#}}\n- API服务地址：{{#start.api_base_url#}}\n\n**数据获取说明：**\n请首先尝试从以下API地址获取数据：\n- 存储数据：{{#start.api_base_url#}}/api/storage\n- 数据库数据：{{#start.api_base_url#}}/api/database\n- 容器数据：{{#start.api_base_url#}}/api/container\n- 虚拟化数据：{{#start.api_base_url#}}/api/virtualization\n\n如果API不可用，请使用以下手动输入的数据：\n\n**存储容量信息：**\n{{#start.storage_info#}}\n\n**数据库容量信息：**\n{{#start.database_info#}}\n\n**容器资源信息：**\n{{#start.container_info#}}\n\n**虚拟化资源信息：**\n{{#start.vm_info#}}\n\n请严格按照以下格式生成报告：\n\n# {{#start.system_name#}}\n\n## 1. 存储资源容量及健康度排查\n\n存储资源池本次排查情况如下：\n\n| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|---------------|-------------|------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （存储使用率<90%）运行良好。\n- 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各存储池状态]\n\n**发现问题详情：** [列出发现的问题，如无问题则说明\"今日未发现问题\"]\n\n**应对措施和预案：** [提供措施，如无问题则说明\"不涉及\"]\n\n## 2. 数据库资源容量及健康度排查\n\n数据库资源池本次排查情况如下：\n\n| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|-----------------|-------------|------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （数据库使用率<85%）运行良好。\n- 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各数据库状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n## 3. 容器资源容量及健康度排查\n\n容器资源池本次排查情况如下：\n\n| 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。\n- 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各容器集群状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n## 4. 虚拟化资源容量及健康度排查\n\n虚拟化资源池本次排查情况如下：\n\n| 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |\n|--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|\n[根据获取的数据填写表格]\n\n**健康度说明：**\n- 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。\n- 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。\n- 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。\n\n**今日状态：** [分析各虚拟化集群状态]\n\n**发现问题详情：** [列出发现的问题]\n\n**应对措施和预案：** [提供措施]\n\n---\n\n**要求：**\n1. 严格按照上述格式输出\n2. 优先尝试从API获取数据，如果无法获取则使用手动输入的数据\n3. 表格数据必须准确填写\n4. 健康度评估要根据使用率阈值准确判断\n5. 状态描述要具体明确\n6. 输出格式为Markdown\n7. 如果是从API获取的数据，请在报告开头说明\"数据来源：API自动获取\"\n8. 如果使用手动输入的数据，请说明\"数据来源：手动输入\""}], "vision": {"enabled": false}}}, {"id": "word_export", "position": {"x": 720, "y": 282}, "type": "code", "data": {"title": "导出Word文档", "type": "code", "code_language": "python3", "code": "import os\nimport re\nfrom datetime import datetime\nfrom docx import Document\nfrom docx.shared import Inches, Pt\nfrom docx.enum.text import WD_ALIGN_PARAGRAPH\nfrom docx.enum.table import WD_TABLE_ALIGNMENT\nfrom docx.oxml.ns import qn\nfrom docx.shared import RGBColor\n\ndef markdown_to_word(markdown_content, report_date, system_name, save_path):\n    \"\"\"\n    将Markdown格式的容量报告转换为Word文档\n    \"\"\"\n    # 创建Word文档\n    doc = Document()\n    \n    # 设置文档样式\n    style = doc.styles['Normal']\n    font = style.font\n    font.name = '微软雅黑'\n    font.size = Pt(10.5)\n    \n    # 添加标题\n    title = doc.add_heading(system_name, 0)\n    title.alignment = WD_ALIGN_PARAGRAPH.CENTER\n    \n    # 添加报告信息\n    info_para = doc.add_paragraph()\n    info_para.add_run(f\"报告日期：{report_date}\").bold = True\n    info_para.add_run(f\"\\n生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER\n    \n    # 分割内容为行\n    lines = markdown_content.split('\\n')\n    current_table = None\n    table_headers = []\n    \n    for line in lines:\n        line = line.strip()\n        if not line:\n            continue\n            \n        # 处理标题\n        if line.startswith('# '):\n            doc.add_heading(line[2:], 1)\n        elif line.startswith('## '):\n            doc.add_heading(line[3:], 2)\n        elif line.startswith('### '):\n            doc.add_heading(line[4:], 3)\n        \n        # 处理表格\n        elif line.startswith('|') and '|' in line:\n            cells = [cell.strip() for cell in line.split('|')[1:-1]]\n            \n            if not current_table:\n                # 创建新表格\n                table_headers = cells\n                current_table = doc.add_table(rows=1, cols=len(cells))\n                current_table.style = 'Table Grid'\n                current_table.alignment = WD_TABLE_ALIGNMENT.CENTER\n                \n                # 设置表头\n                header_row = current_table.rows[0]\n                for i, header in enumerate(cells):\n                    cell = header_row.cells[i]\n                    cell.text = header\n                    # 设置表头样式\n                    for paragraph in cell.paragraphs:\n                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER\n                        for run in paragraph.runs:\n                            run.font.bold = True\n                            run.font.size = Pt(9)\n            else:\n                # 检查是否是分隔行\n                if all(cell.startswith('-') for cell in cells):\n                    continue\n                \n                # 添加数据行\n                row = current_table.add_row()\n                for i, cell_text in enumerate(cells):\n                    if i < len(row.cells):\n                        cell = row.cells[i]\n                        cell.text = cell_text\n                        # 设置单元格样式\n                        for paragraph in cell.paragraphs:\n                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER\n                            for run in paragraph.runs:\n                                run.font.size = Pt(9)\n        else:\n            # 结束当前表格\n            if current_table:\n                current_table = None\n                table_headers = []\n            \n            # 处理普通段落\n            if line.startswith('**') and line.endswith('**'):\n                # 粗体段落\n                para = doc.add_paragraph()\n                para.add_run(line[2:-2]).bold = True\n            elif line.startswith('- '):\n                # 列表项\n                para = doc.add_paragraph(line[2:], style='List Bullet')\n            else:\n                # 普通段落\n                if line:\n                    doc.add_paragraph(line)\n    \n    # 确保保存路径存在\n    os.makedirs(save_path, exist_ok=True)\n    \n    # 生成文件名\n    safe_name = re.sub(r'[<>:\"/\\\\|?*]', '_', system_name)\n    filename = f\"{safe_name}_{report_date.replace('-', '')}.docx\"\n    filepath = os.path.join(save_path, filename)\n    \n    # 保存文档\n    doc.save(filepath)\n    \n    return filepath\n\n# 获取输入参数\nmarkdown_report = \"{{#llm.text#}}\"\nreport_date = \"{{#start.report_date#}}\"\nsystem_name = \"{{#start.system_name#}}\"\nsave_path = \"{{#start.save_path#}}\" or \"D:/work/LLM/reports/\"\n\ntry:\n    # 转换并保存Word文档\n    saved_file = markdown_to_word(markdown_report, report_date, system_name, save_path)\n    \n    result = {\n        \"success\": True,\n        \"message\": \"Word文档生成成功\",\n        \"file_path\": saved_file,\n        \"file_name\": os.path.basename(saved_file),\n        \"file_size\": f\"{os.path.getsize(saved_file) / 1024:.2f} KB\"\n    }\n    \nexcept Exception as e:\n    result = {\n        \"success\": False,\n        \"message\": f\"Word文档生成失败: {str(e)}\",\n        \"error\": str(e)\n    }\n\n# 返回结果\nreturn result", "outputs": {"result": {"type": "object"}}, "variables": [{"value_selector": ["llm", "text"], "variable": "markdown_report"}, {"value_selector": ["start", "report_date"], "variable": "report_date"}, {"value_selector": ["start", "system_name"], "variable": "system_name"}, {"value_selector": ["start", "save_path"], "variable": "save_path"}]}}, {"id": "end", "position": {"x": 1040, "y": 282}, "type": "end", "data": {"title": "结束", "type": "end", "outputs": [{"value_selector": ["llm", "text"], "variable": "capacity_report"}, {"value_selector": ["word_export", "result"], "variable": "word_export_result"}]}}], "viewport": {"x": 0, "y": 0, "zoom": 1}}, "features": {"opening_statement": "欢迎使用容量报告生成器！请提供API服务地址和基本信息，我将为您生成专业的容量分析报告并保存为Word文档。", "suggested_questions": ["如何启动Flask API服务？", "API服务地址应该怎么填写？", "Word文件保存在哪里？", "如果API不可用怎么办？"], "speech_to_text": {"enabled": false}, "text_to_speech": {"enabled": false}, "retrieval": {"enabled": false}, "annotation": {"enabled": false}}}}