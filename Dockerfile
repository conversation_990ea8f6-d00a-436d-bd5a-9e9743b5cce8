# 使用官方的 CentOS 镜像作为构建起点
FROM centos:latest

# 更新系统并安装必要的工具和库
RUN yum update -y && \
    yum groupinstall -y "Development Tools" && \
    yum install -y wget openssl-devel bzip2-devel libffi-devel zlib-devel

# 下载并安装 Python 3.11.11
WORKDIR /usr/src
RUN wget https://www.python.org/ftp/python/3.11.11/Python-3.11.11.tgz && \
    tar xzf Python-3.11.11.tgz && \
    cd Python-3.11.11 && \
    ./configure --enable-optimizations && \
    make altinstall # 使用altinstall而不是install以避免覆盖默认的python版本

# 清理不再需要的源文件以减小镜像大小
RUN rm -rf /usr/src/Python-3.11.11.tgz && \
    rm -rf /usr/src/Python-3.11.11

# 设置工作目录
WORKDIR /app

# 将当前目录下的所有内容复制到容器内的/app目录下
COPY . .

# 安装任何所需的 Python 包
RUN pip3.11 install --no-cache-dir -r requirements.txt

# 设置环境变量，确保python命令指向的是新安装的版本
ENV PATH="/usr/local/bin:${PATH}"

# 暴露端口（如果适用）
# EXPOSE 8080

# 指定容器启动时执行的命令
CMD ["python3.11", "app.py"]