app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: shell_exec
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.7@8b9d2f57d314120744c245b6fe4f8701e1a7490a500d9fb74e9e9dceeaea5f70
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1741931246746-source-1741931280871-target
      source: '1741931246746'
      sourceHandle: source
      target: '1741931280871'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: 1741931280871-source-1741931355199-target
      source: '1741931280871'
      sourceHandle: source
      target: '1741931355199'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: end
      id: 1741931355199-source-1742174841502-target
      source: '1741931355199'
      sourceHandle: source
      target: '1742174841502'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: question
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: question
      height: 90
      id: '1741931246746'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-R1
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 9fb49a92-0959-4074-9b41-2706dad90b5d
          role: system
          text: "```xml\n<instruction>\n    <instructions>\n        1. 仔细阅读用户提供的查询内容{{#1741931246746.question#}}，理解其需求。\n\
            \        2. 根据用户的需求生成仅包含bash指令的响应，确保指令可以直接在终端执行。\n        3. 生成的响应中不应包含任何解释或说明，仅提供指令。\n\
            \        4. 如果用户的查询涉及到路径不明确的文件或目录，首先使用find命令查找文件或目录的完整路径。\n        5.\
            \ 生成的bash指令应简洁明了，避免冗余。\n        6. 确保输出中不包含任何XML标签。\n        7. 严谨给出包含”删除“功能的bash指令\n\
            \    </instructions>\n    <examples>\n        <example>\n            Q:\
            \ 执行dir\n            A: ls\n        </example>\n        <example>\n  \
            \          Q: 进入XXX目录\n            A: cd XXX\n        </example>\n   \
            \     <example>\n            Q: 删除名为test.txt的文件\n            A: find /\
            \ -name test.txt -exec rm -f {} \\;\n        </example>\n    </examples>\n\
            </instruction>\n```"
        selected: true
        title: shell
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1741931280871'
      position:
        x: 459
        y: 282
      positionAbsolute:
        x: 459
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: 提取{{#1741931280871.text#}}中shell指令，并移除“sudo”
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2-VL-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: '提取shell指令

            '
          name: shell
          required: false
          type: string
        query:
        - '1741931280871'
        - text
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1741931355199'
      position:
        x: 768
        y: 282
      positionAbsolute:
        x: 768
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1741931355199'
          - shell
          variable: exec_return
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1742174841502'
      position:
        x: 1120.2038468913981
        y: 292.02298359173426
      positionAbsolute:
        x: 1120.2038468913981
        y: 292.02298359173426
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 169.73797121136062
      y: 16.35942807500436
      zoom: 0.7578582832551989
