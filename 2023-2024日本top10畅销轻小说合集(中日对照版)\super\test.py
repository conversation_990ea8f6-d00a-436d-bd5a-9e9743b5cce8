

import os
import re
import time
from pptx import Presentation
from pptx.util import Inches
import superflow



a=superflow.exec_command("top -d 5")
print(a)
def markdown_to_ppt(md_text):
    """将 Markdown 转换为 PowerPoint 幻灯片，并保存到 `pptx/` 目录"""

    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))  
    output_dir = os.path.join(script_dir, "pptx")  

    # 确保 `pptx` 目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 生成带时间戳的文件名
    timestamp = time.strftime("%Y%m%d%H%M%S")  
    filename = f"{timestamp}_slides.pptx"
    output_path = os.path.join(output_dir, filename)

    # 创建 PPTX
    prs = Presentation()
    lines = md_text.split("\n")

    slide = None
    is_code_block = False  
    code_block = ""  

    for line in lines:
        line = line.strip()

        # 处理代码块
        if line.startswith("```"):
            is_code_block = not is_code_block
            if not is_code_block and code_block:
                textbox = slide.shapes.add_textbox(Inches(1), Inches(2), Inches(8), Inches(5))
                text_frame = textbox.text_frame
                text_frame.text = code_block.strip()
                text_frame.word_wrap = True
                code_block = ""
            continue
        elif is_code_block:
            code_block += line + "\n"
            continue

        # 处理标题
        if re.match(r"^#{1,6} ", line):
            level = line.count("#")
            slide = prs.slides.add_slide(prs.slide_layouts[1])  # 标题+正文布局
            slide.shapes.title.text = line[level+1:].strip()  
            continue

        # 处理普通文本
        if slide:
            content_shape = slide.placeholders[1]
            content_shape.text += f"\n{line}" if content_shape.text else line

    # 保存文件
    prs.save(output_path)
    
    # 生成下载链接
    download_link = f'<a href="file://{output_path}" download>download_pptx</a>'
    print(f"Markdown 内容已转换为 {output_path}")
    print(f"下载链接：{download_link}")
    
    return output_path, download_link


md_text = """### 标题
分析系统资源使用情况：存储使用率、内存使用率、CPU使用率

### 事件解决过程
#### 概要
用户希望分析其系统过去5秒的存储使用率、内存使用率和CPU使用率，并生成综合报告。在执行过程中，遇到了`sudo`命令未找到的错误。通过手动执行相关命令，最终获取了所需的系统资源使用情况数据。

#### 具体操作步骤
1. 确保`sysstat`工具已安装以获取CPU使用率历史数据。
2. 使用`df -h`命令检查存储使用率。
3. 使用`free -m`命令检查内存使用情况。
4. 使用`sar -u 1 5`命令收集5秒内的CPU使用率数据。
5. 将三个指标的结果进行时间戳对齐。
6. 综合分析生成资源使用报告。

### 分析过程
#### 1. 确保`sysstat`工具已安装以获取CPU使用率历史数据
**执行命令：**
```sh
sudo apt-get install sysstat || sudo yum install sysstat || sudo dnf install sysstat
sudo systemctl enable sysstat
sudo systemctl start sysstat
sudo sed -i 's/ENABLED="false"/ENABLED="true"/' /etc/default/sysstat
sudo systemctl restart sysstat
```
**结果：**
```sh
Error Code: 2887
Error Message: /bin/sh: 1: sudo: not found
/bin/sh: 1: sudo: not found
/bin/sh: 1: sudo: not found
/bin/sh: 2: sudo: not found
/bin/sh: 3: not found
/bin/sh: 4: sudo: not found
/bin/sh: 5: sudo: not found
```
**分析：**
在执行这些命令时，系统提示`sudo`命令未找到。这通常意味着当前环境不支持`sudo`命令，或者`sudo`命令未安装。在这种情况下，可以手动以root用户身份执行这些命令，或者使用其他方法安装和配置`sysstat`工具。

#### 2. 使用`df -h`命令检查存储使用率
**执行命令：**
```sh
df -h
```
**结果：**
```sh
Filesystem      Size  Used Avail Use% Mounted on
overlay        1007G  124G  833G  13% /
tmpfs            64M     0   64M   0% /dev
tmpfs           7.8G     0  7.8G   0% /sys/fs/cgroup
shm              64M     0   64M   0% /dev/shm
/dev/sdd       1007G  124G  833G  13% /etc/hosts
```
**分析：**
- 根文件系统（`/`）的总大小为1007G，已使用124G，可用833G，使用率为13%。
- `/dev`、`/sys/fs/cgroup`和`/dev/shm`挂载点的使用率均为0%。
- `/etc/hosts`挂载点的使用情况与根文件系统相同，使用率为13%。

#### 3. 使用`free -m`命令检查内存使用情况
**执行命令：**
```sh
free -m
```
**结果：**
```sh
               total        used        free      shared  buff/cache   available
Mem:           15912        3096       12003          35        1172       12816
Swap:           4096           0        4096
```
**分析：**
- 总内存为15912MB，已使用3096MB，空闲12003MB，共享内存35MB，缓冲/缓存1172MB，实际可用内存12816MB。
- 交换分区总大小为4096MB，目前未使用。

#### 4. 使用`sar -u 1 5`命令收集5秒内的CPU使用率数据
**执行命令：**
```sh
sar -u 1 5
```
**分析：**
由于用户环境中没有安装`sysstat`工具，无法直接使用`sar`命令收集CPU使用率数据。可以使用`top`命令来获取近似的CPU使用率数据。

#### 5. 将三个指标的结果进行时间戳对齐
**执行命令：**
```sh
free -h; df -h; uptime; top -b -n 1 | head -n 12 | awk '/PID/{print;flag=1} flag&&++flag==13{exit}'
```
**结果：**
```sh
               total        used        free      shared  buff/cache   available
Mem:            15Gi       3.0Gi        11Gi        35Mi       1.1Gi        12Gi
Swap:          4.0Gi          0B       4.0Gi
Filesystem      Size  Used Avail Use% Mounted on
overlay        1007G  124G  833G  13% /
tmpfs            64M     0   64M   0% /dev
tmpfs           7.8G     0  7.8G   0% /sys/fs/cgroup
shm              64M     0   64M   0% /dev/shm
/dev/sdd       1007G  124G  833G  13% /etc/hosts
 03:33:55 up 21:58,  0 user,  load average: 0.24, 0.21, 0.18
  PID USER      PR  NI    VIRT    RES    SHR S  %CPU  %MEM     TIME+ COMMAND
  1 root      20   0  174904   6008   3884 S   0.0   0.0   0:01.99 systemd
  2 root      20   0       0      0      0 S   0.0   0.0   0:00.00 kthreadd
  3 root      20   0       0      0      0 S   0.0   0.0   0:00.03 ksoftirqd/0
  5 root       0 -20       0      0      0 S   0.0   0.0   0:00.00 kworker/0:0H
  7 root      20   0       0      0      0 S   0.0   0.0   0:00.82 rcu_sched
  8 root      20   0       0      0      0 S   0.0   0.0   0:00.00 rcu_bh
  9 root      rt   0       0      0      0 S   0.0   0.0   0:00.00 migration/0
  10 root      20   0       0      0      0 S   0.0   0.0   0:00.00 watchdog/0
  11 root      20   0       0      0      0 S   0.0   0.0   0:00.00 kdevtmpfs
  12 root      20   0       0      0      0 S   0.0   0.0   0:00.00 netns
  13 root      20   0       0      0      0 S   0.0   0.0   0:00.00 khungtaskd
```
**分析：**
- 内存使用情况与之前的`free -m`命令结果一致。
- 存储使用率与之前的`df -h`命令结果一致。
- 系统启动时间：21小时58分钟，当前用户数：0，负载平均值：0.24（1分钟），0.21（5分钟），0.18（15分钟）。
- `top`命令的输出显示了当前系统的进程列表，可以看到CPU使用率最高的进程是`systemd`，但其CPU使用率为0.0%。

### 总结
1. **存储使用率**：
   - 根文件系统（`/`）的使用率为13%，总大小为1007G，已使用124G，可用833G。
   - `/dev`、`/sys/fs/cgroup`和`/dev/shm`挂载点的使用率均为0%。
   - `/etc/hosts`挂载点的使用情况与根文件系统相同，使用率为13%。

2. **内存使用率**：
   - 总内存为15912MB，已使用3096MB，空闲12003MB，共享内存35MB，缓冲/缓存1172MB，实际可用内存12816MB。
   - 交换分区总大小为4096MB，目前未使用。

3. **CPU使用率**：
   - 由于`sysstat`工具未安装，无法使用`sar`命令获取过去5秒的CPU使用率数据。
   - 使用`top`命令获取的当前CPU使用率数据显示，系统负载平均值为0.24（1分钟），0.21（5分钟），0.18（15分钟），表明系统当前CPU使用率较低，没有明显的高负载进程。

**建议**：
- 如果需要定期监控CPU使用率，建议安装`sysstat`工具并启用`sysstat`服务。
- 继续监控存储和内存使用情况，确保系统资源充足，避免因资源不足导致的性能问题。
"""
