import os
import subprocess
from typing import Optional


def execute_command(command: str) -> str:
    """执行系统命令并返回输出
    
    :param command: 要执行的命令
    :return: 命令执行的输出
    """
    try:
        # 使用subprocess模块而不是os.system以获取命令输出
        result = subprocess.run(
            command,
            shell=True,
            check=False,
            capture_output=True,
            text=True
        )
        
        # 组合标准输出和错误输出
        output = result.stdout
        if result.stderr:
            if output:
                output += "\n\n错误输出:\n" + result.stderr
            else:
                output = "错误输出:\n" + result.stderr
                
        # 如果没有输出，添加状态信息
        if not output:
            output = f"命令执行完成，返回状态码: {result.returncode}"
            
        return output
    except Exception as e:
        return f"命令执行过程中出错: {str(e)}"
