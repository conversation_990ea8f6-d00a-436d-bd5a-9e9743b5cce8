#!/usr/bin/env python3
"""
测试智能报告生成功能
"""

import requests
import json
from datetime import datetime

def test_smart_report_api():
    """测试智能报告生成API"""
    
    base_url = "http://localhost:5000"
    
    print("🚀 开始测试智能报告生成功能...")
    print("=" * 60)
    
    # 1. 健康检查
    print("1. 检查API服务状态...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ API服务运行正常")
        else:
            print("❌ API服务异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return
    
    # 2. 测试传统报告生成
    print("\n2. 测试传统报告生成...")
    try:
        traditional_data = {
            "report_date": "2024-01-15",
            "system_name": "生产环境运维资源容量检查报告"
        }
        
        response = requests.post(
            f"{base_url}/api/generate_report",
            json=traditional_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 传统报告生成成功")
                print(f"   报告类型: {result.get('report_type', 'N/A')}")
                print(f"   数据来源: {result.get('data_source', 'N/A')}")
                print(f"   报告长度: {len(result.get('report_content', ''))} 字符")
            else:
                print(f"❌ 传统报告生成失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ 传统报告生成请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 传统报告生成异常: {e}")
    
    # 3. 测试智能报告生成（本地分析）
    print("\n3. 测试智能报告生成（本地分析）...")
    try:
        smart_data = {
            "report_date": "2024-01-15",
            "system_name": "生产环境运维资源容量检查报告（智能版）"
        }
        
        response = requests.post(
            f"{base_url}/api/generate_smart_report",
            json=smart_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 智能报告生成成功")
                print(f"   报告类型: {result.get('report_type', 'N/A')}")
                print(f"   数据来源: {result.get('data_source', 'N/A')}")
                print(f"   LLM启用: {result.get('llm_enabled', False)}")
                print(f"   报告长度: {len(result.get('report_content', ''))} 字符")
                
                # 保存智能报告内容到文件
                with open('智能报告示例.md', 'w', encoding='utf-8') as f:
                    f.write(result.get('report_content', ''))
                print("   📄 智能报告已保存到: 智能报告示例.md")
                
            else:
                print(f"❌ 智能报告生成失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ 智能报告生成请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 智能报告生成异常: {e}")
    
    # 4. 测试智能报告生成（模拟LLM配置）
    print("\n4. 测试智能报告生成（模拟LLM配置）...")
    try:
        smart_data_with_llm = {
            "report_date": "2024-01-15",
            "system_name": "生产环境运维资源容量检查报告（LLM增强版）",
            "llm_config": {
                "api_url": "https://api.openai.com/v1/chat/completions",
                "api_key": "sk-test-key-placeholder"  # 测试用的占位符
            }
        }
        
        response = requests.post(
            f"{base_url}/api/generate_smart_report",
            json=smart_data_with_llm,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ LLM增强智能报告生成成功")
                print(f"   报告类型: {result.get('report_type', 'N/A')}")
                print(f"   数据来源: {result.get('data_source', 'N/A')}")
                print(f"   LLM启用: {result.get('llm_enabled', False)}")
                print(f"   报告长度: {len(result.get('report_content', ''))} 字符")
            else:
                print(f"❌ LLM增强智能报告生成失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ LLM增强智能报告生成请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ LLM增强智能报告生成异常: {e}")
    
    # 5. 测试完整工作流（智能报告 + Word导出）
    print("\n5. 测试完整工作流（智能报告 + Word导出）...")
    try:
        # 先生成智能报告
        smart_data = {
            "report_date": "2024-01-15",
            "system_name": "生产环境运维资源容量检查报告（智能版）"
        }
        
        response = requests.post(
            f"{base_url}/api/generate_smart_report",
            json=smart_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 智能报告生成成功")
                
                # 导出为Word文档
                export_data = {
                    "report_content": result.get('report_content', ''),
                    "report_date": result.get('report_date', ''),
                    "system_name": result.get('system_name', ''),
                    "save_path": "./reports/"
                }
                
                export_response = requests.post(
                    f"{base_url}/api/export_word",
                    json=export_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if export_response.status_code == 200:
                    export_result = export_response.json()
                    if export_result.get('success'):
                        print("✅ Word文档导出成功")
                        print(f"   文件路径: {export_result.get('file_path', 'N/A')}")
                        print(f"   文件大小: {export_result.get('file_size', 'N/A')}")
                        print(f"   文件类型: {export_result.get('file_type', 'N/A')}")
                    else:
                        print(f"❌ Word文档导出失败: {export_result.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Word文档导出请求失败: {export_response.status_code}")
            else:
                print(f"❌ 智能报告生成失败: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ 智能报告生成请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 完整工作流测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 智能报告生成功能测试完成！")

def test_capacity_apis():
    """测试各个容量数据API"""
    
    base_url = "http://localhost:5000"
    apis = [
        ("/api/storage", "存储容量"),
        ("/api/database", "数据库容量"),
        ("/api/container", "容器容量"),
        ("/api/virtualization", "虚拟化容量")
    ]
    
    print("\n📊 测试容量数据API...")
    print("-" * 40)
    
    for endpoint, name in apis:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    print(f"✅ {name}API正常")
                    # 显示数据统计
                    if 'data' in data:
                        data_section = data['data']
                        if 'storage_pools' in data_section:
                            print(f"   存储池数量: {len(data_section['storage_pools'])}")
                        elif 'database_instances' in data_section:
                            print(f"   数据库实例数量: {len(data_section['database_instances'])}")
                        elif 'container_clusters' in data_section:
                            print(f"   容器集群数量: {len(data_section['container_clusters'])}")
                        elif 'vm_clusters' in data_section:
                            print(f"   虚拟化集群数量: {len(data_section['vm_clusters'])}")
                else:
                    print(f"❌ {name}API返回错误状态")
            else:
                print(f"❌ {name}API请求失败: {response.status_code}")
        except Exception as e:
            print(f"❌ {name}API异常: {e}")

def compare_reports():
    """对比传统报告和智能报告的差异"""
    
    print("\n🔍 对比传统报告和智能报告...")
    print("-" * 50)
    
    try:
        # 读取智能报告
        with open('智能报告示例.md', 'r', encoding='utf-8') as f:
            smart_content = f.read()
        
        print("📋 智能报告特点分析:")
        
        # 分析报告结构
        sections = smart_content.split('\n## ')
        print(f"   报告章节数量: {len(sections)}")
        
        # 查找关键特性
        features = []
        if '执行摘要' in smart_content:
            features.append("✅ 包含执行摘要")
        if '智能分析结果' in smart_content:
            features.append("✅ 包含智能分析")
        if '风险评估' in smart_content:
            features.append("✅ 包含风险评估")
        if '智能建议' in smart_content:
            features.append("✅ 包含智能建议")
        if '🔴' in smart_content or '🟡' in smart_content or '🟢' in smart_content:
            features.append("✅ 包含状态图标")
        if '附录' in smart_content:
            features.append("✅ 包含技术附录")
        
        for feature in features:
            print(f"   {feature}")
        
        print(f"   报告总长度: {len(smart_content)} 字符")
        print(f"   预估阅读时间: {len(smart_content) // 500} 分钟")
        
    except FileNotFoundError:
        print("❌ 未找到智能报告文件，请先运行智能报告生成测试")
    except Exception as e:
        print(f"❌ 报告对比异常: {e}")

if __name__ == "__main__":
    # 运行所有测试
    test_capacity_apis()
    test_smart_report_api()
    compare_reports()
    
    print("\n" + "=" * 60)
    print("📝 测试总结:")
    print("1. 智能报告生成功能已实现")
    print("2. 支持本地分析和LLM增强分析")
    print("3. 可以与现有Word导出功能无缝集成")
    print("4. 提供更丰富的分析内容和专业建议")
    print("5. 包含风险评估、趋势分析和智能洞察")
    print("=" * 60)
