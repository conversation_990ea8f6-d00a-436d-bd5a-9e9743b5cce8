# Dify YAML工作流导入指南

## 📁 可用的YAML配置文件

我已经为您创建了3个不同复杂度的YAML配置文件：

### 1. `智能容量报告工作流.yml` ⭐⭐⭐ (推荐)
- **特点**：完整功能，中文注释，易于理解
- **适用**：生产环境使用
- **包含**：完整的节点配置、用户界面、错误处理

### 2. `dify_simple_workflow.yml` ⭐⭐
- **特点**：简化版本，核心功能
- **适用**：快速测试和验证
- **包含**：基本的工作流节点和连接

### 3. `dify_smart_workflow_v2.yml` ⭐
- **特点**：完整详细版本，包含所有配置项
- **适用**：需要完全自定义的场景
- **包含**：所有可能的配置参数

## 🚀 导入步骤

### 方法1：直接导入YAML文件

1. **登录Dify平台**
   - 访问：http://************:980
   - 使用您的账号登录

2. **创建新应用**
   - 点击"创建应用"或"Import App"
   - 选择"从文件导入"或"Import from YAML"

3. **上传YAML文件**
   - 选择 `智能容量报告工作流.yml` 文件
   - 点击"导入"或"Import"

4. **验证配置**
   - 检查API地址是否正确：`http://************:5000`
   - 确认所有节点连接正常

### 方法2：复制粘贴内容

如果直接文件导入不工作：

1. **打开YAML文件**
   - 用文本编辑器打开 `智能容量报告工作流.yml`
   - 复制全部内容

2. **在Dify中粘贴**
   - 选择"从YAML导入"
   - 粘贴复制的内容
   - 点击"导入"

## ⚙️ 配置参数说明

### 必填参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `api_base_url` | `http://************:5000` | Flask API服务地址 |
| `report_date` | `2024-01-15` | 报告日期（YYYY-MM-DD格式） |
| `system_name` | `生产环境运维资源容量检查报告` | 系统名称 |
| `enable_llm` | `false` | 是否启用LLM分析 |

### 可选参数

- **LLM配置**：如果启用LLM分析，需要配置相应的API
- **保存路径**：Word文档保存位置（默认：`./reports/`）

## 🔧 网络配置检查

### 1. 确认API服务运行状态

```bash
# 在您的服务器上检查
curl http://localhost:5000/api/health
# 应该返回：{"status": "healthy", "message": "API服务运行正常"}
```

### 2. 确认网络连通性

从Dify服务器测试：
```bash
# 测试连接
curl http://************:5000/api/health

# 或者使用telnet测试端口
telnet ************ 5000
```

### 3. API地址配置

根据您的网络环境选择正确的API地址：

- **Dify和Flask在同一服务器**：`http://localhost:5000`
- **Dify在************，Flask在您的服务器**：`http://************:5000`
- **如果有防火墙**：确保5000端口开放

## 🧪 测试工作流

### 1. 导入成功后测试

1. **填写参数**：
   - API服务地址：`http://************:5000`
   - 报告日期：`2024-01-15`
   - 系统名称：`测试报告`
   - 启用LLM分析：`禁用（使用本地分析）`

2. **运行工作流**：
   - 点击"运行"或"Execute"
   - 观察每个节点的执行状态

3. **检查结果**：
   - 查看生成的报告内容
   - 确认Word文档导出成功
   - 检查文件路径和大小信息

### 2. 预期输出

成功执行后应该看到：

```yaml
outputs:
  report_generated: true
  report_type: "smart"
  data_source: "API自动获取 + LLM智能分析"
  llm_analysis_enabled: false
  word_document_path: "./reports/测试报告_20240115.docx"
  word_document_size: "38.79 KB"
  export_success: true
```

## 🚨 常见问题解决

### 问题1：YAML格式错误

**症状**：导入时提示格式错误
**解决**：
- 检查YAML文件的缩进（使用空格，不要用Tab）
- 确保所有引号配对正确
- 使用在线YAML验证器检查格式

### 问题2：网络连接失败

**症状**：HTTP请求节点执行失败
**解决**：
- 检查API服务是否运行：`python app.py`
- 测试网络连通性：`curl http://************:5000/api/health`
- 检查防火墙设置

### 问题3：变量引用错误

**症状**：节点间数据传递失败
**解决**：
- 确认变量引用语法：`{{#节点名.字段名#}}`
- 检查节点ID和字段名是否正确
- 查看节点执行日志

### 问题4：超时错误

**症状**：请求超时失败
**解决**：
- 增加超时时间（智能报告生成建议60秒）
- 检查API响应时间
- 优化网络连接

## 📋 导入检查清单

导入YAML文件前，请确认：

- [ ] Flask API服务正在运行
- [ ] 网络连接正常（能访问API健康检查接口）
- [ ] YAML文件格式正确
- [ ] API地址配置正确
- [ ] 防火墙端口开放（5000端口）

导入后，请验证：

- [ ] 所有节点显示正常
- [ ] 节点连接关系正确
- [ ] 输入参数配置正确
- [ ] 能够成功执行测试

## 💡 优化建议

1. **首次使用**：建议使用 `智能容量报告工作流.yml`
2. **测试环境**：可以使用 `dify_simple_workflow.yml`
3. **生产环境**：使用完整版配置并根据需要调整参数
4. **网络优化**：如果经常超时，考虑增加超时时间或优化网络

## 📞 技术支持

如果导入过程中遇到问题：

1. **检查日志**：查看Dify平台的错误日志
2. **验证API**：使用 `test_dify_connection.py` 脚本测试
3. **网络诊断**：确认网络连接和防火墙设置
4. **格式验证**：使用YAML验证工具检查文件格式

---

*智能容量报告系统 - 让容量管理更智能！*
