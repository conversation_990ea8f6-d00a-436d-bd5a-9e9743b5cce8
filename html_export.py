#!/usr/bin/env python3
"""
HTML导出工具模块
用于将Markdown格式的容量报告转换为HTML文档（可用Word打开）
"""

import os
import re
from datetime import datetime

def export_to_html(markdown_content, report_date, system_name, save_path):
    """将Markdown内容导出为HTML格式"""
    
    # 确保保存路径存在
    if not save_path or save_path.strip() == "":
        save_path = "D:/work/LLM/reports/"
    
    os.makedirs(save_path, exist_ok=True)
    
    # 生成文件名
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
    filename = f"{safe_name}_{report_date.replace('-', '')}.html"
    filepath = os.path.join(save_path, filename)
    
    # HTML模板
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{system_name}</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            font-size: 12pt;
        }}
        h1 {{
            text-align: center;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 18pt;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
            font-size: 14pt;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10pt;
        }}
        th, td {{
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }}
        th {{
            background-color: #f0f0f0;
            font-weight: bold;
            color: #2c3e50;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .report-header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }}
        .health-section {{
            background-color: #e8f5e8;
            padding: 10px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
        }}
        .status-section {{
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }}
        p {{
            margin: 10px 0;
        }}
        ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
    </style>
</head>
<body>
    <div class="report-header">
        <h1>{system_name}</h1>
        <p><strong>报告日期：</strong>{report_date}</p>
        <p><strong>生成时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
"""
    
    # 转换Markdown内容为HTML
    lines = markdown_content.split('\n')
    in_table = False
    table_html = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 处理标题
        if line.startswith('# '):
            continue  # 跳过主标题，已在header中处理
        elif line.startswith('## '):
            html_content += f"<h2>{line[3:]}</h2>\n"
        elif line.startswith('### '):
            html_content += f"<h3>{line[4:]}</h3>\n"
        
        # 处理表格
        elif line.startswith('|') and '|' in line:
            if not in_table:
                in_table = True
                table_html = "<table>\n"
            
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            
            # 检查是否是分隔行
            if all(cell.startswith('-') for cell in cells):
                continue
            
            # 判断是否是表头
            if '资源池' in line:
                table_html += "<tr>"
                for cell in cells:
                    table_html += f"<th>{cell}</th>"
                table_html += "</tr>\n"
            else:
                table_html += "<tr>"
                for cell in cells:
                    table_html += f"<td>{cell}</td>"
                table_html += "</tr>\n"
        else:
            # 结束表格
            if in_table:
                table_html += "</table>\n"
                html_content += table_html
                in_table = False
                table_html = ""
            
            # 处理其他内容
            if line.startswith('**') and line.endswith('**'):
                if '健康度说明' in line:
                    html_content += f'<div class="health-section"><strong>{line[2:-2]}</strong></div>\n'
                elif '今日状态' in line or '发现问题详情' in line or '应对措施和预案' in line:
                    html_content += f'<div class="status-section"><strong>{line[2:-2]}</strong></div>\n'
                else:
                    html_content += f"<p><strong>{line[2:-2]}</strong></p>\n"
            elif line.startswith('- '):
                html_content += f"<ul><li>{line[2:]}</li></ul>\n"
            elif line.startswith('数据来源：'):
                html_content += f'<p><strong>{line}</strong></p>\n'
            else:
                if line and not line.startswith('---'):
                    html_content += f"<p>{line}</p>\n"
    
    # 结束最后的表格
    if in_table:
        table_html += "</table>\n"
        html_content += table_html
    
    html_content += """
</body>
</html>
"""
    
    # 保存HTML文件
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return filepath
