# 智能容量报告生成器 - Dify工作流配置
# 版本: 1.0
# 创建日期: 2025-06-27
# 适用于: Dify平台工作流导入

version: "0.1.0"
kind: app

data:
  # 应用基本信息
  title: "智能容量报告生成器"
  description: "通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告"
  icon: "📊"
  icon_background: "#3B82F6"
  mode: "workflow"
  
  # 工作流定义
  workflow:
    graph:
      # 节点定义
      nodes:
        # 开始节点
        - id: "start"
          data:
            type: "start"
            title: "开始"
            variables:
              - variable: "api_base_url"
                type: "text-input"
                label: "API服务地址"
                required: true
                default: "http://************:5000"
                description: "Flask API服务的基础URL地址"
              
              - variable: "report_date"
                type: "text-input"
                label: "报告日期"
                required: true
                default: "2024-01-15"
                description: "容量报告的日期，格式：YYYY-MM-DD"
              
              - variable: "system_name"
                type: "text-input"
                label: "系统名称"
                required: true
                default: "生产环境运维资源容量检查报告"
                description: "容量报告的系统名称"
              
              - variable: "enable_llm"
                type: "select"
                label: "启用LLM分析"
                required: true
                default: "false"
                options:
                  - label: "启用（需要配置LLM）"
                    value: "true"
                  - label: "禁用（使用本地分析）"
                    value: "false"
                description: "是否启用LLM增强分析"
          
          position:
            x: 100
            y: 200
        
        # HTTP请求节点1 - 生成智能报告
        - id: "generate_smart_report"
          data:
            type: "http-request"
            title: "生成智能报告"
            method: "POST"
            url: "{{#start.api_base_url#}}/api/generate_smart_report"
            headers:
              "Content-Type": "application/json"
            body:
              type: "json"
              data: |
                {
                  "report_date": "{{#start.report_date#}}",
                  "system_name": "{{#start.system_name#}}",
                  "llm_config": {
                    "enabled": {{#start.enable_llm#}}
                  }
                }
            timeout:
              connect: 10
              read: 60
              write: 10
          
          position:
            x: 400
            y: 200
        
        # HTTP请求节点2 - 导出Word文档
        - id: "export_word_document"
          data:
            type: "http-request"
            title: "导出Word文档"
            method: "POST"
            url: "{{#start.api_base_url#}}/api/export_word"
            headers:
              "Content-Type": "application/json"
            body:
              type: "json"
              data: |
                {
                  "report_content": "{{#generate_smart_report.body.report_content#}}",
                  "report_date": "{{#generate_smart_report.body.report_date#}}",
                  "system_name": "{{#generate_smart_report.body.system_name#}}",
                  "save_path": "./reports/"
                }
            timeout:
              connect: 10
              read: 30
              write: 10
          
          position:
            x: 700
            y: 200
        
        # 结束节点
        - id: "end"
          data:
            type: "end"
            title: "完成"
            outputs:
              - variable: "report_generated"
                type: "boolean"
                value_selector: ["generate_smart_report", "body", "success"]
              
              - variable: "report_type"
                type: "string"
                value_selector: ["generate_smart_report", "body", "report_type"]
              
              - variable: "data_source"
                type: "string"
                value_selector: ["generate_smart_report", "body", "data_source"]
              
              - variable: "llm_analysis_enabled"
                type: "boolean"
                value_selector: ["generate_smart_report", "body", "llm_enabled"]
              
              - variable: "word_document_path"
                type: "string"
                value_selector: ["export_word_document", "body", "file_path"]
              
              - variable: "word_document_size"
                type: "string"
                value_selector: ["export_word_document", "body", "file_size"]
              
              - variable: "export_success"
                type: "boolean"
                value_selector: ["export_word_document", "body", "success"]
          
          position:
            x: 1000
            y: 200
      
      # 连接关系
      edges:
        - source: "start"
          target: "generate_smart_report"
        
        - source: "generate_smart_report"
          target: "export_word_document"
        
        - source: "export_word_document"
          target: "end"
  
  # 模型配置
  model_config:
    # 开场白
    opening_statement: |
      🎉 欢迎使用智能容量报告生成器！
      
      ✨ 功能特点：
      🔍 自动获取系统容量数据（存储、数据库、容器、虚拟化）
      🧠 智能分析和风险评估
      📊 生成专业的容量分析报告
      📄 导出为Word文档格式
      
      📋 使用说明：
      1. 填写API服务地址（默认已配置）
      2. 设置报告日期和系统名称
      3. 选择是否启用LLM分析
      4. 点击运行开始生成报告
      
      ⚡ 预计处理时间：1-2分钟
    
    # 建议问题
    suggested_questions:
      - "生成今日的生产环境容量报告"
      - "使用本地分析生成容量报告"
      - "生成包含风险评估的智能报告"
      - "导出专业格式的Word容量报告"
    
    # 用户输入表单
    user_input_form:
      - text-input:
          variable: "api_base_url"
          label: "API服务地址"
          default: "http://************:5000"
          max_length: 256
          required: true
      
      - text-input:
          variable: "report_date"
          label: "报告日期"
          default: "2024-01-15"
          max_length: 256
          required: true
      
      - text-input:
          variable: "system_name"
          label: "系统名称"
          default: "生产环境运维资源容量检查报告"
          max_length: 256
          required: true
      
      - select:
          variable: "enable_llm"
          label: "启用LLM分析"
          default: "false"
          required: true
          options:
            - label: "启用（需要配置LLM）"
              value: "true"
            - label: "禁用（使用本地分析）"
              value: "false"
