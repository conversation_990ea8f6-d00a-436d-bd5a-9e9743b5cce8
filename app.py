from flask import Flask, jsonify, request, Response
from datetime import datetime, timedelta
import random
import os
import json
from html_export import export_to_word

app = Flask(__name__)
# 配置JSON响应不转义中文字符
app.config['JSON_AS_ASCII'] = False

def json_response(data, status_code=200):
    """自定义JSON响应函数，确保中文字符正确显示"""
    response = Response(
        json.dumps(data, ensure_ascii=False, indent=2),
        status=status_code,
        mimetype='application/json; charset=utf-8'
    )
    return response

# 模拟数据生成函数
def generate_random_change():
    """生成随机的日变化百分比"""
    return round(random.uniform(-3.0, 3.0), 2)

def calculate_usage_rate(total, used):
    """计算使用率"""
    return round((used / total) * 100, 2)

def get_health_status(usage_rate, thresholds):
    """根据使用率获取健康状态"""
    if usage_rate < thresholds['green']:
        return "正常"
    elif usage_rate < thresholds['yellow']:
        return "观察"
    else:
        return "警告"

@app.route('/api/storage', methods=['GET'])
def get_storage_capacity():
    """获取存储容量信息"""
    storage_pools = [
        {
            "pool_name": "嘉兴中端虚拟化存储池",
            "pool_id": "JX-M-VM-Prod",
            "total_capacity_gb": 769034,
            "used_capacity_gb": 685234,
            "available_capacity_gb": 83800
        },
        {
            "pool_name": "后沙峪中端虚拟化存储池",
            "pool_id": "HSY-M-VM-Prod",
            "total_capacity_gb": 5200518,
            "used_capacity_gb": 3496748,
            "available_capacity_gb": 1703770
        },
        {
            "pool_name": "嘉兴中端数据库存储池",
            "pool_id": "JX-M-DB-Prod",
            "total_capacity_gb": 822628,
            "used_capacity_gb": 291539,
            "available_capacity_gb": 531089
        },
        {
            "pool_name": "后沙峪中端数据库存储池",
            "pool_id": "HSY-M-DB-Prod",
            "total_capacity_gb": 2327194,
            "used_capacity_gb": 838188,
            "available_capacity_gb": 1489006
        },
        {
            "pool_name": "后沙峪高端存储池",
            "pool_id": "HSY-H-Prod",
            "total_capacity_gb": 760877,
            "used_capacity_gb": 248246,
            "available_capacity_gb": 512631
        }
    ]
    
    # 添加计算字段
    for pool in storage_pools:
        pool['usage_rate'] = calculate_usage_rate(pool['total_capacity_gb'], pool['used_capacity_gb'])
        pool['daily_change'] = generate_random_change()
        pool['health_status'] = get_health_status(pool['usage_rate'], {'green': 90, 'yellow': 95})
        pool['has_anomaly'] = pool['health_status'] != "正常"
        pool['measures'] = "无需措施" if not pool['has_anomaly'] else "需要关注并制定调整方案"
    
    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "storage_pools": storage_pools,
            "summary": {
                "total_pools": len(storage_pools),
                "normal_pools": len([p for p in storage_pools if p['health_status'] == "正常"]),
                "warning_pools": len([p for p in storage_pools if p['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/database', methods=['GET'])
def get_database_capacity():
    """获取数据库容量信息"""
    database_instances = [
        {
            "db_name": "嘉兴Oracle生产库",
            "db_id": "JX-ORA-PROD01",
            "total_capacity_gb": 8192000,
            "used_capacity_gb": 6348800,
            "available_capacity_gb": 1843200
        },
        {
            "db_name": "嘉兴Oracle生产库",
            "db_id": "JX-ORA-PROD02",
            "total_capacity_gb": 6144000,
            "used_capacity_gb": 4196352,
            "available_capacity_gb": 1947648
        },
        {
            "db_name": "后沙峪MySQL集群主库",
            "db_id": "HSY-MYSQL-MASTER",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "后沙峪MySQL集群从库1",
            "db_id": "HSY-MYSQL-SLAVE1",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "后沙峪MySQL集群从库2",
            "db_id": "HSY-MYSQL-SLAVE2",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "嘉兴PostgreSQL业务库",
            "db_id": "JX-PG-BIZ",
            "total_capacity_gb": 2048000,
            "used_capacity_gb": 1331200,
            "available_capacity_gb": 716800
        },
        {
            "db_name": "后沙峪PostgreSQL分析库",
            "db_id": "HSY-PG-ANALYSIS",
            "total_capacity_gb": 5120000,
            "used_capacity_gb": 2560000,
            "available_capacity_gb": 2560000
        }
    ]
    
    # 添加计算字段
    for db in database_instances:
        db['usage_rate'] = calculate_usage_rate(db['total_capacity_gb'], db['used_capacity_gb'])
        db['daily_change'] = generate_random_change()
        db['health_status'] = get_health_status(db['usage_rate'], {'green': 85, 'yellow': 95})
        db['has_anomaly'] = db['health_status'] != "正常"
        db['measures'] = "无需措施" if not db['has_anomaly'] else "需要关注并制定调整方案"
    
    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "database_instances": database_instances,
            "summary": {
                "total_instances": len(database_instances),
                "normal_instances": len([db for db in database_instances if db['health_status'] == "正常"]),
                "warning_instances": len([db for db in database_instances if db['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/container', methods=['GET'])
def get_container_capacity():
    """获取容器(TAP)容量信息"""
    container_clusters = [
        {
            "cluster_name": "嘉兴K8S生产集群",
            "cluster_id": "JX-K8S-PROD",
            "cpu_total_cores": 480,
            "cpu_used_cores": 312,
            "memory_total_gb": 1920,
            "memory_used_gb": 1344,
            "storage_total_gb": 60000,
            "storage_used_gb": 42000
        },
        {
            "cluster_name": "后沙峪K8S生产集群",
            "cluster_id": "HSY-K8S-PROD",
            "cpu_total_cores": 640,
            "cpu_used_cores": 371,
            "memory_total_gb": 2560,
            "memory_used_gb": 1587,
            "storage_total_gb": 80000,
            "storage_used_gb": 60000
        },
        {
            "cluster_name": "嘉兴K8S测试集群",
            "cluster_id": "JX-K8S-TEST",
            "cpu_total_cores": 240,
            "cpu_used_cores": 108,
            "memory_total_gb": 960,
            "memory_used_gb": 499,
            "storage_total_gb": 30000,
            "storage_used_gb": 18000
        },
        {
            "cluster_name": "后沙峪TAP容器集群",
            "cluster_id": "HSY-TAP-PROD",
            "cpu_total_cores": 320,
            "cpu_used_cores": 230,
            "memory_total_gb": 1280,
            "memory_used_gb": 870,
            "storage_total_gb": 40000,
            "storage_used_gb": 32800
        }
    ]
    
    # 添加计算字段
    for cluster in container_clusters:
        cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
        cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])
        cluster['storage_usage_rate'] = calculate_usage_rate(cluster['storage_total_gb'], cluster['storage_used_gb'])
        
        cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
        cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']
        cluster['storage_available_gb'] = cluster['storage_total_gb'] - cluster['storage_used_gb']
        
        cluster['cpu_daily_change'] = generate_random_change()
        cluster['memory_daily_change'] = generate_random_change()
        cluster['storage_daily_change'] = generate_random_change()
        
        # 健康状态判断（CPU/内存<80%，存储<90%为正常）
        cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 80, 'yellow': 90})
        memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 80, 'yellow': 90})
        storage_health = get_health_status(cluster['storage_usage_rate'], {'green': 90, 'yellow': 95})
        
        if cpu_health == "警告" or memory_health == "警告" or storage_health == "警告":
            cluster['health_status'] = "警告"
        elif cpu_health == "观察" or memory_health == "观察" or storage_health == "观察":
            cluster['health_status'] = "观察"
        else:
            cluster['health_status'] = "正常"
            
        cluster['has_anomaly'] = cluster['health_status'] != "正常"
        cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"
    
    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "container_clusters": container_clusters,
            "summary": {
                "total_clusters": len(container_clusters),
                "normal_clusters": len([c for c in container_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in container_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/virtualization', methods=['GET'])
def get_virtualization_capacity():
    """获取虚拟化容量信息"""
    vm_clusters = [
        {
            "cluster_name": "嘉兴vSphere生产集群",
            "cluster_id": "JX-VSPHERE-PROD",
            "cpu_total_cores": 320,
            "cpu_used_cores": 192,
            "memory_total_gb": 2560,
            "memory_used_gb": 1920,
            "storage_total_gb": 80000,
            "storage_used_gb": 56000
        },
        {
            "cluster_name": "后沙峪vSphere生产集群",
            "cluster_id": "HSY-VSPHERE-PROD",
            "cpu_total_cores": 480,
            "cpu_used_cores": 264,
            "memory_total_gb": 3840,
            "memory_used_gb": 2611,
            "storage_total_gb": 120000,
            "storage_used_gb": 78000
        },
        {
            "cluster_name": "嘉兴vSphere测试集群",
            "cluster_id": "JX-VSPHERE-TEST",
            "cpu_total_cores": 160,
            "cpu_used_cores": 67,
            "memory_total_gb": 1280,
            "memory_used_gb": 614,
            "storage_total_gb": 40000,
            "storage_used_gb": 22000
        },
        {
            "cluster_name": "后沙峪Hyper-V集群",
            "cluster_id": "HSY-HYPERV-PROD",
            "cpu_total_cores": 240,
            "cpu_used_cores": 115,
            "memory_total_gb": 1920,
            "memory_used_gb": 998,
            "storage_total_gb": 60000,
            "storage_used_gb": 34800
        }
    ]
    
    # 添加计算字段
    for cluster in vm_clusters:
        cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
        cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])
        cluster['storage_usage_rate'] = calculate_usage_rate(cluster['storage_total_gb'], cluster['storage_used_gb'])
        
        cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
        cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']
        cluster['storage_available_gb'] = cluster['storage_total_gb'] - cluster['storage_used_gb']
        
        cluster['cpu_daily_change'] = generate_random_change()
        cluster['memory_daily_change'] = generate_random_change()
        cluster['storage_daily_change'] = generate_random_change()
        
        # 健康状态判断（CPU/内存<75%，存储<90%为正常）
        cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 75, 'yellow': 85})
        memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 75, 'yellow': 85})
        storage_health = get_health_status(cluster['storage_usage_rate'], {'green': 90, 'yellow': 95})
        
        if cpu_health == "警告" or memory_health == "警告" or storage_health == "警告":
            cluster['health_status'] = "警告"
        elif cpu_health == "观察" or memory_health == "观察" or storage_health == "观察":
            cluster['health_status'] = "观察"
        else:
            cluster['health_status'] = "正常"
            
        cluster['has_anomaly'] = cluster['health_status'] != "正常"
        cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"
    
    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "vm_clusters": vm_clusters,
            "summary": {
                "total_clusters": len(vm_clusters),
                "normal_clusters": len([c for c in vm_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in vm_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return json_response({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "容量监控API服务运行正常"
    })



@app.route('/api/get_all_capacity_data', methods=['POST'])
def get_all_capacity_data():
    """获取所有容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')

        # 获取所有容量数据
        storage_response = get_storage_capacity()
        database_response = get_database_capacity()
        container_response = get_container_capacity()
        vm_response = get_virtualization_capacity()

        # 提取数据部分
        storage_data = storage_response.get_json()
        database_data = database_response.get_json()
        container_data = container_response.get_json()
        vm_data = vm_response.get_json()

        # 组织数据供LLM分析
        capacity_data = {
            "report_info": {
                "report_date": report_date,
                "system_name": system_name,
                "data_collection_time": datetime.now().isoformat()
            },
            "storage_capacity": storage_data,
            "database_capacity": database_data,
            "container_capacity": container_data,
            "virtualization_capacity": vm_data,
            "summary": {
                "total_storage_pools": len(storage_data.get('pools', [])),
                "total_database_instances": len(database_data.get('instances', [])),
                "total_container_clusters": len(container_data.get('clusters', [])),
                "total_vm_clusters": len(vm_data.get('clusters', []))
            }
        }

        return jsonify({
            'success': True,
            'message': '容量数据获取成功',
            'timestamp': datetime.now().isoformat(),
            'data': capacity_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'容量数据获取失败: {str(e)}',
            'error': str(e)
        }), 500

@app.route('/api/export_word', methods=['POST'])
def export_word():
    """导出Word文档的HTTP接口"""
    try:
        data = request.get_json()

        # 获取参数
        report_content = data.get('report_content', '')
        report_date = data.get('report_date', '')
        system_name = data.get('system_name', '')
        save_path = data.get('save_path', 'D:/work/LLM/reports/')

        # 清理系统名称中的特殊字符
        import re
        system_name = re.sub(r'[^\w\s\-_\u4e00-\u9fff]', '', system_name)

        if not report_content or not report_date or not system_name:
            return json_response({
                'success': False,
                'error': '缺少必要参数：report_content, report_date, system_name'
            }, 400)

        # 导出为Word格式
        file_path = export_to_word(report_content, report_date, system_name, save_path)

        return json_response({
            'success': True,
            'message': 'Word文档生成成功',
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': f"{os.path.getsize(file_path) / 1024:.2f} KB",
            'file_type': 'Microsoft Word文档 (.docx)',
            'note': '生成的Word文档可以直接用Microsoft Word打开'
        })

    except Exception as e:
        return json_response({
            'success': False,
            'error': f'Word导出失败: {str(e)}'
        }, 500)





def extract_query_type_from_classifier_output(classifier_output):
    """从问题分类器的输出中提取查询类型"""
    try:
        # 如果输入已经是简单的查询类型，直接返回
        if classifier_output in ['all', 'storage', 'database', 'container', 'virtualization']:
            return classifier_output

        # 尝试解析JSON格式的输出
        import json
        import re

        # 清理输出，移除可能的markdown格式
        cleaned_output = classifier_output.strip()
        if '```json' in cleaned_output:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_output, re.DOTALL)
            if json_match:
                cleaned_output = json_match.group(1)
        elif '```' in cleaned_output:
            # 移除其他markdown标记
            cleaned_output = re.sub(r'```[^`]*```', '', cleaned_output).strip()

        # 尝试解析JSON
        try:
            parsed_data = json.loads(cleaned_output)
            if isinstance(parsed_data, dict) and 'query_type' in parsed_data:
                return parsed_data['query_type']
        except json.JSONDecodeError:
            pass

        # 如果JSON解析失败，尝试从文本中提取查询类型
        text_lower = classifier_output.lower()
        if 'storage' in text_lower:
            return 'storage'
        elif 'database' in text_lower:
            return 'database'
        elif 'container' in text_lower:
            return 'container'
        elif 'virtualization' in text_lower:
            return 'virtualization'
        else:
            return 'all'  # 默认返回全量查询

    except Exception as e:
        print(f"解析问题分类器输出时出错: {str(e)}")
        return 'all'  # 出错时默认返回全量查询

@app.route('/api/get_capacity_data', methods=['POST'])
def get_capacity_data():
    """根据查询类型获取对应的容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')
        query_type_raw = data.get('query_type', 'all')

        # 处理问题分类器的JSON输出
        query_type = extract_query_type_from_classifier_output(query_type_raw)

        # 基础报告信息
        report_info = {
            "report_date": report_date,
            "system_name": system_name,
            "query_type": query_type,
            "data_collection_time": datetime.now().isoformat()
        }

        capacity_data = {"report_info": report_info}
        summary = {}

        # 根据查询类型获取对应数据
        # 支持组合查询，如 "storage,virtualization"
        query_types = [t.strip() for t in query_type.split(',')]

        # 验证查询类型
        valid_types = ['all', 'storage', 'database', 'container', 'virtualization']
        for qt in query_types:
            if qt not in valid_types:
                return json_response({
                    'success': False,
                    'message': f'不支持的查询类型: {qt}。支持的类型: {", ".join(valid_types)}'
                }, 400)

        # 如果是all，获取所有数据
        if 'all' in query_types:
            query_types = ['storage', 'database', 'container', 'virtualization']

        # 根据查询类型获取数据
        if 'storage' in query_types:
            storage_response = get_storage_capacity()
            storage_data = storage_response.get_json()
            capacity_data["storage_capacity"] = storage_data
            summary["total_storage_pools"] = len(storage_data.get('pools', []))

        if 'database' in query_types:
            database_response = get_database_capacity()
            database_data = database_response.get_json()
            capacity_data["database_capacity"] = database_data
            summary["total_database_instances"] = len(database_data.get('instances', []))

        if 'container' in query_types:
            container_response = get_container_capacity()
            container_data = container_response.get_json()
            capacity_data["container_capacity"] = container_data
            summary["total_container_clusters"] = len(container_data.get('clusters', []))

        if 'virtualization' in query_types:
            vm_response = get_virtualization_capacity()
            vm_data = vm_response.get_json()
            capacity_data["virtualization_capacity"] = vm_data
            summary["total_vm_clusters"] = len(vm_data.get('clusters', []))

        # 如果没有匹配的查询类型
        if not any(qt in query_types for qt in ['storage', 'database', 'container', 'virtualization']):
            return json_response({
                'success': False,
                'message': f'无效的查询类型组合: {query_type}'
            }, 400)

        capacity_data["summary"] = summary

        # 使用自定义JSON响应函数确保中文字符正确显示
        return json_response(capacity_data)

    except Exception as e:
        return json_response({
            'success': False,
            'message': f'获取容量数据失败: {str(e)}'
        }, 500)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
