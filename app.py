from flask import Flask, jsonify, request
from datetime import datetime, timedelta
import random
import os
from html_export import export_to_word
from smart_report_generator import smart_report_generator
from llm_analyzer import capacity_analyzer

app = Flask(__name__)

# 模拟数据生成函数
def generate_random_change():
    """生成随机的日变化百分比"""
    return round(random.uniform(-3.0, 3.0), 2)

def calculate_usage_rate(total, used):
    """计算使用率"""
    return round((used / total) * 100, 2)

def get_health_status(usage_rate, thresholds):
    """根据使用率获取健康状态"""
    if usage_rate < thresholds['green']:
        return "正常"
    elif usage_rate < thresholds['yellow']:
        return "观察"
    else:
        return "警告"

@app.route('/api/storage', methods=['GET'])
def get_storage_capacity():
    """获取存储容量信息"""
    storage_pools = [
        {
            "pool_name": "嘉兴中端虚拟化存储池",
            "pool_id": "JX-M-VM-Prod",
            "total_capacity_gb": 769034,
            "used_capacity_gb": 685234,
            "available_capacity_gb": 83800
        },
        {
            "pool_name": "后沙峪中端虚拟化存储池",
            "pool_id": "HSY-M-VM-Prod",
            "total_capacity_gb": 5200518,
            "used_capacity_gb": 3496748,
            "available_capacity_gb": 1703770
        },
        {
            "pool_name": "嘉兴中端数据库存储池",
            "pool_id": "JX-M-DB-Prod",
            "total_capacity_gb": 822628,
            "used_capacity_gb": 291539,
            "available_capacity_gb": 531089
        },
        {
            "pool_name": "后沙峪中端数据库存储池",
            "pool_id": "HSY-M-DB-Prod",
            "total_capacity_gb": 2327194,
            "used_capacity_gb": 838188,
            "available_capacity_gb": 1489006
        },
        {
            "pool_name": "后沙峪高端存储池",
            "pool_id": "HSY-H-Prod",
            "total_capacity_gb": 760877,
            "used_capacity_gb": 248246,
            "available_capacity_gb": 512631
        }
    ]
    
    # 添加计算字段
    for pool in storage_pools:
        pool['usage_rate'] = calculate_usage_rate(pool['total_capacity_gb'], pool['used_capacity_gb'])
        pool['daily_change'] = generate_random_change()
        pool['health_status'] = get_health_status(pool['usage_rate'], {'green': 90, 'yellow': 95})
        pool['has_anomaly'] = pool['health_status'] != "正常"
        pool['measures'] = "无需措施" if not pool['has_anomaly'] else "需要关注并制定调整方案"
    
    return jsonify({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "storage_pools": storage_pools,
            "summary": {
                "total_pools": len(storage_pools),
                "normal_pools": len([p for p in storage_pools if p['health_status'] == "正常"]),
                "warning_pools": len([p for p in storage_pools if p['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/database', methods=['GET'])
def get_database_capacity():
    """获取数据库容量信息"""
    database_instances = [
        {
            "db_name": "嘉兴Oracle生产库",
            "db_id": "JX-ORA-PROD01",
            "total_capacity_gb": 8192000,
            "used_capacity_gb": 6348800,
            "available_capacity_gb": 1843200
        },
        {
            "db_name": "嘉兴Oracle生产库",
            "db_id": "JX-ORA-PROD02",
            "total_capacity_gb": 6144000,
            "used_capacity_gb": 4196352,
            "available_capacity_gb": 1947648
        },
        {
            "db_name": "后沙峪MySQL集群主库",
            "db_id": "HSY-MYSQL-MASTER",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "后沙峪MySQL集群从库1",
            "db_id": "HSY-MYSQL-SLAVE1",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "后沙峪MySQL集群从库2",
            "db_id": "HSY-MYSQL-SLAVE2",
            "total_capacity_gb": 4096000,
            "used_capacity_gb": 2867200,
            "available_capacity_gb": 1228800
        },
        {
            "db_name": "嘉兴PostgreSQL业务库",
            "db_id": "JX-PG-BIZ",
            "total_capacity_gb": 2048000,
            "used_capacity_gb": 1331200,
            "available_capacity_gb": 716800
        },
        {
            "db_name": "后沙峪PostgreSQL分析库",
            "db_id": "HSY-PG-ANALYSIS",
            "total_capacity_gb": 5120000,
            "used_capacity_gb": 2560000,
            "available_capacity_gb": 2560000
        }
    ]
    
    # 添加计算字段
    for db in database_instances:
        db['usage_rate'] = calculate_usage_rate(db['total_capacity_gb'], db['used_capacity_gb'])
        db['daily_change'] = generate_random_change()
        db['health_status'] = get_health_status(db['usage_rate'], {'green': 85, 'yellow': 95})
        db['has_anomaly'] = db['health_status'] != "正常"
        db['measures'] = "无需措施" if not db['has_anomaly'] else "需要关注并制定调整方案"
    
    return jsonify({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "database_instances": database_instances,
            "summary": {
                "total_instances": len(database_instances),
                "normal_instances": len([db for db in database_instances if db['health_status'] == "正常"]),
                "warning_instances": len([db for db in database_instances if db['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/container', methods=['GET'])
def get_container_capacity():
    """获取容器(TAP)容量信息"""
    container_clusters = [
        {
            "cluster_name": "嘉兴K8S生产集群",
            "cluster_id": "JX-K8S-PROD",
            "cpu_total_cores": 480,
            "cpu_used_cores": 312,
            "memory_total_gb": 1920,
            "memory_used_gb": 1344,
            "storage_total_gb": 60000,
            "storage_used_gb": 42000
        },
        {
            "cluster_name": "后沙峪K8S生产集群",
            "cluster_id": "HSY-K8S-PROD",
            "cpu_total_cores": 640,
            "cpu_used_cores": 371,
            "memory_total_gb": 2560,
            "memory_used_gb": 1587,
            "storage_total_gb": 80000,
            "storage_used_gb": 60000
        },
        {
            "cluster_name": "嘉兴K8S测试集群",
            "cluster_id": "JX-K8S-TEST",
            "cpu_total_cores": 240,
            "cpu_used_cores": 108,
            "memory_total_gb": 960,
            "memory_used_gb": 499,
            "storage_total_gb": 30000,
            "storage_used_gb": 18000
        },
        {
            "cluster_name": "后沙峪TAP容器集群",
            "cluster_id": "HSY-TAP-PROD",
            "cpu_total_cores": 320,
            "cpu_used_cores": 230,
            "memory_total_gb": 1280,
            "memory_used_gb": 870,
            "storage_total_gb": 40000,
            "storage_used_gb": 32800
        }
    ]
    
    # 添加计算字段
    for cluster in container_clusters:
        cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
        cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])
        cluster['storage_usage_rate'] = calculate_usage_rate(cluster['storage_total_gb'], cluster['storage_used_gb'])
        
        cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
        cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']
        cluster['storage_available_gb'] = cluster['storage_total_gb'] - cluster['storage_used_gb']
        
        cluster['cpu_daily_change'] = generate_random_change()
        cluster['memory_daily_change'] = generate_random_change()
        cluster['storage_daily_change'] = generate_random_change()
        
        # 健康状态判断（CPU/内存<80%，存储<90%为正常）
        cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 80, 'yellow': 90})
        memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 80, 'yellow': 90})
        storage_health = get_health_status(cluster['storage_usage_rate'], {'green': 90, 'yellow': 95})
        
        if cpu_health == "警告" or memory_health == "警告" or storage_health == "警告":
            cluster['health_status'] = "警告"
        elif cpu_health == "观察" or memory_health == "观察" or storage_health == "观察":
            cluster['health_status'] = "观察"
        else:
            cluster['health_status'] = "正常"
            
        cluster['has_anomaly'] = cluster['health_status'] != "正常"
        cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"
    
    return jsonify({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "container_clusters": container_clusters,
            "summary": {
                "total_clusters": len(container_clusters),
                "normal_clusters": len([c for c in container_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in container_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/virtualization', methods=['GET'])
def get_virtualization_capacity():
    """获取虚拟化容量信息"""
    vm_clusters = [
        {
            "cluster_name": "嘉兴vSphere生产集群",
            "cluster_id": "JX-VSPHERE-PROD",
            "cpu_total_cores": 320,
            "cpu_used_cores": 192,
            "memory_total_gb": 2560,
            "memory_used_gb": 1920,
            "storage_total_gb": 80000,
            "storage_used_gb": 56000
        },
        {
            "cluster_name": "后沙峪vSphere生产集群",
            "cluster_id": "HSY-VSPHERE-PROD",
            "cpu_total_cores": 480,
            "cpu_used_cores": 264,
            "memory_total_gb": 3840,
            "memory_used_gb": 2611,
            "storage_total_gb": 120000,
            "storage_used_gb": 78000
        },
        {
            "cluster_name": "嘉兴vSphere测试集群",
            "cluster_id": "JX-VSPHERE-TEST",
            "cpu_total_cores": 160,
            "cpu_used_cores": 67,
            "memory_total_gb": 1280,
            "memory_used_gb": 614,
            "storage_total_gb": 40000,
            "storage_used_gb": 22000
        },
        {
            "cluster_name": "后沙峪Hyper-V集群",
            "cluster_id": "HSY-HYPERV-PROD",
            "cpu_total_cores": 240,
            "cpu_used_cores": 115,
            "memory_total_gb": 1920,
            "memory_used_gb": 998,
            "storage_total_gb": 60000,
            "storage_used_gb": 34800
        }
    ]
    
    # 添加计算字段
    for cluster in vm_clusters:
        cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
        cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])
        cluster['storage_usage_rate'] = calculate_usage_rate(cluster['storage_total_gb'], cluster['storage_used_gb'])
        
        cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
        cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']
        cluster['storage_available_gb'] = cluster['storage_total_gb'] - cluster['storage_used_gb']
        
        cluster['cpu_daily_change'] = generate_random_change()
        cluster['memory_daily_change'] = generate_random_change()
        cluster['storage_daily_change'] = generate_random_change()
        
        # 健康状态判断（CPU/内存<75%，存储<90%为正常）
        cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 75, 'yellow': 85})
        memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 75, 'yellow': 85})
        storage_health = get_health_status(cluster['storage_usage_rate'], {'green': 90, 'yellow': 95})
        
        if cpu_health == "警告" or memory_health == "警告" or storage_health == "警告":
            cluster['health_status'] = "警告"
        elif cpu_health == "观察" or memory_health == "观察" or storage_health == "观察":
            cluster['health_status'] = "观察"
        else:
            cluster['health_status'] = "正常"
            
        cluster['has_anomaly'] = cluster['health_status'] != "正常"
        cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"
    
    return jsonify({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "vm_clusters": vm_clusters,
            "summary": {
                "total_clusters": len(vm_clusters),
                "normal_clusters": len([c for c in vm_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in vm_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "容量监控API服务运行正常"
    })

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """生成容量报告的HTTP接口"""
    try:
        data = request.get_json()

        # 获取参数
        report_date = data.get('report_date', '')
        system_name = data.get('system_name', '')
        api_base_url = data.get('api_base_url', 'http://localhost:5000')

        if not report_date or not system_name:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：report_date 和 system_name'
            }), 400

        # 获取所有容量数据
        storage_response = get_storage_capacity()
        database_response = get_database_capacity()
        container_response = get_container_capacity()
        virtualization_response = get_virtualization_capacity()

        # 提取数据部分
        storage_data = storage_response.get_json()
        database_data = database_response.get_json()
        container_data = container_response.get_json()
        virtualization_data = virtualization_response.get_json()

        # 生成报告内容
        report_content = generate_capacity_report(
            storage_data, database_data, container_data, virtualization_data,
            report_date, system_name
        )

        return jsonify({
            'success': True,
            'report_content': report_content,
            'report_date': report_date,
            'system_name': system_name,
            'data_source': 'API自动获取',
            'report_type': 'traditional'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'报告生成失败: {str(e)}'
        }), 500

@app.route('/api/get_all_capacity_data', methods=['POST'])
def get_all_capacity_data():
    """获取所有容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')

        # 获取所有容量数据
        storage_response = get_storage_capacity()
        database_response = get_database_capacity()
        container_response = get_container_capacity()
        vm_response = get_virtualization_capacity()

        # 提取数据部分
        storage_data = storage_response.get_json()
        database_data = database_response.get_json()
        container_data = container_response.get_json()
        vm_data = vm_response.get_json()

        # 组织数据供LLM分析
        capacity_data = {
            "report_info": {
                "report_date": report_date,
                "system_name": system_name,
                "data_collection_time": datetime.now().isoformat()
            },
            "storage_capacity": storage_data,
            "database_capacity": database_data,
            "container_capacity": container_data,
            "virtualization_capacity": vm_data,
            "summary": {
                "total_storage_pools": len(storage_data.get('pools', [])),
                "total_database_instances": len(database_data.get('instances', [])),
                "total_container_clusters": len(container_data.get('clusters', [])),
                "total_vm_clusters": len(vm_data.get('clusters', []))
            }
        }

        return jsonify({
            'success': True,
            'message': '容量数据获取成功',
            'timestamp': datetime.now().isoformat(),
            'data': capacity_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'容量数据获取失败: {str(e)}',
            'error': str(e)
        }), 500

@app.route('/api/export_word', methods=['POST'])
def export_word():
    """导出Word文档的HTTP接口"""
    try:
        data = request.get_json()

        # 获取参数
        report_content = data.get('report_content', '')
        report_date = data.get('report_date', '')
        system_name = data.get('system_name', '')
        save_path = data.get('save_path', 'D:/work/LLM/reports/')
        print(report_content)
        print(system_name)
        if not report_content or not report_date or not system_name:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：report_content, report_date, system_name'
            }), 400

        # 导出为Word格式
        file_path = export_to_word(report_content, report_date, system_name, save_path)

        return jsonify({
            'success': True,
            'message': 'Word文档生成成功',
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': f"{os.path.getsize(file_path) / 1024:.2f} KB",
            'file_type': 'Microsoft Word文档 (.docx)',
            'note': '生成的Word文档可以直接用Microsoft Word打开'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Word导出失败: {str(e)}'
        }), 500

@app.route('/api/generate_smart_report', methods=['POST'])
def generate_smart_report():
    """生成智能容量报告（LLM增强版本）"""
    try:
        data = request.get_json()

        # 获取参数
        report_date = data.get('report_date', '')
        system_name = data.get('system_name', '')

        # 可选的LLM配置
        llm_config = data.get('llm_config', {})

        if not report_date or not system_name:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：report_date 和 system_name'
            }), 400

        # 配置分析器
        if llm_config.get('api_url') and llm_config.get('api_key'):
            # 使用用户提供的LLM配置
            from llm_analyzer import CapacityAnalyzer
            analyzer = CapacityAnalyzer(
                llm_api_url=llm_config['api_url'],
                llm_api_key=llm_config['api_key']
            )
            from smart_report_generator import SmartReportGenerator
            generator = SmartReportGenerator(analyzer)
            llm_enabled = True
        else:
            # 使用默认的本地分析
            generator = smart_report_generator
            llm_enabled = False

        # 获取所有容量数据
        storage_response = get_storage_capacity()
        database_response = get_database_capacity()
        container_response = get_container_capacity()
        virtualization_response = get_virtualization_capacity()

        # 提取数据部分
        storage_data = storage_response.get_json()
        database_data = database_response.get_json()
        container_data = container_response.get_json()
        virtualization_data = virtualization_response.get_json()

        # 生成智能报告内容
        smart_report_content = generator.generate_smart_report(
            storage_data, database_data, container_data, virtualization_data,
            report_date, system_name
        )

        # 计算报告统计信息
        report_length = len(smart_report_content)
        estimated_reading_time = max(1, report_length // 500)  # 估算阅读时间（分钟）

        return jsonify({
            'success': True,
            'report_content': smart_report_content,
            'report_date': report_date,
            'system_name': system_name,
            'data_source': 'API自动获取 + LLM智能分析' if llm_enabled else 'API自动获取 + 本地分析',
            'report_type': 'smart',
            'llm_enabled': llm_enabled,
            'report_length': report_length,
            'estimated_reading_time': f"约{estimated_reading_time}分钟",
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'智能报告生成失败: {str(e)}'
        }), 500

def generate_capacity_report(storage_data, database_data, container_data, virtualization_data, report_date, system_name):
    """生成容量报告内容"""

    report = f"""# {system_name}

数据来源：API自动获取

## 1. 存储资源容量及健康度排查

存储资源池本次排查情况如下：

| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|-------------|------------|-------------------|------------------------|----------|"""

    # 添加存储数据
    storage_pools = storage_data.get('data', {}).get('storage_pools', [])
    for i, pool in enumerate(storage_pools, 1):
        usage = pool.get('usage_rate', 0)
        change = pool.get('daily_change', 0)
        change_str = f"{change:+.2f}%" if change != 0 else "0%"

        # 判断状态
        if usage >= 95:
            status = "红色警告"
            measure = "向安监部报备隐患，制定应急处置方案"
        elif usage >= 90:
            status = "黄色观察"
            measure = "向调度部报备，制定调整方案"
        else:
            status = "否"
            measure = "无需措施"

        report += f"\n| {i} | {pool.get('pool_name', 'N/A')} | {pool.get('total_capacity_gb', 'N/A')} | {usage:.2f} | {change_str} | {status} | {measure} |"

    report += """

**健康度说明：**
- 绿色：正常值 （存储使用率<90%）运行良好。
- 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 存储资源池整体运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 2. 数据库资源容量及健康度排查

数据库资源池本次排查情况如下：

| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|-------------|------------|-------------------|------------------------|----------|"""

    # 添加数据库数据
    db_instances = database_data.get('data', {}).get('database_instances', [])
    for i, instance in enumerate(db_instances, 1):
        usage = instance.get('usage_rate', 0)
        change = instance.get('daily_change', 0)
        change_str = f"{change:+.2f}%" if change != 0 else "0%"

        # 判断状态
        if usage >= 95:
            status = "红色警告"
            measure = "向安监部报备隐患，制定应急处置方案"
        elif usage >= 85:
            status = "黄色观察"
            measure = "向调度部报备，制定调整方案"
        else:
            status = "否"
            measure = "无需措施"

        report += f"\n| {i} | {instance.get('instance_name', 'N/A')} | {instance.get('total_capacity_gb', 'N/A')} | {usage:.2f} | {change_str} | {status} | {measure} |"

    report += """

**健康度说明：**
- 绿色：正常值 （数据库使用率<85%）运行良好。
- 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 数据库实例运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及"""

    # 添加容器部分
    report += """

## 3. 容器资源容量及健康度排查

容器资源池本次排查情况如下：

| 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|"""

    container_clusters = container_data.get('data', {}).get('container_clusters', [])
    for i, cluster in enumerate(container_clusters, 1):
        cpu_usage = cluster.get('cpu_usage_rate', 0)
        memory_usage = cluster.get('memory_usage_rate', 0)
        storage_usage = cluster.get('storage_usage_rate', 0)

        # 简化的变化情况
        change_info = f"CPU{cpu_usage:+.1f}% 内存{memory_usage:+.1f}% 存储{storage_usage:+.1f}%"

        # 判断状态
        if cpu_usage >= 90 or memory_usage >= 90 or storage_usage >= 95:
            status = "红色警告"
            measure = "向安监部报备隐患，制定应急处置方案"
        elif cpu_usage >= 80 or memory_usage >= 80 or storage_usage >= 90:
            status = "黄色观察"
            measure = "向调度部报备，制定调整方案"
        else:
            status = "否"
            measure = "无需措施"

        report += f"\n| {i} | {cluster.get('cluster_name', 'N/A')} | {cpu_usage:.2f} | {memory_usage:.2f} | {storage_usage:.2f} | {change_info} | {status} | {measure} |"

    return report

def extract_query_type_from_classifier_output(classifier_output):
    """从问题分类器的输出中提取查询类型"""
    try:
        # 如果输入已经是简单的查询类型，直接返回
        if classifier_output in ['all', 'storage', 'database', 'container', 'virtualization']:
            return classifier_output

        # 尝试解析JSON格式的输出
        import json
        import re

        # 清理输出，移除可能的markdown格式
        cleaned_output = classifier_output.strip()
        if '```json' in cleaned_output:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_output, re.DOTALL)
            if json_match:
                cleaned_output = json_match.group(1)
        elif '```' in cleaned_output:
            # 移除其他markdown标记
            cleaned_output = re.sub(r'```[^`]*```', '', cleaned_output).strip()

        # 尝试解析JSON
        try:
            parsed_data = json.loads(cleaned_output)
            if isinstance(parsed_data, dict) and 'query_type' in parsed_data:
                return parsed_data['query_type']
        except json.JSONDecodeError:
            pass

        # 如果JSON解析失败，尝试从文本中提取查询类型
        text_lower = classifier_output.lower()
        if 'storage' in text_lower:
            return 'storage'
        elif 'database' in text_lower:
            return 'database'
        elif 'container' in text_lower:
            return 'container'
        elif 'virtualization' in text_lower:
            return 'virtualization'
        else:
            return 'all'  # 默认返回全量查询

    except Exception as e:
        print(f"解析问题分类器输出时出错: {str(e)}")
        return 'all'  # 出错时默认返回全量查询

@app.route('/api/get_capacity_data', methods=['POST'])
def get_capacity_data():
    """根据查询类型获取对应的容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')
        query_type_raw = data.get('query_type', 'all')

        # 处理问题分类器的JSON输出
        query_type = extract_query_type_from_classifier_output(query_type_raw)

        # 基础报告信息
        report_info = {
            "report_date": report_date,
            "system_name": system_name,
            "query_type": query_type,
            "data_collection_time": datetime.now().isoformat()
        }

        capacity_data = {"report_info": report_info}
        summary = {}

        # 根据查询类型获取对应数据
        if query_type == 'all':
            # 获取所有容量数据
            storage_response = get_storage_capacity()
            database_response = get_database_capacity()
            container_response = get_container_capacity()
            vm_response = get_virtualization_capacity()

            storage_data = storage_response.get_json()
            database_data = database_response.get_json()
            container_data = container_response.get_json()
            vm_data = vm_response.get_json()

            capacity_data.update({
                "storage_capacity": storage_data,
                "database_capacity": database_data,
                "container_capacity": container_data,
                "virtualization_capacity": vm_data
            })

            summary = {
                "total_storage_pools": len(storage_data.get('pools', [])),
                "total_database_instances": len(database_data.get('instances', [])),
                "total_container_clusters": len(container_data.get('clusters', [])),
                "total_vm_clusters": len(vm_data.get('clusters', []))
            }

        elif query_type == 'storage':
            # 只获取存储容量数据
            storage_response = get_storage_capacity()
            storage_data = storage_response.get_json()
            capacity_data["storage_capacity"] = storage_data
            summary = {"total_storage_pools": len(storage_data.get('pools', []))}

        elif query_type == 'database':
            # 只获取数据库容量数据
            database_response = get_database_capacity()
            database_data = database_response.get_json()
            capacity_data["database_capacity"] = database_data
            summary = {"total_database_instances": len(database_data.get('instances', []))}

        elif query_type == 'container':
            # 只获取容器容量数据
            container_response = get_container_capacity()
            container_data = container_response.get_json()
            capacity_data["container_capacity"] = container_data
            summary = {"total_container_clusters": len(container_data.get('clusters', []))}

        elif query_type == 'virtualization':
            # 只获取虚拟化容量数据
            vm_response = get_virtualization_capacity()
            vm_data = vm_response.get_json()
            capacity_data["virtualization_capacity"] = vm_data
            summary = {"total_vm_clusters": len(vm_data.get('clusters', []))}

        else:
            return jsonify({
                'success': False,
                'message': f'不支持的查询类型: {query_type}。支持的类型: all, storage, database, container, virtualization'
            }), 400

        capacity_data["summary"] = summary

        return jsonify({
            'success': True,
            'message': f'{query_type}容量数据获取成功',
            'timestamp': datetime.now().isoformat(),
            'query_type': query_type,
            'data': capacity_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取容量数据失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
