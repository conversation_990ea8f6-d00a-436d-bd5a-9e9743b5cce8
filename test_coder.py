import asyncio
from autogen_agentchat.agents import <PERSON><PERSON><PERSON>
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor
from autogen_ext.tools.code_execution import PythonCodeExecutionTool


async def main() -> None:
    tool = PythonCodeExecutionTool(LocalCommandLineCodeExecutor(work_dir="coding"))
    agent = AssistantAgent(
        "assistant",OpenAIChatCompletionClient(
    model="Qwen/Qwen2.5-72B-Instruct-128K",
    base_url="https://api.siliconflow.cn/v1",
    api_key="sk-klqzerngqilswjgvubekqcevgmsynkpdhzdsccrhjxprhjcf",
        model_info={
            "vision": True,
            "function_calling": True,
            "json_output": True,
            "structured_output": True,  # 添加structured_output字段
            "family": "unknown",
        },
    ), tools=[tool], reflect_on_tool_use=True
    )
    await Console(
        agent.run_stream(
            task="帮我将2000到2010年6月份平均气温做一个折线图"
        )
    )


asyncio.run(main())
