#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试问题分类器功能
验证新的智能需求分析工作流
"""

import requests
import json
from datetime import datetime

def test_question_classifier():
    """测试问题分类器的功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试问题分类器功能 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试用例：不同类型的用户需求
    test_cases = [
        {
            "requirement": "我需要生成一份完整的IT基础设施容量报告，包含所有资源类型的分析",
            "expected": "all",
            "description": "完整报告需求"
        },
        {
            "requirement": "帮我检查一下存储系统的容量使用情况，生成存储专项报告",
            "expected": "storage", 
            "description": "存储专项需求"
        },
        {
            "requirement": "我想了解数据库的容量状况，请生成数据库容量分析报告",
            "expected": "database",
            "description": "数据库专项需求"
        },
        {
            "requirement": "需要分析容器集群的资源使用情况，生成容器容量报告",
            "expected": "container",
            "description": "容器专项需求"
        },
        {
            "requirement": "请帮我检查虚拟化平台的容量状态，生成虚拟化专项报告",
            "expected": "virtualization",
            "description": "虚拟化专项需求"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_case['description']}")
        print(f"用户需求: {test_case['requirement']}")
        print(f"预期类型: {test_case['expected']}")
        
        # 模拟问题分类器的输出（这里我们直接测试后端处理函数）
        classifier_outputs = [
            # 模拟不同格式的分类器输出
            test_case['expected'],  # 直接输出类型
            f'{{"query_type": "{test_case["expected"]}", "analysis": "测试分析", "confidence": 0.9}}',  # JSON格式
            f'```json\n{{"query_type": "{test_case["expected"]}", "analysis": "测试分析", "confidence": 0.9}}\n```',  # Markdown包装的JSON
        ]
        
        for j, classifier_output in enumerate(classifier_outputs):
            print(f"  格式 {j+1}: 测试分类器输出处理...")
            
            # 测试API调用
            try:
                request_data = {
                    "report_date": "2025-07-01",
                    "system_name": "测试系统",
                    "query_type": classifier_output
                }
                
                response = requests.post(f"{base_url}/api/get_capacity_data", 
                                       json=request_data, 
                                       timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    actual_type = result.get('query_type', 'unknown')
                    
                    if actual_type == test_case['expected']:
                        print(f"    ✅ 成功: 识别为 {actual_type}")
                        if j == 0:  # 只在第一个格式测试时计数
                            success_count += 1
                    else:
                        print(f"    ❌ 失败: 预期 {test_case['expected']}, 实际 {actual_type}")
                else:
                    print(f"    ❌ API调用失败: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 测试异常: {str(e)}")
        
        print()
    
    # 输出测试总结
    print("="*50)
    print("📊 测试总结")
    print("="*50)
    print(f"✅ 成功测试: {success_count}/{total_count}")
    print(f"❌ 失败测试: {total_count - success_count}/{total_count}")
    print(f"📈 成功率: {(success_count/total_count)*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过! 问题分类器功能正常工作!")
    else:
        print("\n⚠️  部分测试失败，请检查实现")
    
    return success_count == total_count

def test_complete_workflow_simulation():
    """测试完整工作流的模拟"""
    print("\n=== 测试完整工作流模拟 ===")
    
    # 模拟完整的工作流程
    workflow_steps = [
        "1. 用户输入需求",
        "2. 问题分类器分析需求",
        "3. 获取对应的容量数据", 
        "4. LLM分析生成报告",
        "5. 导出Word文档"
    ]
    
    print("工作流步骤:")
    for step in workflow_steps:
        print(f"  {step}")
    
    print("\n模拟执行:")
    
    # 步骤1: 用户需求
    user_requirement = "我需要检查存储系统的容量使用情况"
    print(f"✅ 步骤1: 用户需求 - {user_requirement}")
    
    # 步骤2: 问题分类器（模拟）
    classifier_result = {
        "query_type": "storage",
        "analysis": "用户明确要求检查存储系统容量，应生成存储专项报告",
        "confidence": 0.95
    }
    print(f"✅ 步骤2: 问题分类器 - 识别为 {classifier_result['query_type']}")
    
    # 步骤3: 获取容量数据
    try:
        response = requests.post("http://127.0.0.1:5000/api/get_capacity_data",
                               json={
                                   "report_date": "2025-07-01",
                                   "system_name": "测试系统",
                                   "query_type": json.dumps(classifier_result)
                               },
                               timeout=10)
        
        if response.status_code == 200:
            print("✅ 步骤3: 容量数据获取成功")
        else:
            print(f"❌ 步骤3: 容量数据获取失败 - {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 步骤3: 容量数据获取异常 - {str(e)}")
        return False
    
    # 步骤4: LLM分析（模拟）
    print("✅ 步骤4: LLM分析 - 模拟完成")
    
    # 步骤5: Word导出（模拟）
    print("✅ 步骤5: Word文档导出 - 模拟完成")
    
    print("\n🎉 完整工作流模拟成功!")
    return True

def main():
    """主测试函数"""
    print("🎯 智能容量报告系统 - 问题分类器功能测试")
    print("="*60)
    
    # 测试问题分类器功能
    classifier_success = test_question_classifier()
    
    # 测试完整工作流
    workflow_success = test_complete_workflow_simulation()
    
    print("\n" + "="*60)
    print("🎯 总体测试结果:")
    print(f"问题分类器功能: {'✅ 通过' if classifier_success else '❌ 失败'}")
    print(f"完整工作流模拟: {'✅ 通过' if workflow_success else '❌ 失败'}")
    
    if classifier_success and workflow_success:
        print("\n🎉 所有功能测试通过!")
        print("\n📋 下一步操作:")
        print("1. 在Dify平台导入更新后的配置文件")
        print("2. 配置LLM模型（GPT-4推荐）")
        print("3. 测试完整的工作流")
        print("4. 验证生成的Word文档内容")
    else:
        print("\n⚠️  部分功能存在问题，请检查配置")

if __name__ == "__main__":
    main()
