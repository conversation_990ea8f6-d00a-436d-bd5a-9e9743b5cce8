version: "0.1.0"
kind: app
data:
  title: 智能容量报告生成器
  description: 生成智能容量报告并导出Word文档
  icon: 📊
  icon_background: "#3B82F6"
  mode: workflow
  workflow:
    graph:
      nodes:
        - id: start
          data:
            type: start
            title: 开始
            variables:
              - variable: api_url
                type: text-input
                label: API地址
                required: true
                default: http://************:5000
              - variable: date
                type: text-input
                label: 日期
                required: true
                default: "2024-01-15"
              - variable: name
                type: text-input
                label: 报告名称
                required: true
                default: 容量检查报告
          position:
            x: 100
            y: 200
        - id: http1
          data:
            type: http-request
            title: 生成报告
            method: post
            url: "{{#start.api_url#}}/api/generate_smart_report"
            headers: "Content-Type: application/json"
            body:
              type: json
              data: |
                {
                  "report_date": "{{#start.date#}}",
                  "system_name": "{{#start.name#}}"
                }
            timeout:
              read: 60
          position:
            x: 400
            y: 200
        - id: http2
          data:
            type: http-request
            title: 导出Word
            method: post
            url: "{{#start.api_url#}}/api/export_word"
            headers: "Content-Type: application/json"
            body:
              type: json
              data: |
                {
                  "report_content": "{{#http1.body.report_content#}}",
                  "report_date": "{{#http1.body.report_date#}}",
                  "system_name": "{{#http1.body.system_name#}}",
                  "save_path": "./reports/"
                }
            timeout:
              read: 30
          position:
            x: 700
            y: 200
        - id: end
          data:
            type: end
            title: 完成
            outputs:
              - variable: result
                type: string
                value_selector:
                  - http2
                  - body
                  - file_path
          position:
            x: 1000
            y: 200
      edges:
        - source: start
          target: http1
        - source: http1
          target: http2
        - source: http2
          target: end
