import requests
import json
from datetime import datetime
import time

# API基础URL
BASE_URL = "http://localhost:5000/api"

def print_separator(title="", char="=", length=80):
    """打印分隔线"""
    if title:
        title_line = f" {title} "
        padding = (length - len(title_line)) // 2
        print(char * padding + title_line + char * padding)
    else:
        print(char * length)

def format_capacity(gb_value):
    """格式化容量显示"""
    if gb_value >= 1024:
        return f"{gb_value/1024:.2f} TB"
    else:
        return f"{gb_value:.2f} GB"

def format_percentage(value):
    """格式化百分比显示"""
    return f"{value:.2f}%"

def test_health_check():
    """测试健康检查接口"""
    print_separator("健康检查接口测试")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)

        if response.status_code == 200:
            data = response.json()
            print("[PASS] 健康检查接口正常")
            print(f"服务状态: {data.get('status', 'unknown')}")
            print(f"响应时间: {data.get('timestamp', 'unknown')}")
            print(f"消息: {data.get('message', 'unknown')}")

            if 'version' in data:
                print(f"版本: {data['version']}")
            if 'uptime' in data:
                print(f"运行时间: {data['uptime']}")

            if 'endpoints' in data:
                print("接口状态:")
                for endpoint, status in data['endpoints'].items():
                    status_mark = "[OK]" if status == "available" else "[FAIL]"
                    print(f"   {status_mark} {endpoint}: {status}")

            return True
        else:
            print(f"[FAIL] 健康检查接口异常: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"[FAIL] 健康检查接口连接失败: {str(e)}")
        return False

def test_storage_api():
    """测试存储容量接口"""
    print_separator("存储容量接口测试")
    try:
        response = requests.get(f"{BASE_URL}/storage", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print("[PASS] 存储容量接口正常")
            print(f"状态: {data['status']}")
            print(f"时间戳: {data['timestamp']}")

            if 'data' in data and 'storage_pools' in data['data']:
                pools = data['data']['storage_pools']
                print(f"\n存储池数量: {len(pools)}")

                for i, pool in enumerate(pools[:3], 1):  # 只显示前3个
                    print(f"\n存储池 {i}: {pool.get('pool_name', 'Unknown')}")
                    print(f"   ID: {pool.get('pool_id', 'Unknown')}")
                    print(f"   位置: {pool.get('location', 'Unknown')}")
                    print(f"   类型: {pool.get('storage_type', 'Unknown')}")
                    print(f"   厂商: {pool.get('vendor', 'Unknown')} {pool.get('model', '')}")
                    print(f"   总容量: {format_capacity(pool.get('total_capacity_gb', 0))}")
                    print(f"   已使用: {format_capacity(pool.get('used_capacity_gb', 0))}")
                    print(f"   可用: {format_capacity(pool.get('available_capacity_gb', 0))}")
                    print(f"   使用率: {format_percentage(pool.get('usage_rate', 0))}")
                    print(f"   日变化: {pool.get('daily_change', 0):+.2f}%")
                    print(f"   健康状态: {pool.get('health_status', 'Unknown')}")

                if len(pools) > 3:
                    print(f"\n... 还有 {len(pools) - 3} 个存储池")

            if 'data' in data and 'summary' in data['data']:
                summary = data['data']['summary']
                print(f"\n汇总信息:")
                print(f"   总存储池: {summary.get('total_pools', 0)}")
                print(f"   正常: {summary.get('normal_pools', 0)}")
                print(f"   警告: {summary.get('warning_pools', 0)}")
                if 'overall_usage_rate' in summary:
                    print(f"   整体使用率: {format_percentage(summary['overall_usage_rate'])}")

            return True
        else:
            print(f"[FAIL] 存储容量接口异常: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"[FAIL] 存储容量接口连接失败: {str(e)}")
        return False

def test_database_api():
    """测试数据库容量接口"""
    print_separator("数据库容量接口测试")
    try:
        response = requests.get(f"{BASE_URL}/database", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print("[PASS] 数据库容量接口正常")
            print(f"状态: {data['status']}")
            print(f"时间戳: {data['timestamp']}")

            if 'data' in data and 'database_instances' in data['data']:
                instances = data['data']['database_instances']
                print(f"\n数据库实例数量: {len(instances)}")

                for i, db in enumerate(instances[:3], 1):  # 只显示前3个
                    print(f"\n数据库 {i}: {db.get('db_name', 'Unknown')}")
                    print(f"   ID: {db.get('db_id', 'Unknown')}")
                    print(f"   类型: {db.get('db_type', 'Unknown')} {db.get('version', '')}")
                    print(f"   位置: {db.get('location', 'Unknown')}")
                    print(f"   环境: {db.get('environment', 'Unknown')}")
                    print(f"   总容量: {format_capacity(db.get('total_capacity_gb', 0))}")
                    print(f"   已使用: {format_capacity(db.get('used_capacity_gb', 0))}")
                    print(f"   使用率: {format_percentage(db.get('usage_rate', 0))}")
                    print(f"   CPU使用率: {format_percentage(db.get('cpu_usage_rate', 0))}")
                    print(f"   内存使用率: {format_percentage(db.get('memory_usage_rate', 0))}")
                    print(f"   连接数: {db.get('connection_count', 0)}/{db.get('max_connections', 0)}")
                    print(f"   健康状态: {db.get('health_status', 'Unknown')}")

                if len(instances) > 3:
                    print(f"\n... 还有 {len(instances) - 3} 个数据库实例")

            if 'data' in data and 'summary' in data['data']:
                summary = data['data']['summary']
                print(f"\n汇总信息:")
                print(f"   总实例: {summary.get('total_instances', 0)}")
                print(f"   正常: {summary.get('normal_instances', 0)}")
                print(f"   警告: {summary.get('warning_instances', 0)}")
                if 'overall_usage_rate' in summary:
                    print(f"   整体使用率: {format_percentage(summary['overall_usage_rate'])}")

            return True
        else:
            print(f"[FAIL] 数据库容量接口异常: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"[FAIL] 数据库容量接口连接失败: {str(e)}")
        return False

def test_container_api():
    """测试容器容量接口"""
    print_separator("容器容量接口测试")
    try:
        response = requests.get(f"{BASE_URL}/container", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print("[PASS] 容器容量接口正常")
            print(f"状态: {data['status']}")
            print(f"时间戳: {data['timestamp']}")

            if 'data' in data and 'container_clusters' in data['data']:
                clusters = data['data']['container_clusters']
                print(f"\n容器集群数量: {len(clusters)}")

                for i, cluster in enumerate(clusters[:3], 1):  # 只显示前3个
                    print(f"\n集群 {i}: {cluster.get('cluster_name', 'Unknown')}")
                    print(f"   ID: {cluster.get('cluster_id', 'Unknown')}")
                    print(f"   类型: {cluster.get('cluster_type', 'Unknown')} {cluster.get('version', '')}")
                    print(f"   位置: {cluster.get('location', 'Unknown')}")
                    print(f"   环境: {cluster.get('environment', 'Unknown')}")
                    print(f"   节点数: {cluster.get('node_count', 0)}")

                    # CPU信息
                    cpu_total = cluster.get('cpu_total_cores', 0)
                    cpu_used = cluster.get('cpu_used_cores', 0)
                    cpu_usage = cluster.get('cpu_usage_rate', 0)
                    print(f"   CPU: {cpu_used}/{cpu_total} 核 ({format_percentage(cpu_usage)})")

                    # 内存信息
                    mem_total = cluster.get('memory_total_gb', 0)
                    mem_used = cluster.get('memory_used_gb', 0)
                    mem_usage = cluster.get('memory_usage_rate', 0)
                    print(f"   内存: {format_capacity(mem_used)}/{format_capacity(mem_total)} ({format_percentage(mem_usage)})")

                    # 存储信息
                    storage_total = cluster.get('storage_total_gb', 0)
                    storage_used = cluster.get('storage_used_gb', 0)
                    storage_usage = cluster.get('storage_usage_rate', 0)
                    print(f"   存储: {format_capacity(storage_used)}/{format_capacity(storage_total)} ({format_percentage(storage_usage)})")

                    # Pod和容器信息
                    if 'pod_running_count' in cluster:
                        print(f"   Pod: {cluster.get('pod_running_count', 0)}/{cluster.get('pod_total_count', 0)} 运行中")
                    if 'container_running_count' in cluster:
                        print(f"   容器: {cluster.get('container_running_count', 0)}/{cluster.get('container_total_count', 0)} 运行中")

                    print(f"   健康状态: {cluster.get('health_status', 'Unknown')}")

                    # TAP特有信息
                    if cluster.get('cluster_type') == 'TAP' and 'tap_specific' in cluster:
                        tap_info = cluster['tap_specific']
                        print(f"   TAP工作负载: {tap_info.get('workload_count', 0)}")
                        print(f"   供应链: {tap_info.get('supply_chain_count', 0)}")

                if len(clusters) > 3:
                    print(f"\n... 还有 {len(clusters) - 3} 个容器集群")

            if 'data' in data and 'summary' in data['data']:
                summary = data['data']['summary']
                print(f"\n汇总信息:")
                print(f"   总集群: {summary.get('total_clusters', 0)}")
                print(f"   正常: {summary.get('normal_clusters', 0)}")
                print(f"   警告: {summary.get('warning_clusters', 0)}")
                print(f"   总节点: {summary.get('total_nodes', 0)}")
                if 'overall_cpu_usage' in summary:
                    print(f"   整体CPU使用率: {format_percentage(summary['overall_cpu_usage'])}")
                if 'overall_memory_usage' in summary:
                    print(f"   整体内存使用率: {format_percentage(summary['overall_memory_usage'])}")

            return True
        else:
            print(f"[FAIL] 容器容量接口异常: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"[FAIL] 容器容量接口连接失败: {str(e)}")
        return False

def test_virtualization_api():
    """测试虚拟化容量接口"""
    print_separator("虚拟化容量接口测试")
    try:
        response = requests.get(f"{BASE_URL}/virtualization", timeout=30)

        if response.status_code == 200:
            data = response.json()
            print("[PASS] 虚拟化容量接口正常")
            print(f"状态: {data['status']}")
            print(f"时间戳: {data['timestamp']}")

            if 'data' in data and 'vm_clusters' in data['data']:
                clusters = data['data']['vm_clusters']
                print(f"\n虚拟化集群数量: {len(clusters)}")

                for i, cluster in enumerate(clusters[:3], 1):  # 只显示前3个
                    print(f"\n集群 {i}: {cluster.get('cluster_name', 'Unknown')}")
                    print(f"   ID: {cluster.get('cluster_id', 'Unknown')}")
                    print(f"   类型: {cluster.get('cluster_type', 'Unknown')} {cluster.get('version', '')}")
                    print(f"   位置: {cluster.get('location', 'Unknown')}")
                    print(f"   环境: {cluster.get('environment', 'Unknown')}")

                    # 主机信息
                    host_count = cluster.get('esxi_host_count', cluster.get('host_count', 0))
                    print(f"   主机数: {host_count}")

                    # 物理资源
                    phy_cpu = cluster.get('physical_cpu_cores', 0)
                    phy_mem = cluster.get('physical_memory_gb', 0)
                    phy_storage = cluster.get('physical_storage_gb', 0)
                    print(f"   物理资源: {phy_cpu}核/{format_capacity(phy_mem)}/{format_capacity(phy_storage)}")

                    # CPU使用情况
                    cpu_usage = cluster.get('cpu_usage_rate', 0)
                    vcpu_allocated = cluster.get('vcpu_allocated', 0)
                    vcpu_used = cluster.get('vcpu_used', 0)
                    print(f"   CPU: {vcpu_used}/{vcpu_allocated} vCPU ({format_percentage(cpu_usage)})")

                    # 内存使用情况
                    mem_usage = cluster.get('memory_usage_rate', 0)
                    vmem_allocated = cluster.get('vmemory_allocated_gb', 0)
                    vmem_used = cluster.get('vmemory_used_gb', 0)
                    print(f"   内存: {format_capacity(vmem_used)}/{format_capacity(vmem_allocated)} ({format_percentage(mem_usage)})")

                    # 存储使用情况
                    storage_usage = cluster.get('storage_usage_rate', 0)
                    storage_allocated = cluster.get('storage_allocated_gb', 0)
                    storage_used = cluster.get('storage_used_gb', 0)
                    print(f"   存储: {format_capacity(storage_used)}/{format_capacity(storage_allocated)} ({format_percentage(storage_usage)})")

                    # 虚拟机信息
                    vm_running = cluster.get('vm_running_count', 0)
                    vm_total = cluster.get('vm_total_count', 0)
                    vm_off = cluster.get('vm_powered_off', 0)
                    print(f"   虚拟机: {vm_running}/{vm_total} 运行中 ({vm_off} 关机)")

                    print(f"   健康状态: {cluster.get('health_status', 'Unknown')}")

                    # 超分比例
                    if 'overcommit_ratios' in cluster:
                        ratios = cluster['overcommit_ratios']
                        cpu_ratio = ratios.get('cpu_overcommit', 0)
                        mem_ratio = ratios.get('memory_overcommit', 0)
                        print(f"   超分比例: CPU {cpu_ratio}:1, 内存 {mem_ratio}:1")

                if len(clusters) > 3:
                    print(f"\n... 还有 {len(clusters) - 3} 个虚拟化集群")

            if 'data' in data and 'summary' in data['data']:
                summary = data['data']['summary']
                print(f"\n汇总信息:")
                print(f"   总集群: {summary.get('total_clusters', 0)}")
                print(f"   正常: {summary.get('normal_clusters', 0)}")
                print(f"   警告: {summary.get('warning_clusters', 0)}")
                print(f"   总主机: {summary.get('total_hosts', 0)}")
                print(f"   总虚拟机: {summary.get('total_vms', 0)}")
                print(f"   运行中: {summary.get('running_vms', 0)}")
                if 'overall_cpu_usage' in summary:
                    print(f"   整体CPU使用率: {format_percentage(summary['overall_cpu_usage'])}")
                if 'overall_memory_usage' in summary:
                    print(f"   整体内存使用率: {format_percentage(summary['overall_memory_usage'])}")

            return True
        else:
            print(f"[FAIL] 虚拟化容量接口异常: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"[FAIL] 虚拟化容量接口连接失败: {str(e)}")
        return False

def test_api_performance():
    """测试API性能"""
    print_separator("API性能测试")

    endpoints = [
        ("health", "健康检查"),
        ("storage", "存储容量"),
        ("database", "数据库容量"),
        ("container", "容器容量"),
        ("virtualization", "虚拟化容量")
    ]

    performance_results = []

    for endpoint, name in endpoints:
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}/{endpoint}", timeout=30)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000  # 转换为毫秒

            if response.status_code == 200:
                # 验证响应数据格式
                try:
                    data = response.json()
                    has_valid_data = 'status' in data and 'timestamp' in data
                except:
                    has_valid_data = False

                data_size = len(response.content)

                performance_results.append({
                    'endpoint': endpoint,
                    'name': name,
                    'response_time': response_time,
                    'data_size': data_size,
                    'status': 'success',
                    'valid_data': has_valid_data
                })

                print(f"[PASS] {name}: {response_time:.2f}ms ({data_size} bytes)")
            else:
                performance_results.append({
                    'endpoint': endpoint,
                    'name': name,
                    'response_time': response_time,
                    'status': 'error',
                    'error_code': response.status_code
                })
                print(f"[FAIL] {name}: {response_time:.2f}ms (HTTP {response.status_code})")

        except Exception as e:
            performance_results.append({
                'endpoint': endpoint,
                'name': name,
                'status': 'failed',
                'error': str(e)
            })
            print(f"[FAIL] {name}: 连接失败 - {str(e)}")

    # 性能统计
    successful_tests = [r for r in performance_results if r['status'] == 'success']
    if successful_tests:
        avg_response_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
        max_response_time = max(r['response_time'] for r in successful_tests)
        min_response_time = min(r['response_time'] for r in successful_tests)

        print(f"\n性能统计:")
        print(f"   平均响应时间: {avg_response_time:.2f}ms")
        print(f"   最快响应: {min_response_time:.2f}ms")
        print(f"   最慢响应: {max_response_time:.2f}ms")
        print(f"   成功率: {len(successful_tests)}/{len(endpoints)} ({len(successful_tests)/len(endpoints)*100:.1f}%)")

def test_error_handling():
    """测试错误处理"""
    print_separator("错误处理测试")

    # 测试不存在的接口
    try:
        response = requests.get(f"{BASE_URL}/nonexistent", timeout=10)
        if response.status_code == 404:
            print("[PASS] 404错误处理正常")
        else:
            print(f"[WARN] 意外的响应码: {response.status_code}")
    except Exception as e:
        print(f"[FAIL] 错误处理测试失败: {str(e)}")

    # 测试无效参数
    try:
        response = requests.get(f"{BASE_URL}/storage?invalid_param=test", timeout=10)
        if response.status_code in [200, 400]:
            print("[PASS] 参数处理正常")
        else:
            print(f"[WARN] 意外的响应码: {response.status_code}")
    except Exception as e:
        print(f"[FAIL] 参数测试失败: {str(e)}")

def generate_test_report(results):
    """生成测试报告"""
    print_separator("测试报告")

    total_tests = len(results)
    passed_tests = sum(1 for result in results if result)
    failed_tests = total_tests - passed_tests

    print(f"测试总结:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   失败: {failed_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")

    if failed_tests > 0:
        print(f"\n建议检查:")
        print(f"   1. Flask服务是否正在运行 (python app.py)")
        print(f"   2. API地址是否正确 ({BASE_URL})")
        print(f"   3. 网络连接是否正常")
        print(f"   4. 防火墙设置是否阻止连接")

def main():
    """主测试函数"""
    print_separator("容量监控API接口测试", "=", 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"API地址: {BASE_URL}")
    print()

    # 存储测试结果
    test_results = []

    # 执行各项测试
    test_results.append(test_health_check())
    test_results.append(test_storage_api())
    test_results.append(test_database_api())
    test_results.append(test_container_api())
    test_results.append(test_virtualization_api())

    # 性能测试
    test_api_performance()

    # 错误处理测试
    test_error_handling()

    # 生成测试报告
    generate_test_report(test_results)

    print_separator("测试完成", "=", 80)

    return all(test_results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
