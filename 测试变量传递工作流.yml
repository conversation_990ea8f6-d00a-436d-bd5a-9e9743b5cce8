app:
  description: 简化测试工作流 - 用于调试变量传递
  icon: 🧪
  icon_background: '#FFEAD5'
  mode: workflow
  name: 测试变量传递工作流
kind: app
version: 0.1.2
workflow:
  graph:
    edges:
    - id: start-test-export
      source: start
      target: test-export
      type: custom
    nodes:
    - data:
        desc: 测试开始节点
        selected: false
        title: 开始
        type: start
        variables:
        - label: 测试内容
          max_length: 1000
          required: true
          type: text-input
          variable: test_content
          default: 这是一个测试内容
        - label: 系统名称
          max_length: 100
          required: true
          type: text-input
          variable: system_name
          default: 测试系统
      id: start
      type: custom
    - data:
        body:
          data: "{\n  \"report_content\": \"{{#start.test_content#}}\",\n  \"report_date\"\
            : \"2025-07-01\",\n  \"system_name\": \"{{#start.system_name#}}\",\n \
            \ \"save_path\": \"D:/work/LLM/reports/\"\n}"
          type: json
        headers: 'Content-Type: application/json'
        method: post
        title: 测试导出
        type: http-request
        url: http://127.0.0.1:5000/api/export_word
      id: test-export
      type: custom
