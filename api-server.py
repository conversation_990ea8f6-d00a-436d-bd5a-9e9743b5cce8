#!/usr/bin/env python3
from flask import Flask, request, jsonify
import subprocess

app = Flask(__name__)

def execute_command(command):
    """
    执行Linux命令并返回结果
    :param command: 要执行的命令
    :return: (return_code, stdout, stderr)
    """
    try:
        # 使用subprocess执行命令
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 获取输出和错误信息
        stdout, stderr = process.communicate()
        
        return process.returncode, stdout, stderr
    
    except Exception as e:
        print(f"执行命令时发生错误: {str(e)}")
        return 1, "", str(e)

@app.route('/execute', methods=['POST'])
def execute():
    data = request.json
    command = data.get('command')
    
    if not command:
        return jsonify({"error": "未提供命令"}), 400
    
    print(f"正在执行命令: {command}")
    print("-" * 50)
    
    # 执行命令
    return_code, stdout, stderr = execute_command(command)
    
    # 输出结果
    if stdout:
        print("命令输出:")
        print(stdout)
    
    if stderr:
        print("错误信息:")
        print(stderr)
    
    print(f"命令返回码: {return_code}")
    
    return jsonify({
        "return_code": return_code,
        "stdout": stdout,
        "stderr": stderr
    })

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)