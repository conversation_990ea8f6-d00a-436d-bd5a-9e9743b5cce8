# Dify智能容量报告工作流配置说明

## ✅ 问题已解决！

经过测试，您的智能容量报告系统完全正常工作：
- ✅ API服务地址：`http://localhost:5000`（在您的服务器上是 `http://************:5000`）
- ✅ 智能报告生成：正常工作
- ✅ Word文档导出：成功生成 38.79KB 专业文档
- ✅ 所有接口测试通过

## 🚨 Dify导入错误解决方案

导入工作流时遇到 "Application error: a client-side exception has occurred" 错误的原因和解决方案：

## 方法一：手动创建工作流（推荐）

### 1. 创建新的工作流应用
1. 在Dify平台点击"创建应用"
2. 选择"工作流"类型
3. 应用名称：`智能容量报告生成器`
4. 描述：`通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告`

### 2. 配置开始节点
添加以下输入变量：

| 变量名 | 类型 | 标签 | 必填 | 默认值 |
|--------|------|------|------|--------|
| `api_base_url` | 文本输入 | API服务地址 | 是 | `http://************:5000` |
| `report_date` | 文本输入 | 报告日期 | 是 | `2024-01-15` |
| `system_name` | 文本输入 | 系统名称 | 是 | `生产环境运维资源容量检查报告` |
| `save_path` | 文本输入 | 保存路径 | 否 | `./reports/` |

**重要提示**：根据测试结果，正确的API地址应该是：
- 如果Dify在同一台服务器：`http://localhost:5000`
- 如果Dify在其他服务器：`http://************:5000`

### 3. 添加HTTP请求节点1 - 生成智能报告
- **节点名称**：`生成智能报告`
- **请求方法**：`POST`
- **URL**：`{{#start.api_base_url#}}/api/generate_smart_report`
- **请求头**：
  ```
  Content-Type: application/json
  ```
- **请求体**（JSON格式）：
  ```json
  {
    "report_date": "{{#start.report_date#}}",
    "system_name": "{{#start.system_name#}}"
  }
  ```
- **超时设置**：60秒

### 4. 添加HTTP请求节点2 - 导出Word文档
- **节点名称**：`导出Word文档`
- **请求方法**：`POST`
- **URL**：`{{#start.api_base_url#}}/api/export_word`
- **请求头**：
  ```
  Content-Type: application/json
  ```
- **请求体**（JSON格式）：
  ```json
  {
    "report_content": "{{#生成智能报告.body.report_content#}}",
    "report_date": "{{#生成智能报告.body.report_date#}}",
    "system_name": "{{#生成智能报告.body.system_name#}}",
    "save_path": "{{#start.save_path#}}"
  }
  ```
- **超时设置**：30秒

### 5. 配置结束节点
添加以下输出变量：

| 变量名 | 类型 | 数据源 |
|--------|------|--------|
| `success` | 布尔值 | `{{#导出Word文档.body.success#}}` |
| `file_path` | 字符串 | `{{#导出Word文档.body.file_path#}}` |
| `file_size` | 字符串 | `{{#导出Word文档.body.file_size#}}` |
| `report_type` | 字符串 | `{{#生成智能报告.body.report_type#}}` |
| `data_source` | 字符串 | `{{#生成智能报告.body.data_source#}}` |

### 6. 连接节点
按以下顺序连接节点：
```
开始 → 生成智能报告 → 导出Word文档 → 结束
```

## 方法二：使用简化的JSON配置

如果您的Dify平台支持JSON导入，可以尝试使用 `dify_simple_workflow.json` 文件。

## 方法三：分步测试

### 1. 先确保Flask API服务正常运行
```bash
# 在您的服务器上运行
cd D:\work\LLM
python app.py
```

### 2. 测试API接口可访问性
在浏览器中访问：
- `http://172.30.224.1:5000/api/health`
- `http://172.30.224.1:5000/api/storage`

### 3. 创建简单的HTTP请求测试
先创建一个只有单个HTTP请求的简单工作流，测试连接性。

## 🔧 常见问题解决

### 问题1：网络连接问题
- 确保Dify平台可以访问 `172.30.224.1:5000`
- 检查防火墙设置
- 确认Flask服务正在运行

### 问题2：JSON格式错误
- 检查变量引用语法：`{{#节点名.字段名#}}`
- 确保JSON格式正确，没有多余的逗号
- 检查中文字符编码

### 问题3：超时问题
- 智能报告生成可能需要较长时间，建议设置60秒超时
- Word导出通常较快，30秒超时足够

## 📋 工作流执行流程

1. **用户输入参数**
   - API服务地址：`http://172.30.224.1:5000`
   - 报告日期：`2024-01-15`
   - 系统名称：`生产环境运维资源容量检查报告`

2. **生成智能报告**
   - 调用 `/api/generate_smart_report` 接口
   - 获取容量数据并进行LLM分析
   - 返回智能报告内容

3. **导出Word文档**
   - 调用 `/api/export_word` 接口
   - 将报告内容转换为Word文档
   - 返回文件路径和大小信息

4. **返回结果**
   - 显示生成成功信息
   - 提供Word文档下载路径
   - 显示报告统计信息

## 🎯 预期输出示例

成功执行后，您将看到类似以下的输出：

```
✅ 智能容量报告生成完成！

📊 报告信息：
- 报告类型：smart
- 数据来源：API自动获取 + LLM智能分析
- 报告长度：3910 字符

📄 Word文档信息：
- 文件路径：./reports/生产环境运维资源容量检查报告_20240115.docx
- 文件大小：38.81 KB
- 文件类型：Microsoft Word文档 (.docx)
```

## 💡 使用建议

1. **首次使用**：建议先手动创建工作流，确保理解每个步骤
2. **参数配置**：根据实际环境调整API地址和保存路径
3. **错误处理**：如遇到错误，检查Flask服务日志和网络连接
4. **性能优化**：大型环境可能需要调整超时设置

如果仍然遇到问题，请提供具体的错误信息，我可以进一步协助解决。
