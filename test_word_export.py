#!/usr/bin/env python3
"""
测试Word导出功能的独立脚本
用于验证Markdown到Word的转换功能
"""

import os
import re
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.ns import qn
from docx.shared import RGBColor

def markdown_to_word(markdown_content, report_date, system_name, save_path):
    """
    将Markdown格式的容量报告转换为Word文档
    """
    # 创建Word文档
    doc = Document()
    
    # 设置文档样式
    style = doc.styles['Normal']
    font = style.font
    font.name = '微软雅黑'
    font.size = Pt(10.5)
    
    # 添加标题
    title = doc.add_heading(system_name, 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加报告信息
    info_para = doc.add_paragraph()
    info_para.add_run(f"报告日期：{report_date}").bold = True
    info_para.add_run(f"\n生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 分割内容为行
    lines = markdown_content.split('\n')
    current_table = None
    table_headers = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 处理标题
        if line.startswith('# '):
            doc.add_heading(line[2:], 1)
        elif line.startswith('## '):
            doc.add_heading(line[3:], 2)
        elif line.startswith('### '):
            doc.add_heading(line[4:], 3)
        
        # 处理表格
        elif line.startswith('|') and '|' in line:
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            
            if not current_table:
                # 创建新表格
                table_headers = cells
                current_table = doc.add_table(rows=1, cols=len(cells))
                current_table.style = 'Table Grid'
                current_table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # 设置表头
                header_row = current_table.rows[0]
                for i, header in enumerate(cells):
                    cell = header_row.cells[i]
                    cell.text = header
                    # 设置表头样式
                    for paragraph in cell.paragraphs:
                        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        for run in paragraph.runs:
                            run.font.bold = True
                            run.font.size = Pt(9)
            else:
                # 检查是否是分隔行
                if all(cell.startswith('-') for cell in cells):
                    continue
                
                # 添加数据行
                row = current_table.add_row()
                for i, cell_text in enumerate(cells):
                    if i < len(row.cells):
                        cell = row.cells[i]
                        cell.text = cell_text
                        # 设置单元格样式
                        for paragraph in cell.paragraphs:
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            for run in paragraph.runs:
                                run.font.size = Pt(9)
        else:
            # 结束当前表格
            if current_table:
                current_table = None
                table_headers = []
            
            # 处理普通段落
            if line.startswith('**') and line.endswith('**'):
                # 粗体段落
                para = doc.add_paragraph()
                para.add_run(line[2:-2]).bold = True
            elif line.startswith('- '):
                # 列表项
                para = doc.add_paragraph(line[2:], style='List Bullet')
            else:
                # 普通段落
                if line:
                    doc.add_paragraph(line)
    
    # 确保保存路径存在
    os.makedirs(save_path, exist_ok=True)
    
    # 生成文件名
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
    filename = f"{safe_name}_{report_date.replace('-', '')}.docx"
    filepath = os.path.join(save_path, filename)
    
    # 保存文档
    doc.save(filepath)
    
    return filepath

def test_word_export():
    """测试Word导出功能"""
    
    # 示例Markdown报告内容
    sample_markdown = """# 生产环境运维资源容量检查报告

数据来源：API自动获取

## 1. 存储资源容量及健康度排查

存储资源池本次排查情况如下：

| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|-------------|------------|-------------------|------------------------|----------|
| 1 | 嘉兴中端虚拟化存储池 | 769034 | 89.11 | -0.72% | 否 | 无需措施 |
| 2 | 后沙峪中端虚拟化存储池 | 5200518 | 67.24 | +2.10% | 否 | 无需措施 |
| 3 | 嘉兴中端数据库存储池 | 822628 | 35.44 | +0.49% | 否 | 无需措施 |

**健康度说明：**
- 绿色：正常值 （存储使用率<90%）运行良好。
- 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 所有存储池运行正常，使用率均在安全范围内。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 2. 数据库资源容量及健康度排查

数据库资源池本次排查情况如下：

| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|-------------|------------|-------------------|------------------------|----------|
| 1 | 嘉兴Oracle生产库 | 8192000 | 77.5 | +0.8% | 否 | 无需措施 |
| 2 | 后沙峪MySQL集群主库 | 4096000 | 70.0 | +0.5% | 否 | 无需措施 |

**健康度说明：**
- 绿色：正常值 （数据库使用率<85%）运行良好。
- 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 所有数据库实例运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 3. 容器资源容量及健康度排查

容器资源池本次排查情况如下：

| 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
| 1 | 嘉兴K8S生产集群 | 65.0 | 70.0 | 70.0 | CPU+1.2% 内存+0.8% 存储+2.1% | 否 | 无需措施 |
| 2 | 后沙峪TAP容器集群 | 71.9 | 67.9 | 82.0 | CPU+2.1% 内存+1.5% 存储+3.2% | 否 | 关注存储使用率 |

**健康度说明：**
- 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。
- 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 容器集群整体运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 4. 虚拟化资源容量及健康度排查

虚拟化资源池本次排查情况如下：

| 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
| 1 | 嘉兴vSphere生产集群 | 60.0 | 75.0 | 70.0 | CPU+0.8% 内存+1.2% 存储+1.5% | 否 | 无需措施 |
| 2 | 后沙峪Hyper-V集群 | 48.0 | 52.0 | 58.0 | CPU+0.7% 内存+1.0% 存储+1.3% | 否 | 无需措施 |

**健康度说明：**
- 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。
- 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 虚拟化集群运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

---

**报告总结：** 本次容量检查显示所有系统资源运行正常，未发现异常情况。"""

    # 测试参数
    report_date = "2024-01-15"
    system_name = "生产环境运维资源容量检查报告"
    save_path = "./reports/"
    
    try:
        print("开始测试Word导出功能...")
        
        # 转换并保存Word文档
        saved_file = markdown_to_word(sample_markdown, report_date, system_name, save_path)
        
        print(f"[PASS] Word文档生成成功")
        print(f"文件路径: {saved_file}")
        print(f"文件名: {os.path.basename(saved_file)}")
        print(f"文件大小: {os.path.getsize(saved_file) / 1024:.2f} KB")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Word文档生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 检查依赖包
    try:
        from docx import Document
        print("python-docx 包已安装")
    except ImportError:
        print("错误：需要安装 python-docx 包")
        print("请运行：pip install python-docx")
        exit(1)
    
    # 运行测试
    success = test_word_export()
    exit(0 if success else 1)
