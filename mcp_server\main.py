# -*- coding: utf-8 -*-
from mcp.server.fastmcp import FastMCP
from pydantic import Field
import httpx
import json
import os
import logging


logger = logging.getLogger("mcp")

settings = {
    "log_level": "DEBUG"
}

# 初始化mcp服务
mcp = FastMCP("hello-mcp-server", log_level="ERROR", settings=settings)

###### 工具函数 ######

import os
import subprocess
from typing import Optional


def execute_command(command: str) -> str:
    """执行系统命令并返回输出
    
    :param command: 要执行的命令
    :return: 命令执行的输出
    """
    try:
        # 使用subprocess模块而不是os.system以获取命令输出
        result = subprocess.run(
            command,
            shell=True,
            check=False,
            capture_output=True,
            text=True
        )
        
        # 组合标准输出和错误输出
        output = result.stdout
        if result.stderr:
            if output:
                output += "\n\n错误输出:\n" + result.stderr
            else:
                output = "错误输出:\n" + result.stderr
                
        # 如果没有输出，添加状态信息
        if not output:
            output = f"命令执行完成，返回状态码: {result.returncode}"
            
        return output
    except Exception as e:
        return f"命令执行过程中出错: {str(e)}"






###### 工具 ######

@mcp.tool()
async def get_weather(city: str = Field(description="要查询天气的城市名称")) -> str:
    """获取天气信息
    
    :param city: 城市名称
    :return: 天气信息
    """
    logger.info(f"获取天气: {city}")
    return f"当前{city}的天气是晴天,气温20度,有阵雨."

@mcp.tool()
async def command_execute(command: str = Field(description="要执行的命令")) -> str:
    """执行命令
    
    :param command: 要执行的命令
    :return: 执行结果
    """
    logger.info(f"执行命令: {command}")
    return execute_command(command)





def run():
    mcp.run(transport="sse")


if __name__ == "__main__":
   run()