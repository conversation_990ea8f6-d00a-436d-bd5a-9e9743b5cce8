# 容量报告API测试说明

## 测试脚本概述

我已经根据完善后的API接口文档修改了测试脚本，现在提供两个测试工具：

### 1. test_api.py - 详细测试脚本
**功能特点：**
- 🔍 详细的接口测试和数据展示
- 📊 性能测试和响应时间统计
- 🛡️ 错误处理测试
- 📋 完整的测试报告生成
- 🎨 美观的输出格式和图标

### 2. quick_test.py - 快速测试脚本
**功能特点：**
- ⚡ 快速验证所有接口可用性
- 📊 简洁的测试结果统计
- 🔍 可选的示例数据展示
- 💡 故障排除提示

## 主要改进内容

### 1. 数据展示优化
**原版本：** 只显示基本的状态和汇总信息
```python
print(f"状态: {data['status']}")
print(f"总数: {list(summary.values())[0]}")
```

**新版本：** 详细展示各类资源信息
```python
print(f"📦 存储池 {i}: {pool.get('pool_name', 'Unknown')}")
print(f"   🆔 ID: {pool.get('pool_id', 'Unknown')}")
print(f"   📍 位置: {pool.get('location', 'Unknown')}")
print(f"   💾 总容量: {format_capacity(pool.get('total_capacity_gb', 0))}")
```

### 2. 新增功能模块

#### 存储容量测试 (`test_storage_api`)
- 显示存储池详细信息（名称、ID、位置、类型）
- 展示厂商和型号信息
- 格式化容量显示（GB/TB自动转换）
- 健康状态和变化趋势

#### 数据库容量测试 (`test_database_api`)
- 数据库类型和版本信息
- CPU和内存使用率
- 连接数统计
- 环境和角色信息

#### 容器容量测试 (`test_container_api`)
- 集群类型和版本（Kubernetes/TAP）
- 节点数量和资源分配
- Pod和容器运行状态
- TAP平台特有信息

#### 虚拟化容量测试 (`test_virtualization_api`)
- 虚拟化平台类型（vSphere/Hyper-V）
- 物理和虚拟资源对比
- 虚拟机分布和状态
- 超分比例和HA配置

#### 性能测试 (`test_api_performance`)
- 响应时间统计
- 数据大小测量
- 成功率计算
- 性能基准对比

#### 错误处理测试 (`test_error_handling`)
- 404错误处理
- 无效参数处理
- 连接失败处理

### 3. 辅助功能

#### 格式化函数
```python
def format_capacity(gb_value):
    """自动转换GB/TB显示"""
    if gb_value >= 1024:
        return f"{gb_value/1024:.2f} TB"
    else:
        return f"{gb_value:.2f} GB"

def format_percentage(value):
    """格式化百分比显示"""
    return f"{value:.2f}%"
```

#### 美观输出
```python
def print_separator(title="", char="=", length=80):
    """打印分隔线和标题"""
    if title:
        title_line = f" {title} "
        padding = (length - len(title_line)) // 2
        print(char * padding + title_line + char * padding)
```

## 使用方法

### 基本测试
```bash
# 详细测试（推荐）
python test_api.py

# 快速测试
python quick_test.py

# 快速测试 + 示例数据
python quick_test.py --sample
```

### 测试前准备
1. **启动Flask服务**
   ```bash
   python app.py
   ```

2. **验证服务状态**
   ```bash
   curl http://localhost:5000/api/health
   ```

3. **运行测试**
   ```bash
   python test_api.py
   ```

## 测试输出示例

### 详细测试输出
```
================================ 容量监控API接口测试 ================================
🕐 测试时间: 2024-01-15 14:30:25
🌐 API地址: http://localhost:5000/api

=============================== 健康检查接口测试 ===============================
✅ 健康检查接口正常
📊 服务状态: healthy
🕐 响应时间: 2024-01-15T14:30:25.123456
💬 消息: 容量监控API服务运行正常
🔗 接口状态:
   ✅ storage: available
   ✅ database: available
   ✅ container: available
   ✅ virtualization: available

=============================== 存储容量接口测试 ===============================
✅ 存储容量接口正常
📊 状态: success
🕐 时间戳: 2024-01-15T14:30:25.234567

📦 存储池数量: 5

🏢 存储池 1: 嘉兴中端虚拟化存储池
   🆔 ID: JX-M-VM-Prod
   📍 位置: 嘉兴
   🏷️  类型: 中端虚拟化
   🏭 厂商: EMC Unity 500
   💾 总容量: 751.01 TB
   📈 已使用: 669.17 TB
   📉 可用: 81.84 TB
   📊 使用率: 89.11%
   📈 日变化: -0.72%
   🏥 健康状态: 正常
```

### 快速测试输出
```
🚀 快速API测试
==================================================
测试 健康检查... ✅ 通过
测试 存储容量... ✅ 通过
测试 数据库容量... ✅ 通过
测试 容器容量... ✅ 通过
测试 虚拟化容量... ✅ 通过

==================================================
📊 测试结果: 5/5 通过 (100.0%)
🎉 所有测试通过！API服务运行正常。
```

## 故障排除

### 常见问题

#### 1. 连接失败
```
❌ 健康检查接口连接失败: Connection refused
```
**解决方案：**
- 确认Flask服务已启动：`python app.py`
- 检查端口5000是否被占用
- 验证防火墙设置

#### 2. 超时错误
```
❌ 存储容量接口连接失败: Read timed out
```
**解决方案：**
- 增加超时时间
- 检查服务器性能
- 优化API响应速度

#### 3. 数据格式错误
```
⚠️ 响应异常: error
```
**解决方案：**
- 检查API返回的错误信息
- 验证数据源连接
- 查看Flask服务日志

### 调试技巧

#### 1. 详细错误信息
修改测试脚本，添加详细的错误输出：
```python
except Exception as e:
    print(f"❌ 详细错误: {type(e).__name__}: {str(e)}")
    import traceback
    traceback.print_exc()
```

#### 2. 网络诊断
```bash
# 检查端口连通性
telnet localhost 5000

# 检查HTTP响应
curl -v http://localhost:5000/api/health
```

#### 3. 服务状态检查
```bash
# 检查进程
ps aux | grep python

# 检查端口占用
netstat -tulpn | grep 5000
```

## 扩展建议

### 1. 添加压力测试
```python
def stress_test():
    """压力测试"""
    import concurrent.futures
    import threading
    
    def single_request():
        response = requests.get(f"{BASE_URL}/storage")
        return response.status_code == 200
    
    # 并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(single_request) for _ in range(100)]
        results = [f.result() for f in futures]
    
    success_rate = sum(results) / len(results) * 100
    print(f"压力测试成功率: {success_rate:.1f}%")
```

### 2. 添加数据验证
```python
def validate_data_structure(data, expected_fields):
    """验证数据结构"""
    for field in expected_fields:
        if field not in data:
            return False, f"缺少字段: {field}"
    return True, "数据结构正确"
```

### 3. 添加基准测试
```python
def benchmark_test():
    """基准测试"""
    benchmarks = {
        'health': 100,      # 100ms
        'storage': 500,     # 500ms
        'database': 800,    # 800ms
        'container': 600,   # 600ms
        'virtualization': 700  # 700ms
    }
    
    # 对比实际响应时间与基准
```

---

**测试脚本版本**: v2.0  
**更新时间**: 2024-01-15  
**兼容API版本**: v1.0
