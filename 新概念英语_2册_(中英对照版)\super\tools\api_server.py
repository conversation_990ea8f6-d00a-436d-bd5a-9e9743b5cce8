from flask import Blueprint, request, jsonify
import subprocess
import json

# 创建蓝图而不是Flask应用实例
api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/execute_command', methods=['POST'])
def execute_command():
    # 打印请求头
    print("Headers:")
    for key, value in request.headers.items():
        print(f"{key}: {value}")

    # 打印请求体
    print("Request Body:")
    raw_data = request.get_data(as_text=True)
    print(raw_data)

    # 从原始文本获取参数，而不是表单数据
    try:
        data = json.loads(raw_data)
    except json.JSONDecodeError:
        return jsonify({'error': '无效的JSON数据'}), 400

    command = data.get('command')
    timeout = data.get('timeout')
    if not command:
        return jsonify({'error': '未提供命令'}), 400
    
    # 如果timeout是字符串，需要转换为数字
    if timeout is not None:
        try:
            timeout = float(timeout)
        except ValueError:
            return jsonify({'error': '无效的超时值'}), 400
    
    try:
        result = subprocess.run(command, shell=True, text=True, capture_output=True, check=True, timeout=timeout)
        return result.stdout  # 返回标准输出，保留换行符
    except subprocess.TimeoutExpired:
        return "Error Code: 2887\nError Message: Command timed out after {} seconds.".format(timeout)
    except subprocess.CalledProcessError as e:
        return f"Error Code: 2887\nError Message: {e.stderr.strip()}"  # 发生错误时返回错误代码和错误信息

# 删除直接运行部分，因为我们将在main.py中注册这个蓝图

