#!/usr/bin/env python3
"""
快速API测试脚本
用于快速验证容量监控API的基本功能
"""

import requests
import json
import sys

# API基础URL
BASE_URL = "http://localhost:5000/api"

def quick_test():
    """快速测试所有API接口"""
    print("快速API测试")
    print("=" * 50)

    endpoints = [
        ("health", "健康检查"),
        ("storage", "存储容量"),
        ("database", "数据库容量"),
        ("container", "容器容量"),
        ("virtualization", "虚拟化容量")
    ]

    results = []

    for endpoint, name in endpoints:
        try:
            print(f"测试 {name}...", end=" ")
            response = requests.get(f"{BASE_URL}/{endpoint}", timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success' or data.get('status') == 'healthy':
                    print("[PASS]")
                    results.append(True)
                else:
                    print(f"[WARN] 响应异常: {data.get('status', 'unknown')}")
                    results.append(False)
            else:
                print(f"[FAIL] HTTP {response.status_code}")
                results.append(False)

        except requests.exceptions.ConnectionError:
            print("[FAIL] 连接失败")
            results.append(False)
        except requests.exceptions.Timeout:
            print("[FAIL] 超时")
            results.append(False)
        except Exception as e:
            print(f"[FAIL] 错误: {str(e)}")
            results.append(False)

    # 统计结果
    passed = sum(results)
    total = len(results)

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")

    if passed == total:
        print("所有测试通过！API服务运行正常。")
        return True
    else:
        print("部分测试失败，请检查:")
        print("   1. Flask服务是否启动: python app.py")
        print("   2. 服务地址是否正确: http://localhost:5000")
        print("   3. 防火墙是否阻止连接")
        return False

def show_sample_data():
    """显示示例数据"""
    print("\n获取示例数据:")
    print("-" * 30)

    try:
        # 获取存储数据示例
        response = requests.get(f"{BASE_URL}/storage", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'storage_pools' in data['data']:
                pools = data['data']['storage_pools']
                if pools:
                    pool = pools[0]
                    print(f"存储池示例: {pool.get('pool_name', 'Unknown')}")
                    print(f"   总容量: {pool.get('total_capacity_gb', 0):,} GB")
                    print(f"   使用率: {pool.get('usage_rate', 0):.2f}%")
                    print(f"   状态: {pool.get('health_status', 'Unknown')}")

        # 获取数据库数据示例
        response = requests.get(f"{BASE_URL}/database", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'database_instances' in data['data']:
                instances = data['data']['database_instances']
                if instances:
                    db = instances[0]
                    print(f"\n数据库示例: {db.get('db_name', 'Unknown')}")
                    print(f"   类型: {db.get('db_type', 'Unknown')}")
                    print(f"   总容量: {db.get('total_capacity_gb', 0):,} GB")
                    print(f"   使用率: {db.get('usage_rate', 0):.2f}%")
                    print(f"   连接数: {db.get('connection_count', 0)}")

    except Exception as e:
        print(f"[FAIL] 获取示例数据失败: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--sample":
        # 显示示例数据
        if quick_test():
            show_sample_data()
    else:
        # 只进行快速测试
        success = quick_test()
        
        if not success:
            print("\n提示:")
            print("   运行 'python quick_test.py --sample' 查看示例数据")
            print("   运行 'python test_api.py' 进行详细测试")
        
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
