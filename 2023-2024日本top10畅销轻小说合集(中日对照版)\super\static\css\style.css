body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1c2c 0%, #2a3c54 100%);
    position: relative;
    overflow-x: hidden;
}

.chat-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.chat-header {
    position: relative;
    text-align: center;
    padding: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    border-radius: 10px;
    background: transparent;
    border: none;
}

.animated-background {
    background: linear-gradient(120deg, 
        rgba(21, 87, 153, 0.9), 
        rgba(21, 153, 87, 0.9));
}

.header-content {
    position: relative;
    z-index: 2;
    color: white;
}

.header-content h1 {
    margin: 0;
    font-size: 2em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

.header-content p {
    margin: 10px 0 0;
    opacity: 0.9;
}

.messages-container {
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.message {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeIn 0.5s ease forwards;
    color: rgba(255, 255, 255, 0.9);
}

.user-message {
    background: rgba(255, 255, 255, 0.9);
    border-left: 4px solid #155799;
    color: #333;
}

.assistant-message {
    background: rgba(255, 255, 255, 0.9);
    border-left: 4px solid #159957;
    color: #333;
}

.loading-animation {
    display: none;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    background-color: #f7f7f8;
}

.loading-dots {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    animation: pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
    animation-delay: 0.2s;
}

.dot:nth-child(3) {
    animation-delay: 0.4s;
}

.input-area {
    position: sticky;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
}

#question {
    flex: 1;
    padding: 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    font-size: 16px;
    resize: none;
    height: 24px;
    max-height: 200px;
    overflow-y: auto;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

#question::placeholder {
    color: rgba(0, 0, 0, 0.5);
}

button {
    padding: 10px 20px;
    background: linear-gradient(120deg, 
        rgba(21, 87, 153, 0.9), 
        rgba(21, 153, 87, 0.9));
    backdrop-filter: blur(5px);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Markdown styles */
.markdown-body {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

.markdown-body h1, 
.markdown-body h2, 
.markdown-body h3 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
}

.markdown-body code {
    background-color: rgba(0, 0, 0, 0.05);
    color: #333;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.markdown-body pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
}

.markdown-body pre code {
    background-color: transparent;
    padding: 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-container {
        padding: 10px;
    }
    
    .input-area {
        padding: 10px;
    }
}

/* Scrollbar styles */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.messages-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Text colors */
.message, 
.header-content p, 
#question,
.markdown-body,
.message *,  /* 确保消息内的所有元素都使用深色 */
textarea,
.user-message,
.assistant-message {
    color: #333 !important;  /* 使用 !important 确保覆盖其他样式 */
}

/* 保持代码块的特殊样式 */
.markdown-body pre code {
    color: #333 !important;
}

/* 确保链接颜色仍然可见 */
a {
    color: #155799 !important;
}

/* 保持按钮文字为白色 */
button {
    color: #fff !important;
}

/* Glass hover effect */
.message:hover,
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 添加下载链接样式 */
.download-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.download-link {
    display: inline-block;
    padding: 8px 16px;
    background: linear-gradient(120deg, 
        rgba(21, 87, 153, 0.9), 
        rgba(21, 153, 87, 0.9));
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.download-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.download-link:active {
    transform: translateY(0);
}

/* 应用容器布局 */
.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    color: white;
}

.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.2em;
    font-weight: normal;
}

.new-chat-btn {
    width: 100%;
    padding: 10px;
    margin-top: 15px;
    background: linear-gradient(120deg, 
        rgba(21, 87, 153, 0.9), 
        rgba(21, 153, 87, 0.9));
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.new-chat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.new-chat-btn span {
    font-size: 1.2em;
}

.conversations-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.conversation-item {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.conversation-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.conversation-item.active {
    background: rgba(21, 87, 153, 0.3);
    border-left: 3px solid #159957;
}

.conversation-title {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 25px; /* 为删除按钮腾出空间 */
}

.conversation-date {
    font-size: 0.8em;
    opacity: 0.7;
    margin-top: 5px;
}

.delete-conversation {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 0, 0, 0.3);
    color: white;
    border: none;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
}

.conversation-item:hover .delete-conversation {
    opacity: 1;
}

.delete-conversation:hover {
    background: rgba(255, 0, 0, 0.7);
}

/* 调整聊天容器 */
.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* 响应式布局 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 30vh;
    }
    
    .chat-container {
        height: 70vh;
    }
} 