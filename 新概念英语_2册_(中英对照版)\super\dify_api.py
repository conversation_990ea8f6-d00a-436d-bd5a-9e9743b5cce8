import requests
import json
import subprocess


# 调用workflow的方法
def run_workflow(token,user,question):
    """
    调用工作流接口的函数
    参数：
    token (str): 认证令牌，默认为示例令牌
    
    返回：
    dict: 如果返回数据包含 return_exec，则返回其值，否则返回完整响应 JSON
    """
    
    url = "http://172.30.224.1/v1/workflows/run"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {
        "inputs": {"question": question},
        "response_mode": "streaming",
        "user": user
    }
    response = requests.post(url, headers=headers, json=payload, stream=True)
    try:
        # 逐行读取流式数据
        for line in response.iter_lines():
            if line:
                # 每行数据都是以 "data: " 开头的 JSON 字符串
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    # 解析 data 部分的 JSON
                    json_data = json.loads(line[len('data: '):])
                    # 检查并提取 exec_return
                    if 'data' in json_data and 'outputs' in json_data['data']:
                        outputs = json_data['data']['outputs']
                        if 'exec_return' in outputs:
                            return outputs['exec_return']
                    else:
                        # 继续处理或返回完整的数据
                        print(json_data)  # 打印每次接收到的数据
        return {"error": "No exec_return found in the response"}
    
    except requests.exceptions.JSONDecodeError:
        return {"error": "Invalid JSON response", "text": response.text}
    
