#!/usr/bin/env python3
"""
测试HTML导出功能（Dify工作流中使用的Word导出方法）
"""

import os
import re
from datetime import datetime

def markdown_to_html_word(markdown_content, report_date, system_name, save_path):
    """
    将Markdown格式的容量报告转换为HTML文档（可用Word打开）
    """
    # 确保保存路径存在
    if not save_path or save_path.strip() == "":
        save_path = "D:/work/LLM/reports/"
    
    os.makedirs(save_path, exist_ok=True)
    
    # 生成文件名
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
    filename = f"{safe_name}_{report_date.replace('-', '')}.html"
    filepath = os.path.join(save_path, filename)
    
    # HTML模板
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{system_name}</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            font-size: 12pt;
        }}
        h1 {{
            text-align: center;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 18pt;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            margin-top: 30px;
            font-size: 14pt;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10pt;
        }}
        th, td {{
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }}
        th {{
            background-color: #f0f0f0;
            font-weight: bold;
            color: #2c3e50;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .report-header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
        }}
        .health-section {{
            background-color: #e8f5e8;
            padding: 10px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
        }}
        .status-section {{
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }}
        p {{
            margin: 10px 0;
        }}
        ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
    </style>
</head>
<body>
    <div class="report-header">
        <h1>{system_name}</h1>
        <p><strong>报告日期：</strong>{report_date}</p>
        <p><strong>生成时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
"""
    
    # 转换Markdown内容为HTML
    lines = markdown_content.split('\n')
    in_table = False
    table_html = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 处理标题
        if line.startswith('# '):
            continue  # 跳过主标题，已在header中处理
        elif line.startswith('## '):
            html_content += f"<h2>{line[3:]}</h2>\n"
        elif line.startswith('### '):
            html_content += f"<h3>{line[4:]}</h3>\n"
        
        # 处理表格
        elif line.startswith('|') and '|' in line:
            if not in_table:
                in_table = True
                table_html = "<table>\n"
            
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            
            # 检查是否是分隔行
            if all(cell.startswith('-') for cell in cells):
                continue
            
            # 判断是否是表头
            if '资源池' in line or '存储资源池名称' in line or '数据库资源池名称' in line or '容器资源池名称' in line or '虚拟化资源池名称' in line:
                table_html += "<tr>"
                for cell in cells:
                    table_html += f"<th>{cell}</th>"
                table_html += "</tr>\n"
            else:
                table_html += "<tr>"
                for cell in cells:
                    table_html += f"<td>{cell}</td>"
                table_html += "</tr>\n"
        else:
            # 结束表格
            if in_table:
                table_html += "</table>\n"
                html_content += table_html
                in_table = False
                table_html = ""
            
            # 处理其他内容
            if line.startswith('**') and line.endswith('**'):
                if '健康度说明' in line:
                    html_content += f'<div class="health-section"><strong>{line[2:-2]}</strong></div>\n'
                elif '今日状态' in line or '发现问题详情' in line or '应对措施和预案' in line:
                    html_content += f'<div class="status-section"><strong>{line[2:-2]}</strong></div>\n'
                else:
                    html_content += f"<p><strong>{line[2:-2]}</strong></p>\n"
            elif line.startswith('- '):
                html_content += f"<ul><li>{line[2:]}</li></ul>\n"
            elif line.startswith('数据来源：'):
                html_content += f'<p><strong>{line}</strong></p>\n'
            else:
                if line and not line.startswith('---'):
                    html_content += f"<p>{line}</p>\n"
    
    # 结束最后的表格
    if in_table:
        table_html += "</table>\n"
        html_content += table_html
    
    html_content += """
</body>
</html>
"""
    
    # 保存HTML文件
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return filepath

def test_html_export():
    """测试HTML导出功能"""
    
    # 示例Markdown报告内容
    sample_markdown = """# 生产环境运维资源容量检查报告

数据来源：API自动获取

## 1. 存储资源容量及健康度排查

存储资源池本次排查情况如下：

| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|-------------|------------|-------------------|------------------------|----------|
| 1 | 嘉兴中端虚拟化存储池 | 769034 | 89.11 | -0.72% | 否 | 无需措施 |
| 2 | 后沙峪中端虚拟化存储池 | 5200518 | 67.24 | +2.10% | 否 | 无需措施 |

**健康度说明：**
- 绿色：正常值 （存储使用率<90%）运行良好。
- 黄色：观察值 （存储使用率90%~95%）需要关注。
- 红色：警告值：(存储使用率>95%) 资源不足。

**今日状态：** 所有存储池运行正常，使用率均在安全范围内。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 2. 数据库资源容量及健康度排查

数据库资源池本次排查情况如下：

| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|-------------|------------|-------------------|------------------------|----------|
| 1 | 嘉兴Oracle生产库 | 8192000 | 77.5 | +0.8% | 否 | 无需措施 |
| 2 | 后沙峪MySQL集群主库 | 4096000 | 70.0 | +0.5% | 否 | 无需措施 |

**健康度说明：**
- 绿色：正常值 （数据库使用率<85%）运行良好。

**今日状态：** 所有数据库实例运行正常。

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及"""

    # 测试参数
    report_date = "2024-01-15"
    system_name = "生产环境运维资源容量检查报告"
    save_path = "./reports/"
    
    try:
        print("开始测试HTML导出功能...")
        
        # 转换并保存HTML文档
        saved_file = markdown_to_html_word(sample_markdown, report_date, system_name, save_path)
        
        print(f"[PASS] HTML文档生成成功")
        print(f"文件路径: {saved_file}")
        print(f"文件名: {os.path.basename(saved_file)}")
        print(f"文件大小: {os.path.getsize(saved_file) / 1024:.2f} KB")
        print(f"文件类型: HTML (可用Word打开)")
        
        # 验证文件内容
        with open(saved_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if '生产环境运维资源容量检查报告' in content and '<table>' in content:
                print("[PASS] 文件内容验证通过")
            else:
                print("[WARN] 文件内容可能不完整")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] HTML文档生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("HTML导出功能测试（Dify工作流Word导出方法）")
    print("=" * 60)
    
    # 运行测试
    success = test_html_export()
    
    if success:
        print("\n[PASS] HTML导出测试通过")
        print("提示：生成的HTML文件可以直接用Microsoft Word打开")
        print("      在Word中可以另存为.docx格式")
    else:
        print("\n[FAIL] HTML导出测试失败")
    
    exit(0 if success else 1)
