app:
  description: '通过API获取容量参数，LLM分析后按规定格式生成报告，并导出Word文档'
  icon: 📊
  icon_background: '#2563EB'
  mode: workflow
  name: 智能容量报告系统
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.1.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: '🎉 欢迎使用智能容量报告系统！\n\n✨ 系统功能：\n🔍 自动获取容量参数（存储、数据库、容器、虚拟化）\n🧠 LLM智能分析和风险评估\n📊 按规定格式生成专业报告\n📄 导出为Word文档格式\n\n📋 使用流程：\n1. 配置API服务地址\n2. 设置报告日期和系统名称\n3. 选择是否启用LLM分析\n4. 点击运行开始生成报告\n\n⚡ 预计处理时间：2-3分钟\n📁 文档保存位置：./reports/ 目录'
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - '生成今日的生产环境容量报告'
    - '使用LLM分析生成智能容量报告'
    - '生成包含风险评估的专业报告'
    - '导出Word格式的容量检查报告'
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        sourceType: start
        targetType: http-request
      id: start-generate-report
      source: start
      sourceHandle: source
      target: generate-report
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: http-request
      id: generate-report-export-word
      source: generate-report
      sourceHandle: source
      target: export-word
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: answer
      id: export-word-answer
      source: export-word
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: '配置智能容量报告系统参数'
        selected: false
        title: 开始
        type: start
        variables:
        - label: API服务地址
          max_length: 200
          options: []
          required: true
          type: text-input
          variable: api_base_url
          default: "http://192.168.233.119:5000"
        - label: 报告日期
          max_length: 50
          options: []
          required: true
          type: text-input
          variable: report_date
          default: "2025-06-30"
        - label: 系统名称
          max_length: 100
          options: []
          required: true
          type: text-input
          variable: system_name
          default: "生产环境运维资源容量检查报告"
        - label: 启用LLM分析
          options:
          - label: 启用LLM智能分析
            value: "true"
          - label: 使用本地分析
            value: "false"
          required: true
          type: select
          variable: enable_llm
          default: "false"
        - label: LLM API地址
          max_length: 200
          options: []
          required: false
          type: text-input
          variable: llm_api_url
          default: ""
        - label: LLM API密钥
          max_length: 200
          options: []
          required: false
          type: text-input
          variable: llm_api_key
          default: ""
      height: 89
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data: |
            {
              "report_date": "{{#start.report_date#}}",
              "system_name": "{{#start.system_name#}}",
              "llm_config": {
                "enabled": {{#start.enable_llm#}},
                "api_url": "{{#start.llm_api_url#}}",
                "api_key": "{{#start.llm_api_key#}}"
              }
            }
          type: json
        desc: '通过API获取容量数据并使用LLM分析生成智能报告'
        headers: 'Content-Type: application/json'
        method: post
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 120
          max_write_timeout: 10
        title: 生成智能报告
        type: http-request
        url: '{{#start.api_base_url#}}/api/generate_smart_report'
        variables: []
      height: 89
      id: generate-report
      position:
        x: 400
        y: 282
      positionAbsolute:
        x: 400
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data: |
            {
              "report_content": "{{#generate-report.body.report_content#}}",
              "report_date": "{{#generate-report.body.report_date#}}",
              "system_name": "{{#generate-report.body.system_name#}}",
              "save_path": "./reports/"
            }
          type: json
        desc: '将生成的智能报告导出为Word文档格式'
        headers: 'Content-Type: application/json'
        method: post
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 60
          max_write_timeout: 10
        title: 导出Word文档
        type: http-request
        url: '{{#start.api_base_url#}}/api/export_word'
        variables: []
      height: 89
      id: export-word
      position:
        x: 720
        y: 282
      positionAbsolute:
        x: 720
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |
          ## 🎉 智能容量报告生成完成！

          ### 📊 报告生成结果
          **生成状态：** {{#generate-report.body.success#}}
          **报告类型：** {{#generate-report.body.report_type#}}
          **数据来源：** {{#generate-report.body.data_source#}}
          **LLM分析：** {{#generate-report.body.llm_enabled#}}
          **报告长度：** {{#generate-report.body.report_length#}} 字符
          **阅读时间：** {{#generate-report.body.estimated_reading_time#}}

          ### 📄 Word文档导出结果
          **导出状态：** {{#export-word.body.success#}}
          **文件路径：** {{#export-word.body.file_path#}}
          **文件名称：** {{#export-word.body.file_name#}}
          **文件大小：** {{#export-word.body.file_size#}}
          **文件类型：** {{#export-word.body.file_type#}}

          ### 📋 报告内容预览
          {{#generate-report.body.report_content#}}

          ---

          **✅ 系统功能验证完成：**
          - ✅ 通过API获取容量参数
          - ✅ LLM智能分析处理
          - ✅ 按规定格式生成报告
          - ✅ 导出Word文档格式

          **📁 生成的文件可在以下位置找到：**
          `{{#export-word.body.file_path#}}`
        desc: '输出智能容量报告生成结果和Word文档信息'
        selected: false
        title: 报告完成
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1040
        y: 282
      positionAbsolute:
        x: 1040
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1
