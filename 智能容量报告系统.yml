version: "0.1.0"
kind: app
data:
  title: 智能容量报告系统
  description: 通过API获取容量参数，LLM分析后按规定格式生成报告，并导出Word文档
  icon: 📊
  icon_background: "#2563EB"
  mode: workflow
  workflow:
    graph:
      nodes:
        - id: start
          data:
            type: start
            title: 开始
            variables:
              - variable: api_base_url
                type: text-input
                label: API服务地址
                required: true
                default: http://***************:5000
                description: Flask API服务的基础URL地址
              - variable: report_date
                type: text-input
                label: 报告日期
                required: true
                default: "2024-01-15"
                description: 容量报告的日期，格式：YYYY-MM-DD
              - variable: system_name
                type: text-input
                label: 系统名称
                required: true
                default: 生产环境运维资源容量检查报告
                description: 容量报告的系统名称
              - variable: enable_llm
                type: select
                label: 启用LLM分析
                required: true
                default: "false"
                options:
                  - label: 启用LLM智能分析
                    value: "true"
                  - label: 使用本地分析
                    value: "false"
                description: 是否启用LLM增强分析功能
              - variable: llm_api_url
                type: text-input
                label: LLM API地址
                required: false
                default: ""
                description: LLM服务的API地址（启用LLM时必填）
              - variable: llm_api_key
                type: text-input
                label: LLM API密钥
                required: false
                default: ""
                description: LLM服务的API密钥（启用LLM时必填）
          position:
            x: 100
            y: 200
        
        - id: get_capacity_data
          data:
            type: http-request
            title: 获取容量数据
            method: get
            url: "{{#start.api_base_url#}}/api/health"
            headers: "Content-Type: application/json"
            timeout:
              read: 10
            description: 验证API服务连接状态
          position:
            x: 400
            y: 200
        
        - id: generate_smart_report
          data:
            type: http-request
            title: 生成智能报告
            method: post
            url: "{{#start.api_base_url#}}/api/generate_smart_report"
            headers: "Content-Type: application/json"
            body:
              type: json
              data: |
                {
                  "report_date": "{{#start.report_date#}}",
                  "system_name": "{{#start.system_name#}}",
                  "llm_config": {
                    "enabled": {{#start.enable_llm#}},
                    "api_url": "{{#start.llm_api_url#}}",
                    "api_key": "{{#start.llm_api_key#}}"
                  }
                }
            timeout:
              read: 120
            description: 通过API获取容量数据并使用LLM分析生成智能报告
          position:
            x: 700
            y: 200
        
        - id: export_word_document
          data:
            type: http-request
            title: 导出Word文档
            method: post
            url: "{{#start.api_base_url#}}/api/export_word"
            headers: "Content-Type: application/json"
            body:
              type: json
              data: |
                {
                  "report_content": "{{#generate_smart_report.body.report_content#}}",
                  "report_date": "{{#generate_smart_report.body.report_date#}}",
                  "system_name": "{{#generate_smart_report.body.system_name#}}",
                  "save_path": "./reports/"
                }
            timeout:
              read: 60
            description: 将生成的智能报告导出为Word文档格式
          position:
            x: 1000
            y: 200
        
        - id: end
          data:
            type: end
            title: 完成
            outputs:
              - variable: api_connection_status
                type: string
                value_selector:
                  - get_capacity_data
                  - body
                  - status
              - variable: report_generated
                type: boolean
                value_selector:
                  - generate_smart_report
                  - body
                  - success
              - variable: report_type
                type: string
                value_selector:
                  - generate_smart_report
                  - body
                  - report_type
              - variable: data_source
                type: string
                value_selector:
                  - generate_smart_report
                  - body
                  - data_source
              - variable: llm_analysis_enabled
                type: boolean
                value_selector:
                  - generate_smart_report
                  - body
                  - llm_enabled
              - variable: report_length
                type: number
                value_selector:
                  - generate_smart_report
                  - body
                  - report_length
              - variable: estimated_reading_time
                type: string
                value_selector:
                  - generate_smart_report
                  - body
                  - estimated_reading_time
              - variable: word_document_path
                type: string
                value_selector:
                  - export_word_document
                  - body
                  - file_path
              - variable: word_document_size
                type: string
                value_selector:
                  - export_word_document
                  - body
                  - file_size
              - variable: word_document_type
                type: string
                value_selector:
                  - export_word_document
                  - body
                  - file_type
              - variable: export_success
                type: boolean
                value_selector:
                  - export_word_document
                  - body
                  - success
          position:
            x: 1300
            y: 200
      
      edges:
        - source: start
          target: get_capacity_data
        - source: get_capacity_data
          target: generate_smart_report
        - source: generate_smart_report
          target: export_word_document
        - source: export_word_document
          target: end
  
  model_config:
    opening_statement: |
      🎉 欢迎使用智能容量报告系统！
      
      ✨ 系统功能：
      🔍 自动获取容量参数（存储、数据库、容器、虚拟化）
      🧠 LLM智能分析和风险评估
      📊 按规定格式生成专业报告
      📄 导出为Word文档格式
      
      📋 使用流程：
      1. 配置API服务地址
      2. 设置报告日期和系统名称
      3. 选择是否启用LLM分析
      4. 如启用LLM，请填写API配置
      5. 点击运行开始生成报告
      
      ⚡ 预计处理时间：2-3分钟
      📁 文档保存位置：./reports/ 目录
    
    suggested_questions:
      - 生成今日的生产环境容量报告
      - 使用LLM分析生成智能容量报告
      - 生成包含风险评估的专业报告
      - 导出Word格式的容量检查报告
