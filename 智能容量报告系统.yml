app:
  description: 通过API获取容量参数，LLM分析后按规定格式生成报告，并导出Word文档
  icon: 📊
  icon_background: '#2563EB'
  mode: workflow
  name: 智能容量报告系统
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 🎉 欢迎使用智能容量报告系统！\n\n✨ 系统功能：\n🔍 支持多种查询模式：\n  • 查询所有容量（存储+数据库+容器+虚拟化）\n  •
      只查询存储容量\n  • 只查询数据库容量\n  • 只查询容器容量\n  • 只查询虚拟化容量\n🧠 LLM智能分析和风险评估\n📊 按规定格式生成专业报告\n📄
      导出为Word文档格式\n\n📋 使用流程：\n1. 配置API服务地址\n2. 设置报告日期和系统名称\n3. 选择查询类型（全部或单项）\n4. 选择是否启用LLM分析\n5.
      点击运行开始生成报告\n\n⚡ 预计处理时间：1-3分钟（根据查询类型）\n📁 文档保存位置：./reports/ 目录
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 生成今日的完整容量报告（所有资源类型）
    - 只查询存储容量并生成专项报告
    - 只查询数据库容量并生成专项报告
    - 只查询容器容量并生成专项报告
    - 只查询虚拟化容量并生成专项报告
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: http-request
      id: start-get-data
      source: start
      sourceHandle: source
      target: get-capacity-data
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: llm
      id: get-data-llm-analysis
      source: get-capacity-data
      sourceHandle: source
      target: llm-analysis
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: answer
      id: export-word-answer
      source: export-word
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: llm-analysis-source-1751437927645-target
      source: llm-analysis
      sourceHandle: source
      target: '1751437927645'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: http-request
      id: 1751437927645-source-export-word-target
      source: '1751437927645'
      sourceHandle: source
      target: export-word
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 配置智能容量报告系统参数
        selected: false
        title: 开始
        type: start
        variables:
        - default: http://127.0.0.1:5000
          label: API服务地址
          max_length: 200
          options: []
          required: true
          type: text-input
          variable: api_base_url
        - default: '2025-06-30'
          label: 报告日期
          max_length: 50
          options: []
          required: true
          type: text-input
          variable: report_date
        - default: 生产环境运维资源容量检查报告
          label: 系统名称
          max_length: 100
          options: []
          required: true
          type: text-input
          variable: system_name
        - default: all
          label: 查询类型
          options:
          - all
          - storage
          - database
          - container
          - virtualization
          required: true
          type: select
          variable: query_type
        - default: 'true'
          label: 启用LLM分析
          options:
          - 'True'
          - 'False'
          required: true
          type: select
          variable: enable_llm
      height: 221
      id: start
      position:
        x: -130.13492974417252
        y: 114.5090375355316
      positionAbsolute:
        x: -130.13492974417252
        y: 114.5090375355316
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_date\": \"{{#start.report_date#}}\",\n  \"system_name\"\
              : \"{{#start.system_name#}}\",\n  \"query_type\": \"{{#start.query_type#}}\"\
              \n}\n"
          type: json
        desc: 根据查询类型获取对应的容量数据
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 60
          max_write_timeout: 10
        title: 获取容量数据
        type: http-request
        url: '{{#start.api_base_url#}}/api/get_capacity_data'
        variables: []
      height: 169
      id: get-capacity-data
      position:
        x: 289.0945774786811
        y: 114.5090375355316
      positionAbsolute:
        x: 289.0945774786811
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: LLM智能分析容量数据并生成专业报告
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: system-prompt
          role: system
          text: '你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。


            根据用户选择的查询类型，你需要生成对应的容量分析报告：

            - all: 生成包含存储、数据库、容器、虚拟化四个维度的完整报告

            - storage: 只生成存储资源容量报告

            - database: 只生成数据库资源容量报告

            - container: 只生成容器资源容量报告

            - virtualization: 只生成虚拟化资源容量报告


            请严格按照以下格式生成报告，确保表格格式正确，数据准确：


            # {{#start.system_name#}}


            **报告日期：** {{#start.report_date#}}

            **查询类型：** {{#start.query_type#}}

            **数据来源：** API自动获取 + LLM智能分析

            **生成时间：** {当前时间}


            ## 报告格式说明：


            ### 存储资源容量报告格式（当query_type为"all"或"storage"时使用）：

            ## 1. 存储资源容量及健康度排查


            存储资源池本次排查情况如下：


            | 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施
            |

            |--------|---------------|-------------|------------|-------------------|------------------------|----------|

            [根据存储容量数据填写表格]


            **健康度说明：**

            - 🟢 绿色：正常值 （存储使用率<90%）运行良好。

            - 🟡 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。

            - 🔴 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。


            **今日状态：** [基于使用率阈值分析存储池状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]


            ### 数据库资源容量报告格式（当query_type为"all"或"database"时使用）：

            ## 2. 数据库资源容量及健康度排查


            数据库资源池本次排查情况如下：


            | 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施
            |

            |--------|-----------------|-------------|------------|-------------------|------------------------|----------|

            [根据数据库容量数据填写表格]


            **健康度说明：**

            - 🟢 绿色：正常值 （数据库使用率<85%）运行良好。

            - 🟡 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。

            - 🔴 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。


            **今日状态：** [基于使用率阈值分析数据库状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]


            ### 容器资源容量报告格式（当query_type为"all"或"container"时使用）：

            ## 3. 容器资源容量及健康度排查


            容器资源池本次排查情况如下：


            | 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足
            | 对应措施 |

            |--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|

            [根据容器资源数据填写表格]


            **健康度说明：**

            - 🟢 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。

            - 🟡 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。

            - 🔴 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。


            **今日状态：** [基于使用率阈值分析容器集群状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]


            ### 虚拟化资源容量报告格式（当query_type为"all"或"virtualization"时使用）：

            ## 4. 虚拟化资源容量及健康度排查


            虚拟化资源池本次排查情况如下：


            | 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足
            | 对应措施 |

            |--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|

            [根据虚拟化资源数据填写表格]


            **健康度说明：**

            - 🟢 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。

            - 🟡 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。

            - 🔴 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。


            **今日状态：** [基于使用率阈值分析虚拟化集群状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]


            ### 总体风险评估（当query_type为"all"时使用，单项查询时可简化）：

            ## 5. 总体风险评估和建议


            **整体健康度评估：** [综合分析资源池的健康状况]

            **主要风险点：** [识别需要重点关注的资源池和风险]

            **优化建议：** [提供具体的容量优化和扩容建议]

            **下一步行动计划：** [制定具体的后续行动计划]


            ---


            **重要要求：**

            1. 根据query_type参数只生成对应类型的报告章节

            2. 如果query_type="storage"，只生成存储资源容量报告

            3. 如果query_type="database"，只生成数据库资源容量报告

            4. 如果query_type="container"，只生成容器资源容量报告

            5. 如果query_type="virtualization"，只生成虚拟化资源容量报告

            6. 如果query_type="all"，生成所有四个维度的完整报告

            7. 表格数据必须根据提供的容量数据准确填写

            8. 健康度评估要根据使用率阈值准确判断

            9. 输出格式为Markdown，确保表格格式正确

            '
        - id: user-prompt
          role: user
          text: '请根据以下容量数据生成专业的容量分析报告：


            **基本信息：**

            - 报告日期：{{#start.report_date#}}

            - 系统名称：{{#start.system_name#}}

            - 查询类型：{{#start.query_type#}}


            **容量数据：**

            {{#get-capacity-data.body#}}


            **重要说明：**

            根据查询类型"{{#start.query_type#}}"，请只生成对应的报告章节：

            - 如果是"all"：生成所有四个维度的完整报告

            - 如果是"storage"：只生成存储资源容量报告

            - 如果是"database"：只生成数据库资源容量报告

            - 如果是"container"：只生成容器资源容量报告

            - 如果是"virtualization"：只生成虚拟化资源容量报告


            请严格按照系统提示中的格式要求生成对应的容量报告，包含：

            1. 详细的数据表格

            2. 基于阈值的健康度评估

            3. 问题识别和风险分析

            4. 具体的应对措施和建议

            5. 如果是完整报告，包含总体风险评估和优化建议


            确保所有数据都准确反映在报告中，表格格式正确，分析专业深入。

            '
        selected: false
        title: LLM智能分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 117
      id: llm-analysis
      position:
        x: 711.8511973081294
        y: 114.5090375355316
      positionAbsolute:
        x: 711.8511973081294
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_content\": \"{{#1751437927645.text#}}\",\n  \"report_date\"\
              : \"{{#start.report_date#}}\",\n  \"system_name\": \"{{#start.system_name#}}\"\
              ,\n  \"save_path\": \"D:/work/LLM/reports/\"\n}\n"
          type: json
        desc: 将生成的智能报告导出为Word文档格式
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 60
          max_write_timeout: 10
        title: 导出Word文档
        type: http-request
        url: '{{#start.api_base_url#}}/api/export_word'
        variables: []
      height: 169
      id: export-word
      position:
        x: 1456.21143215179
        y: 114.5090375355316
      positionAbsolute:
        x: 1456.21143215179
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '## 🎉 智能容量报告生成完成！


          ### 📊 容量数据获取结果

          **查询类型：** {{#start.query_type#}}

          **数据获取状态：** {{#get-capacity-data.body.success#}}

          **数据来源：** API自动获取

          **获取时间：** {{#get-capacity-data.body.timestamp#}}


          ### 🧠 LLM智能分析结果

          **分析状态：** 完成

          **分析模型：** {{#llm-analysis.model#}}

          **分析类型：** 专业容量规划分析

          **启用LLM：** {{#start.enable_llm#}}

          **查询范围：**

          {% if start.query_type == "all" %}完整容量报告（存储+数据库+容器+虚拟化）

          {% elif start.query_type == "storage" %}存储容量专项报告

          {% elif start.query_type == "database" %}数据库容量专项报告

          {% elif start.query_type == "container" %}容器容量专项报告

          {% elif start.query_type == "virtualization" %}虚拟化容量专项报告

          {% endif %}


          ### 📄 Word文档导出结果

          **导出状态：** {{#export-word.body.success#}}

          **文件路径：** {{#export-word.body.file_path#}}

          **文件名称：** {{#export-word.body.file_name#}}

          **文件大小：** {{#export-word.body.file_size#}}

          **文件类型：** {{#export-word.body.file_type#}}


          ### 📋 智能分析报告内容

          {{#llm-analysis.text#}}


          ---


          **✅ 工作流执行完成：**

          - ✅ 根据查询类型获取对应容量数据

          - ✅ Dify LLM节点智能分析

          - ✅ 按专业格式生成专项报告

          - ✅ 导出Word文档格式


          **📁 生成的文件可在以下位置找到：**

          `{{#export-word.body.file_path#}}`


          **🎯 工作流优势：**

          - 🔄 支持多种查询模式，按需生成报告

          - 🧠 使用Dify原生LLM节点，无需外部API

          - 📊 专业的容量规划分析提示词

          - 📄 标准化的报告格式和健康度评估

          - 💾 自动化的Word文档生成

          '
        desc: 输出智能容量报告生成结果和Word文档信息
        selected: false
        title: 报告完成
        type: answer
        variables: []
      height: 897
      id: answer
      position:
        x: 1857.7023946162587
        y: 114.5090375355316
      positionAbsolute:
        x: 1857.7023946162587
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: 提取text中的文字内容
          name: text
          required: false
          type: string
        query:
        - llm-analysis
        - text
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1751437927645'
      position:
        x: 1083.189154957362
        y: 114.5090375355316
      positionAbsolute:
        x: 1083.189154957362
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 177.72968662233785
      y: 179.4657128732341
      zoom: 0.5888613947974906
