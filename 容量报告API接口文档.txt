## Flask API服务

### 功能特点

- **四个核心接口**：存储、数据库、容器(TAP)、虚拟化
- **自动计算**：使用率、剩余容量、健康状态
- **模拟数据**：包含真实的容量数据结构
- **健康度评估**：根据不同阈值自动判断状态

### API接口说明

#### 1. 存储容量接口
- **URL**: `GET /api/storage`
- **功能**: 获取存储资源池容量信息
- **数据**: 存储池容量信息，包括嘉兴和后沙峪的虚拟化、数据库、高端存储池，包括总容量、已使用容量、使用率及与前日对比信息

#### 2. 数据库容量接口
- **URL**: `GET /api/database`
- **功能**: 获取数据库实例容量信息
- **数据**: 所有数据库实例的容量使用情况、CPU及内存使用率、连接数等信息，包括与前日对比信息

#### 3. 容器容量接口
- **URL**: `GET /api/container`
- **功能**: 获取容器集群资源信息
- **数据**: 所有容器集群的宿主机数量、Pod数量、CPU/内存/存储使用峰值信息，包括与前日及一周前比较信息

#### 4. 虚拟化容量接口
- **URL**: `GET /api/virtualization`
- **功能**: 获取虚拟化集群资源信息
- **数据**: 所有虚拟化集群的CPU及内存使用信息、虚拟机分布情况，包括与一周前及前月比较信息


### 数据结构示例

#### 1. 存储容量接口响应示例

```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "storage_pools": [
      {
        "pool_name": "嘉兴中端虚拟化存储池",
        "pool_id": "JX-M-VM-Prod",
        "total_capacity_gb": 769034,
        "used_capacity_gb": 685234,
        "available_capacity_gb": 83800,
        "usage_rate": 89.11,
        "daily_change": -0.72,
        "weekly_change": 2.15,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "location": "嘉兴",
        "storage_type": "中端虚拟化",
        "vendor": "EMC",
        "model": "Unity 500"
      },
      {
        "pool_name": "后沙峪中端虚拟化存储池",
        "pool_id": "HSY-M-VM-Prod",
        "total_capacity_gb": 5200518,
        "used_capacity_gb": 3496748,
        "available_capacity_gb": 1703770,
        "usage_rate": 67.24,
        "daily_change": 2.10,
        "weekly_change": 5.32,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "location": "后沙峪",
        "storage_type": "中端虚拟化",
        "vendor": "Dell",
        "model": "PowerStore"
      },
      {
        "pool_name": "嘉兴中端数据库存储池",
        "pool_id": "JX-M-DB-Prod",
        "total_capacity_gb": 822628,
        "used_capacity_gb": 291539,
        "available_capacity_gb": 531089,
        "usage_rate": 35.44,
        "daily_change": 0.49,
        "weekly_change": 1.23,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "location": "嘉兴",
        "storage_type": "中端数据库",
        "vendor": "NetApp",
        "model": "FAS2750"
      }
    ],
    "summary": {
      "total_pools": 5,
      "normal_pools": 5,
      "warning_pools": 0,
      "total_capacity_tb": 7.55,
      "total_used_tb": 4.47,
      "overall_usage_rate": 59.2
    }
  }
}
```

#### 2. 数据库容量接口响应示例

```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "database_instances": [
      {
        "db_name": "嘉兴Oracle生产库",
        "db_id": "JX-ORA-PROD01",
        "db_type": "Oracle",
        "version": "19c",
        "total_capacity_gb": 8192000,
        "used_capacity_gb": 6348800,
        "available_capacity_gb": 1843200,
        "usage_rate": 77.5,
        "daily_change": 0.8,
        "weekly_change": 3.2,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "cpu_usage_rate": 65.2,
        "memory_usage_rate": 78.5,
        "connection_count": 150,
        "max_connections": 400,
        "tablespace_details": [
          {
            "tablespace_name": "USERS",
            "size_gb": 2048,
            "used_gb": 1587,
            "usage_rate": 77.5
          },
          {
            "tablespace_name": "SYSTEM",
            "size_gb": 1024,
            "used_gb": 512,
            "usage_rate": 50.0
          }
        ],
        "location": "嘉兴",
        "environment": "生产"
      },
      {
        "db_name": "后沙峪MySQL集群主库",
        "db_id": "HSY-MYSQL-MASTER",
        "db_type": "MySQL",
        "version": "8.0",
        "total_capacity_gb": 4096000,
        "used_capacity_gb": 2867200,
        "available_capacity_gb": 1228800,
        "usage_rate": 70.0,
        "daily_change": 0.5,
        "weekly_change": 2.1,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "cpu_usage_rate": 58.3,
        "memory_usage_rate": 72.1,
        "connection_count": 80,
        "max_connections": 200,
        "replication_lag": 0.2,
        "location": "后沙峪",
        "environment": "生产",
        "cluster_role": "主库"
      }
    ],
    "summary": {
      "total_instances": 7,
      "normal_instances": 7,
      "warning_instances": 0,
      "total_capacity_tb": 33.79,
      "total_used_tb": 23.65,
      "overall_usage_rate": 70.0
    }
  }
}
```

#### 3. 容器容量接口响应示例

```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "container_clusters": [
      {
        "cluster_name": "嘉兴K8S生产集群",
        "cluster_id": "JX-K8S-PROD",
        "cluster_type": "Kubernetes",
        "version": "1.28.2",
        "location": "嘉兴",
        "environment": "生产",
        "node_count": 12,
        "master_node_count": 3,
        "worker_node_count": 9,
        "cpu_total_cores": 480,
        "cpu_used_cores": 312,
        "cpu_available_cores": 168,
        "cpu_usage_rate": 65.0,
        "cpu_daily_change": 1.2,
        "cpu_weekly_change": 3.8,
        "cpu_peak_usage_rate": 78.5,
        "memory_total_gb": 1920,
        "memory_used_gb": 1344,
        "memory_available_gb": 576,
        "memory_usage_rate": 70.0,
        "memory_daily_change": 0.8,
        "memory_weekly_change": 2.5,
        "storage_total_gb": 60000,
        "storage_used_gb": 42000,
        "storage_available_gb": 18000,
        "storage_usage_rate": 70.0,
        "storage_daily_change": 2.1,
        "storage_weekly_change": 5.2,
        "pod_running_count": 450,
        "pod_total_count": 600,
        "container_running_count": 850,
        "container_total_count": 1200,
        "namespace_count": 15,
        "service_count": 120,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "pod_details": [
          {
            "namespace": "production",
            "pod_count": 180,
            "cpu_usage": "45%",
            "memory_usage": "52%"
          },
          {
            "namespace": "middleware",
            "pod_count": 120,
            "cpu_usage": "38%",
            "memory_usage": "41%"
          }
        ]
      },
      {
        "cluster_name": "后沙峪TAP容器集群",
        "cluster_id": "HSY-TAP-PROD",
        "cluster_type": "TAP",
        "version": "2.1.0",
        "location": "后沙峪",
        "environment": "生产",
        "node_count": 8,
        "cpu_total_cores": 320,
        "cpu_used_cores": 230,
        "cpu_available_cores": 90,
        "cpu_usage_rate": 71.9,
        "cpu_daily_change": 2.1,
        "cpu_weekly_change": 4.5,
        "cpu_peak_usage_rate": 85.2,
        "memory_total_gb": 1280,
        "memory_used_gb": 870,
        "memory_available_gb": 410,
        "memory_usage_rate": 67.9,
        "memory_daily_change": 1.5,
        "memory_weekly_change": 3.2,
        "storage_total_gb": 40000,
        "storage_used_gb": 32800,
        "storage_available_gb": 7200,
        "storage_usage_rate": 82.0,
        "storage_daily_change": 3.2,
        "storage_weekly_change": 8.1,
        "application_count": 45,
        "instance_count": 280,
        "health_status": "观察",
        "has_anomaly": false,
        "measures": "关注存储使用率，建议扩容",
        "tap_specific": {
          "platform_version": "TAP 2.1.0",
          "workload_count": 45,
          "supply_chain_count": 12,
          "developer_namespace_count": 8
        }
      }
    ],
    "summary": {
      "total_clusters": 4,
      "normal_clusters": 3,
      "warning_clusters": 1,
      "total_nodes": 32,
      "total_cpu_cores": 1680,
      "total_memory_gb": 6720,
      "total_storage_gb": 190000,
      "overall_cpu_usage": 67.2,
      "overall_memory_usage": 68.5,
      "overall_storage_usage": 74.3
    }
  }
}
```

#### 4. 虚拟化容量接口响应示例

```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "vm_clusters": [
      {
        "cluster_name": "嘉兴vSphere生产集群",
        "cluster_id": "JX-VSPHERE-PROD",
        "cluster_type": "VMware vSphere",
        "version": "8.0",
        "location": "嘉兴",
        "environment": "生产",
        "esxi_host_count": 8,
        "physical_cpu_cores": 320,
        "physical_memory_gb": 2560,
        "physical_storage_gb": 80000,
        "vcpu_allocated": 480,
        "vcpu_used": 192,
        "cpu_usage_rate": 60.0,
        "cpu_daily_change": 0.8,
        "cpu_weekly_change": 2.1,
        "cpu_monthly_change": 5.3,
        "vmemory_allocated_gb": 3200,
        "vmemory_used_gb": 1920,
        "memory_usage_rate": 75.0,
        "memory_daily_change": 1.2,
        "memory_weekly_change": 3.5,
        "memory_monthly_change": 8.2,
        "storage_allocated_gb": 70000,
        "storage_used_gb": 56000,
        "storage_usage_rate": 70.0,
        "storage_daily_change": 1.5,
        "storage_weekly_change": 4.2,
        "storage_monthly_change": 12.1,
        "vm_running_count": 95,
        "vm_total_count": 130,
        "vm_powered_off": 15,
        "vm_template_count": 20,
        "datastore_count": 12,
        "network_count": 8,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "overcommit_ratios": {
          "cpu_overcommit": 1.5,
          "memory_overcommit": 1.25
        },
        "ha_configuration": {
          "ha_enabled": true,
          "drs_enabled": true,
          "admission_control": "25%"
        },
        "vm_distribution": [
          {
            "host_name": "esxi-jx-01",
            "vm_count": 12,
            "cpu_usage": "58%",
            "memory_usage": "72%"
          },
          {
            "host_name": "esxi-jx-02",
            "vm_count": 11,
            "cpu_usage": "62%",
            "memory_usage": "78%"
          }
        ]
      },
      {
        "cluster_name": "后沙峪Hyper-V集群",
        "cluster_id": "HSY-HYPERV-PROD",
        "cluster_type": "Microsoft Hyper-V",
        "version": "2022",
        "location": "后沙峪",
        "environment": "生产",
        "host_count": 6,
        "physical_cpu_cores": 240,
        "physical_memory_gb": 1920,
        "physical_storage_gb": 60000,
        "vcpu_allocated": 300,
        "vcpu_used": 115,
        "cpu_usage_rate": 48.0,
        "cpu_daily_change": 0.7,
        "cpu_weekly_change": 1.8,
        "cpu_monthly_change": 4.2,
        "vmemory_allocated_gb": 2400,
        "vmemory_used_gb": 998,
        "memory_usage_rate": 52.0,
        "memory_daily_change": 1.0,
        "memory_weekly_change": 2.8,
        "memory_monthly_change": 6.5,
        "storage_allocated_gb": 45000,
        "storage_used_gb": 34800,
        "storage_usage_rate": 58.0,
        "storage_daily_change": 1.3,
        "storage_weekly_change": 3.8,
        "storage_monthly_change": 9.2,
        "vm_running_count": 42,
        "vm_total_count": 55,
        "vm_powered_off": 8,
        "vm_template_count": 5,
        "csv_count": 8,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施",
        "failover_cluster": {
          "cluster_enabled": true,
          "live_migration_enabled": true,
          "shared_storage": "CSV"
        }
      }
    ],
    "summary": {
      "total_clusters": 4,
      "normal_clusters": 4,
      "warning_clusters": 0,
      "total_hosts": 30,
      "total_physical_cpu_cores": 1280,
      "total_physical_memory_gb": 10240,
      "total_physical_storage_gb": 320000,
      "total_vms": 350,
      "running_vms": 274,
      "overall_cpu_usage": 54.2,
      "overall_memory_usage": 63.8,
      "overall_storage_usage": 64.1
    }
  }
}
```

#### 5. 健康检查接口响应示例

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00",
  "message": "容量监控API服务运行正常",
  "version": "1.0.0",
  "uptime": "72h 15m 30s",
  "endpoints": {
    "storage": "available",
    "database": "available",
    "container": "available",
    "virtualization": "available"
  }
}
```

### 字段说明

#### 通用字段
- `status`: 响应状态 ("success" | "error")
- `timestamp`: 数据生成时间戳 (ISO 8601格式)
- `data`: 具体数据内容

#### 容量相关字段
- `total_capacity_gb`: 总容量 (GB)
- `used_capacity_gb`: 已使用容量 (GB)
- `available_capacity_gb`: 可用容量 (GB)
- `usage_rate`: 使用率 (百分比)
- `daily_change`: 日变化率 (百分比)
- `weekly_change`: 周变化率 (百分比)
- `monthly_change`: 月变化率 (百分比)

#### 健康状态字段
- `health_status`: 健康状态 ("正常" | "观察" | "警告")
- `has_anomaly`: 是否存在异常 (boolean)
- `measures`: 应对措施 (string)

#### 资源特定字段
- `cpu_usage_rate`: CPU使用率 (百分比)
- `memory_usage_rate`: 内存使用率 (百分比)
- `storage_usage_rate`: 存储使用率 (百分比)
- `node_count`: 节点数量
- `pod_count`: Pod数量 (容器)
- `vm_count`: 虚拟机数量 (虚拟化)

### 错误响应示例

#### 服务不可用
```json
{
  "status": "error",
  "timestamp": "2024-01-15T10:30:00",
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "message": "数据源服务暂时不可用",
    "details": "无法连接到监控系统"
  }
}
```

#### 数据获取失败
```json
{
  "status": "error",
  "timestamp": "2024-01-15T10:30:00",
  "error": {
    "code": "DATA_FETCH_ERROR",
    "message": "部分数据获取失败",
    "details": "存储池 JX-M-VM-Prod 数据获取超时"
  }
}
```

#### 参数错误
```json
{
  "status": "error",
  "timestamp": "2024-01-15T10:30:00",
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "请求参数无效",
    "details": "不支持的查询参数: invalid_param"
  }
}
```

### 使用示例

#### 获取存储容量信息
```bash
curl -X GET "http://localhost:5000/api/storage" \
     -H "Content-Type: application/json"
```

#### 获取特定数据库信息
```bash
curl -X GET "http://localhost:5000/api/database?db_id=JX-ORA-PROD01" \
     -H "Content-Type: application/json"
```

#### 批量获取所有容量信息
```bash
# 存储
curl -X GET "http://localhost:5000/api/storage"

# 数据库
curl -X GET "http://localhost:5000/api/database"

# 容器
curl -X GET "http://localhost:5000/api/container"

# 虚拟化
curl -X GET "http://localhost:5000/api/virtualization"
```

### 注意事项

1. **数据时效性**: API返回的数据包含时间戳，建议定期刷新
2. **错误处理**: 调用API时应检查status字段，处理可能的错误情况
3. **性能考虑**: 大量数据查询时可能需要较长响应时间
4. **数据格式**: 所有容量数据以GB为单位，使用率以百分比表示
5. **健康状态**: 根据不同资源类型有不同的健康度阈值标准