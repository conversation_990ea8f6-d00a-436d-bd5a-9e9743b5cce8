#!/usr/bin/env python3
"""
测试HTTP API接口功能
验证基于HTTP请求的容量报告生成和Word导出功能
"""

import requests
import json
import time

def test_generate_report_api():
    """测试报告生成API"""
    print("=== 测试报告生成API ===")
    
    url = "http://localhost:5000/api/generate_report"
    data = {
        "report_date": "2024-01-15",
        "system_name": "生产环境运维资源容量检查报告",
        "api_base_url": "http://localhost:5000"
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("[PASS] 报告生成成功")
            print(f"成功状态: {result.get('success')}")
            print(f"报告日期: {result.get('report_date')}")
            print(f"系统名称: {result.get('system_name')}")
            print(f"数据来源: {result.get('data_source')}")
            
            # 显示报告内容的前500个字符
            report_content = result.get('report_content', '')
            if report_content:
                print(f"报告内容预览: {report_content[:500]}...")
                return result
            else:
                print("[WARN] 报告内容为空")
                return None
        else:
            print(f"[FAIL] 报告生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"[FAIL] 请求失败: {str(e)}")
        return None
    except Exception as e:
        print(f"[FAIL] 处理失败: {str(e)}")
        return None

def test_export_word_api(report_data):
    """测试Word导出API"""
    print("\n=== 测试Word导出API ===")
    
    if not report_data or not report_data.get('report_content'):
        print("[SKIP] 跳过Word导出测试，因为没有报告内容")
        return False
    
    url = "http://localhost:5000/api/export_word"
    data = {
        "report_content": report_data.get('report_content'),
        "report_date": report_data.get('report_date'),
        "system_name": report_data.get('system_name'),
        "save_path": "./reports/"
    }
    
    try:
        print(f"请求URL: {url}")
        print(f"请求数据大小: {len(json.dumps(data))} 字节")
        
        response = requests.post(url, json=data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("[PASS] Word导出成功")
            print(f"成功状态: {result.get('success')}")
            print(f"导出消息: {result.get('message')}")
            print(f"文件路径: {result.get('file_path')}")
            print(f"文件名: {result.get('file_name')}")
            print(f"文件大小: {result.get('file_size')}")
            print(f"文件类型: {result.get('file_type')}")
            print(f"使用说明: {result.get('note')}")
            return True
        else:
            print(f"[FAIL] Word导出失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"[FAIL] 请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"[FAIL] 处理失败: {str(e)}")
        return False

def test_api_health():
    """测试API健康状态"""
    print("=== 测试API健康状态 ===")
    
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("[PASS] API服务正常")
            print(f"状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            print(f"时间戳: {result.get('timestamp')}")
            return True
        else:
            print(f"[FAIL] API服务异常: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"[FAIL] API服务不可用: {str(e)}")
        return False

def test_complete_workflow():
    """测试完整的工作流程"""
    print("HTTP API容量报告生成器测试")
    print("=" * 60)
    
    # 1. 测试API健康状态
    if not test_api_health():
        print("\n[FAIL] API服务不可用，请先启动Flask服务")
        print("提示：运行 python app.py 启动API服务")
        return False
    
    print("\n" + "=" * 60)
    
    # 2. 测试报告生成
    report_data = test_generate_report_api()
    if not report_data:
        print("\n[FAIL] 报告生成失败，无法继续测试")
        return False
    
    print("\n" + "=" * 60)
    
    # 3. 测试Word导出
    export_success = test_export_word_api(report_data)
    if not export_success:
        print("\n[FAIL] Word导出失败")
        return False
    
    print("\n" + "=" * 60)
    print("[PASS] 完整工作流程测试通过")
    print("\n✅ 测试总结:")
    print("1. API服务健康检查 - 通过")
    print("2. 容量报告生成 - 通过")
    print("3. Word文档导出 - 通过")
    print("\n🎉 HTTP API版本的容量报告生成器工作正常！")
    print("\n📝 使用说明:")
    print("1. 在Dify中导入 dify_http_workflow.yml 工作流配置")
    print("2. 确保Flask API服务正在运行")
    print("3. 在工作流中填写相应参数即可生成报告")
    
    return True

def simulate_dify_workflow():
    """模拟Dify工作流的HTTP请求序列"""
    print("\n" + "=" * 60)
    print("=== 模拟Dify工作流HTTP请求序列 ===")
    
    # 模拟用户输入参数
    workflow_params = {
        "report_date": "2024-01-15",
        "system_name": "生产环境运维资源容量检查报告",
        "api_base_url": "http://localhost:5000",
        "save_path": "./reports/"
    }
    
    print(f"工作流参数: {json.dumps(workflow_params, ensure_ascii=False, indent=2)}")
    
    # 步骤1: 生成报告
    print("\n步骤1: 调用报告生成API...")
    report_response = requests.post(
        f"{workflow_params['api_base_url']}/api/generate_report",
        json={
            "report_date": workflow_params["report_date"],
            "system_name": workflow_params["system_name"],
            "api_base_url": workflow_params["api_base_url"]
        },
        timeout=30
    )
    
    if report_response.status_code != 200:
        print(f"[FAIL] 步骤1失败: {report_response.status_code}")
        return False
    
    report_data = report_response.json()
    print(f"[PASS] 步骤1成功，报告长度: {len(report_data.get('report_content', ''))} 字符")
    
    # 步骤2: 导出Word
    print("\n步骤2: 调用Word导出API...")
    export_response = requests.post(
        f"{workflow_params['api_base_url']}/api/export_word",
        json={
            "report_content": report_data.get('report_content'),
            "report_date": workflow_params["report_date"],
            "system_name": workflow_params["system_name"],
            "save_path": workflow_params["save_path"]
        },
        timeout=30
    )
    
    if export_response.status_code != 200:
        print(f"[FAIL] 步骤2失败: {export_response.status_code}")
        return False
    
    export_data = export_response.json()
    print(f"[PASS] 步骤2成功，文件: {export_data.get('file_name')}")
    
    # 模拟最终输出
    print("\n=== 模拟Dify工作流最终输出 ===")
    print("# 容量报告生成完成\n")
    print("## 📊 报告内容")
    print(report_data.get('report_content', '')[:1000] + "...")
    print("\n---\n")
    print("## 📁 Word文档导出结果")
    print(f"**导出状态：** {export_data.get('success')}")
    print(f"**文件信息：**")
    print(f"- 文件名：{export_data.get('file_name')}")
    print(f"- 文件路径：{export_data.get('file_path')}")
    print(f"- 导出说明：{export_data.get('note')}")
    
    return True

if __name__ == "__main__":
    # 运行完整测试
    success = test_complete_workflow()
    
    if success:
        # 模拟Dify工作流
        simulate_dify_workflow()
    
    exit(0 if success else 1)
