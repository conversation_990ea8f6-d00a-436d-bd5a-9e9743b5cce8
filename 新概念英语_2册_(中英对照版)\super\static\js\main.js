// 配置marked选项
marked.setOptions({
    highlight: function(code, lang) {
        if (window.hljs && lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        }
        return code;
    },
    breaks: true
});

// 自动调整文本框高度
const textarea = document.getElementById('question');
textarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// 添加消息到界面
function addMessage(content, isUser = false) {
    const messagesDiv = document.getElementById('messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
    
    if (isUser) {
        messageDiv.textContent = content;
    } else {
        try {
            // 检查内容类型
            console.log("Response content type:", typeof content);
            console.log("Response content:", content);
            
            // 处理对象类型的响应
            if (typeof content === 'object') {
                // 直接访问report和download_link
                const report = content.report || (content.output && content.output.report);
                const downloadLink = content.download_link || (content.output && content.output.download_link);
                
                if (report) {
                    // 确保report是字符串
                    const reportStr = typeof report === 'string' ? report : JSON.stringify(report);
                    const markdownContent = marked.parse(reportStr);
                    let htmlContent = `<div class="markdown-body">${markdownContent}</div>`;
                    
                    // 添加下载链接
                    if (downloadLink) {
                        htmlContent += `
                            <div class="download-section">
                                <a href="${downloadLink}" target="_blank" class="download-link">
                                    📄 下载完整报告(DOCX)
                                </a>
                            </div>
                        `;
                    }
                    
                    messageDiv.innerHTML = htmlContent;
                } else {
                    // 如果没有report字段，尝试将整个对象转换为字符串
                    messageDiv.innerHTML = `<div class="markdown-body">${JSON.stringify(content, null, 2)}</div>`;
                }
            } else {
                // 处理字符串类型的响应
                const markdownContent = marked.parse(String(content));
                messageDiv.innerHTML = `<div class="markdown-body">${markdownContent}</div>`;
            }
            
            // 应用代码高亮，添加检查确保hljs存在
            if (window.hljs) {
                messageDiv.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            }
        } catch (error) {
            console.error('Error processing message:', error);
            messageDiv.innerHTML = `<div class="markdown-body error-message">处理消息时发生错误: ${error.message}</div>`;
        }
    }
    
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

// 发送问题
async function sendQuestion() {
    const question = document.getElementById('question').value.trim();
    if (!question) return;

    // 添加用户问题到界面
    addMessage(question, true);

    // 显示加载动画
    document.getElementById('loading').style.display = 'block';

    try {
        const response = await fetch('/get_answer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ question: question })
        });
        const data = await response.json();

        // 隐藏加载动画
        document.getElementById('loading').style.display = 'none';

        if (data.error) {
            addMessage(`错误: ${data.error}`);
        } else {
            addMessage(data.output);
        }
    } catch (error) {
        // 隐藏加载动画
        document.getElementById('loading').style.display = 'none';
        console.error('Error:', error);
        addMessage('发生错误，请稍后重试');
    }

    // 清空输入框并重置高度
    document.getElementById('question').value = '';
    document.getElementById('question').style.height = '24px';
}

// 添加回车发送功能
textarea.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendQuestion();
    }
}); 