import requests
import json
import subprocess
import dify_api

# 编排
def make_plan(user,question):
    print("---------------【编排被调用】---------------")
    print("\n"+question)
    
    token="app-a7TWboeG5w39JQ9Gyoam0Z74"
    make_plan_ans = dify_api.run_workflow(user=user,question=question,token=token)
    print("---------------【编排结束】---------------")
    return make_plan_ans

# 生成单一shell指令
def get_shell(user,question):
    print("---------------【shell_exec被调用】---------------")
    print("\n"+question)
    
    token="app-gthhoAcf2mNyp14V02FCeuFB"
    get_shell_ans=dify_api.run_workflow(user=user,question=question,token=token)
    
    print("\n【指令】"+get_shell_ans+"\n")
    print("---------------【shell_exec结束】---------------")
    return get_shell_ans

# 总结分析报告
def get_summary(user,question):
    print("---------------【分析被调用】---------------")
    print("\n"+question)
    
    token="app-Y8usid7tjdgLVyrsal1bF2aw"
    get_summary_ans=dify_api.run_workflow(user=user,question=question,token=token)
    print("---------------【分析结束】---------------")
    return get_summary_ans

# 获取编排chatflow
def get_chat(user, question, conversation_id):
    print("---------------【chatflow被调用】---------------")
    print("\n"+question)
    print(f"当前conversation_id: {conversation_id}")
    
    token="app-8Tvueh43UxxxR4hGWXw0pg2s"
    chat_result, new_conversation_id, class_id = dify_api.run_chatflow(
        user=user,
        question=question,
        token=token,
        conversation_id=conversation_id
    )
    
    print(f"新的conversation_id: {new_conversation_id}")
    print(f"问题分类(class_id): {class_id}")
    print("---------------【chatflow结束】---------------")
    
    # 如果响应是字典且包含错误，转换为字符串
    if isinstance(chat_result, dict) and 'error' in chat_result:
        return str(chat_result), new_conversation_id, class_id
    
    return chat_result, new_conversation_id, class_id

# conversation_id="fcb4565f-7da7-44fe-947f-32c2bb12a732"
# a,b=get_chat(user="111",question="现在是你第几次回复我？",conversation_id=conversation_id)
# print("*********************************************************************")
# print("*********************************************************************")
# print(a)
# print("*********************************************************************")
# print(b)