from flask import Flask, render_template, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/vm/data', methods=['GET'])
def get_vm_data():
    data = {
        "status": "success",
        "message": "数据获取成功",
        "data": {
            "name": "测试数据",
            "items": [
                {"id": 1, "title": "项目1"},
                {"id": 2, "title": "项目2"},
                {"id": 3, "title": "项目3"}
            ],
            "timestamp": "2024-03-21"
        }
    }
    return jsonify(data)

@app.route('/api/database/data', methods=['GET'])
def get_database_data():
    data = {
        "status": "success",
        "message": "数据获取成功",
        "data": {
            "name": "测试数据",
            "items": [
                {"id": 1, "title": "项目1"},
                {"id": 2, "title": "项目2"},
                {"id": 3, "title": "项目3"}
            ],
            "timestamp": "2024-03-21"
        }
    }
    return jsonify(data)

@app.route('/api/docker/data', methods=['GET'])
def get_docker_data():
    data = {
        "status": "success",
        "message": "数据获取成功",
        "data": {
            "name": "测试数据",
            "items": [
                {"id": 1, "title": "项目1"},
                {"id": 2, "title": "项目2"},
                {"id": 3, "title": "项目3"}
            ],
            "timestamp": "2024-03-21"
        }
    }
    return jsonify(data)
if __name__ == '__main__':
    app.run(debug=True) 