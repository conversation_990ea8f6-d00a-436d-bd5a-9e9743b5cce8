# 容量报告API接口文档完善说明

## 完善内容概述

我已经帮你完善了 `容量报告API接口文档.txt` 中的数据结构示例部分，使其更加详细和完整。

## 主要完善内容

### 1. 存储容量接口数据结构
**新增字段：**
- `weekly_change`: 周变化率
- `location`: 存储位置（嘉兴/后沙峪）
- `storage_type`: 存储类型（中端虚拟化/中端数据库/高端）
- `vendor`: 厂商信息（EMC/Dell/NetApp）
- `model`: 设备型号
- `summary`: 汇总信息（总容量TB、整体使用率等）

### 2. 数据库容量接口数据结构
**新增字段：**
- `db_type`: 数据库类型（Oracle/MySQL/PostgreSQL）
- `version`: 数据库版本
- `cpu_usage_rate`: CPU使用率
- `memory_usage_rate`: 内存使用率
- `connection_count`: 当前连接数
- `max_connections`: 最大连接数
- `tablespace_details`: 表空间详细信息（Oracle）
- `replication_lag`: 复制延迟（MySQL）
- `environment`: 环境类型（生产/测试）
- `cluster_role`: 集群角色（主库/从库）

### 3. 容器容量接口数据结构
**新增字段：**
- `cluster_type`: 集群类型（Kubernetes/TAP）
- `version`: 平台版本
- `node_count`: 节点数量
- `master_node_count`: 主节点数量
- `worker_node_count`: 工作节点数量
- `cpu_peak_usage_rate`: CPU峰值使用率
- `pod_running_count`: 运行中Pod数量
- `container_running_count`: 运行中容器数量
- `namespace_count`: 命名空间数量
- `service_count`: 服务数量
- `pod_details`: Pod详细分布信息
- `tap_specific`: TAP平台特有信息

### 4. 虚拟化容量接口数据结构
**新增字段：**
- `cluster_type`: 集群类型（VMware vSphere/Microsoft Hyper-V）
- `version`: 平台版本
- `esxi_host_count`: ESXi主机数量
- `physical_cpu_cores`: 物理CPU核数
- `physical_memory_gb`: 物理内存
- `vcpu_allocated`: 分配的vCPU
- `vcpu_used`: 使用的vCPU
- `vmemory_allocated_gb`: 分配的虚拟内存
- `vmemory_used_gb`: 使用的虚拟内存
- `monthly_change`: 月变化率
- `vm_running_count`: 运行中虚拟机数量
- `vm_powered_off`: 关机虚拟机数量
- `vm_template_count`: 模板数量
- `overcommit_ratios`: 超分比例
- `ha_configuration`: 高可用配置
- `vm_distribution`: 虚拟机分布信息

### 5. 健康检查接口数据结构
**新增字段：**
- `version`: API版本
- `uptime`: 服务运行时间
- `endpoints`: 各接口可用性状态

## 新增的文档部分

### 1. 字段说明
- **通用字段**: status、timestamp、data等
- **容量相关字段**: 总容量、已使用、可用容量、使用率、变化率等
- **健康状态字段**: 健康状态、异常标识、应对措施等
- **资源特定字段**: CPU、内存、存储使用率、节点数量等

### 2. 错误响应示例
- **服务不可用**: SERVICE_UNAVAILABLE
- **数据获取失败**: DATA_FETCH_ERROR
- **参数错误**: INVALID_PARAMETER

### 3. 使用示例
- **基本API调用**: curl命令示例
- **特定查询**: 带参数的查询示例
- **批量获取**: 获取所有接口数据的示例

### 4. 注意事项
- 数据时效性说明
- 错误处理建议
- 性能考虑
- 数据格式规范
- 健康状态标准

## 数据结构特点

### 1. 完整性
- 每个接口都包含完整的数据结构示例
- 涵盖了所有主要字段和嵌套对象
- 提供了真实的数据值示例

### 2. 实用性
- 包含了实际业务中需要的关键字段
- 提供了多种时间维度的变化对比（日/周/月）
- 包含了健康状态和异常处理信息

### 3. 扩展性
- 预留了厂商、版本、环境等扩展字段
- 支持不同类型的集群和平台
- 包含了平台特有的配置信息

### 4. 标准化
- 统一的响应格式（status、timestamp、data）
- 一致的字段命名规范
- 标准的错误响应格式

## 与Flask API的对应关系

文档中的数据结构与实际的Flask API (`app.py`) 完全对应：

1. **存储接口** → `get_storage_capacity()`
2. **数据库接口** → `get_database_capacity()`
3. **容器接口** → `get_container_capacity()`
4. **虚拟化接口** → `get_virtualization_capacity()`
5. **健康检查** → `health_check()`

## 使用建议

### 1. 开发参考
- 可以直接根据文档中的数据结构进行前端开发
- 字段说明部分有助于理解各字段的含义和用途
- 错误响应示例有助于完善错误处理逻辑

### 2. 测试验证
- 使用文档中的curl示例进行API测试
- 对比实际API响应与文档示例的一致性
- 验证错误场景的处理

### 3. 集成开发
- 根据数据结构设计数据库表结构
- 参考字段定义进行数据映射
- 使用健康状态字段进行监控告警

## 后续扩展建议

### 1. 增加查询参数
- 时间范围查询
- 特定资源池查询
- 分页查询支持

### 2. 增加统计接口
- 历史趋势数据
- 容量预测分析
- 跨维度对比分析

### 3. 增加配置接口
- 健康度阈值配置
- 告警规则配置
- 数据源配置

---

**文档状态**: 已完善  
**完善时间**: 2024-01-15  
**文档版本**: v1.0
