错误：Bigeye脚本作业心跳超时
解决方法：第一步.root用户登录服务器，连续执行几次：
		  ps -ef |grep jobid（jobid在事件详情中提供）
		  如果没有回显，则无故障。如果一直有回显，执行第二步。
		  
		第二步 root用户登录服务器，执行：
		  cd /opt/adm/itoms/itomsAgent
		  ./agent.sh stop
		  ./agent.sh start
		  或者
		  service itomsAgent restart
		  或者
		  systemctl restart itomsAgent
		正常回显如下：
		Started [PID]
###################################################################

错误：【仲裁】JbossHTTP端口无法连接异常
解决方法：如果服务级别为S3，则按照以下顺序进行通知：
		应用值班人（公司值班表中查看）----应用维护人（通过事件详情中的报警主机信息页中查到）----业务负责人（通过事件详情中的报警主机信息页中查到）----“归属部门”负责人（可在最新版的重保安全联系人列表查询）或  研发值班经理（旅游交通部/航旅纵横跳过此步，直接通知中间件值班，如果联系人不属于研发的通知“归属部门”负责人）

		若服务器维护级别为S4则：
		　　切换至“事件现场”，点击“1:检测JBoss端口监听”。
		　　若端口不存在，点击“2:重启JBossServer”。
		　　若５分钟后没有回显或回显FAIL，请转为人工处理，或根据“事件现场”的回显进行后续处理。

		S4人工处理流程：
		第一步：首先检查server端口是否存在，以jboss5或jboss用户登录
		　　　　netstat -an |grep $PORT |grep LISTEN
		第二步：如不存在则直接重启，以jboss5或jboss用户登录
		　　　　cd /opt/app/appManage/bin
		　　　　./restartServer.sh -l $PORT
		第三步：如果显示The server restarted failed，表示server重启失败。通知中间件二线值班。
		　　　　如果显示The server already restarted，表示server重启成功，不用通知

################################################################### 
错误：磁盘IO BUSY
解决方法：如果是Solaris系统
			1、  登录系统，使用vmstat或者iostat命令查看系统IO状态
			2、  使用prstat命令查看那些进程占用系统资源较大（主要为CPU及MEM）
			3、  根据相关进程号查看具体进程（如为系统进程则需据经验判断该进程是否正常，或通过厂家工程师支持进行分析，如为应用进程，则通知开放值班人员进行分析）

			如果是AIX系统
			1、  登录系统，使用topas命令查看出现I/O 100％的磁盘是否有读写，即KB-Read、KB-Writ的值是否正常；
			2、  如果KB-Read 、KB-Writ为0，但I/O 持续为100％，按照系统I/O hung应急流程处理；
			3、KB-Read、KB-Writ值如果较大，使用nmon命令（进入nmon界面后，按t），查看I/O高的进程，根据进程通知开放人员。

			如果是Linux 系统
			1、  登录系统，使用iostat命令查看系统IO状态
			[root@bl685a9-vmwc2-wls ~]# iostat
			Linux 2.6.18-128.el5PAE (bl685a9-vmwc2-wls)     08/26/2009
			avg-cpu:  %user   %nice %system %iowait  %steal   %idle

					   0.34    0.01    0.52    0.65    0.00   98.49
			Device:            tps   Blk_read/s   Blk_wrtn/s   Blk_read   Blk_wrtn
			sda              11.83      1111.14       346.82    8334460    2601437
			sda1              0.01         0.30         0.00       2262          4
			sda2             11.81      1110.80       346.82    8331926    2601433
			2、  使用top命令查看那些进程占用系统资源较大（主要为CPU及MEM）
			根据相关进程号查看具体进程（如为系统进程则需据经验判断该进程是否正常，或通过厂家工程师支持进行分析，如为应用进程，则通知开放值班人员进行分析）

###################################################################
错误：BigEye报警脚本关键字信息在知识库中没有登记
解决方法： 1）给运维工具值班员发邮件通知有未识别的脚本；
		   2）添加相应rules

###################################################################
错误：FC Switch其他事件
解决方法： 检查交换机和所连接设备状态

###################################################################
错误：监控脚本所需系统命令运行出错
解决方法：监控脚本调用系统命令时出错，通知运维工具值班 

###################################################################
错误：prouser用户ULIMIT连接数异常
解决方法：该报警是监控S3服务器PROUSER用户ULIMIT设置是否等于或大于65535，如果不是，则报警。
			修改参数按如下流程处理，修改完成后通知应用。
			1. 登录报警服务器
			2. 执行#su - -c 'ulimit -a' prouser 检查连接数参数，小于65535则执行步骤3-5进行修改。
			3. 备份配置文件 cp /etc/security/limits.conf /etc/security/limits.conf.bak.`date +%Y%m%d`
			4. 编辑文件/etc/security/limits.consf   
				 如果之前存在对prouser用户的设置，注释掉
					 在文件末尾添加两行：
					 prouser - nproc 65535
					 prouser - nofile 65535
			5. 执行#su - -c 'ulimit -a' prouser检查参数是否修改成功。

###################################################################
错误：BigEye心跳超时
解决方法：*如IP为**********/2，机器名为HSY-SPO1/2，请直接通知东方仗助12345678910
			第一步.登录报警服务器，如无法登录通知硬件值班人员.
			如可以登录执行第二步
			第二步.
			出现个别报警时：
			通过4a登陆目标服务器，然后通过myid获取目标服务器的root密码，
			执行su -，输入root密码切换至root用户
			root用户登录服务器，执行：
			  cd /opt/adm/itoms/itomsAgent
			  ./agent.sh stop
			  ./agent.sh start
			  或者
			  service itomsAgent restart
			  或者
			  systemctl restart itomsAgent
			正常回显如下：
			running: PID:

			出现大量报警时，登录报警服务器，
			cd /opt/adm/itoms/itomsAgent/conf(没有此目录则进入/opt/app/itoms/itomsAgent/conf)
			cat itomsAgent.xml,查看<name>后的两个ip，登陆这两个ip，
			 cd /opt/app/itoms/bigeye-ms
			重启   ./bigeye-ms.jar restart
			两个都重启后，等待几分钟，报警的最新发生时间没有更新就可以关闭报警，如果还有最新发生时间在更新的，登录还在发生更新的报警ip，按照上述方法查看itomsAgent.xml中两个ip是不是刚才重启的，如果不是的话，按照上述方法重启，然后等待报警最新发生时间是否更新。

###################################################################
错误：【仲裁】Apache无法连接异常
解决方法："若服务器维护级别为S3则通知应用维护负责人，若为S4则：若服务级别为S3，则按照以下顺序通知：应用值班人（公司值班表中查看）----应用维护人  （通过事件详情中的报警主机信息页中查到）
			若服务级别为S4，则：
			用用户root登录服务器
			1、执行/opt/app/apache2/bin/restartApache.sh
			2、执行ps -ef|grep -v grep|grep httpd
			如果有输出，则apache重启成功，不用通知，否则通知中间件二线值班。"
 
###################################################################
错误：物理服务器网络接口故障
解决方法：通知硬件二线工程师
 
################################################################### 
错误：【仲裁】MQ初始化失败异常
解决方法：远程探测MQ初始化连接失败，需要核查QMGR，端口的配置项的正确性等，更改配置字段信息。
 
################################################################### 
错误：Oracle19c_发现enq: TX - row lock contention等待争用 
解决方法：（1）通过报警事件查看等待发生在CDB或某个PDB中。
		（2）确认数据库中等待事件：select event,count(*) from v$session where wait_class !='Idle' group by event;
		（3）如确认等待严重则先保留ASH现场并查询并KILL阻塞者并通知应用：
			保留现场：create table  ****  as select * from v$active_session_history;
			查询阻塞者并KILL
			select sid,count(BLOCKER) from (select INST_ID, SID, TYPE, ID1, ID2, LMODE, REQUEST, CTIME, BLOCK,
			DECODE (BLOCK, 0, '', 'blocker') blocker,
			DECODE (request, 0, '', 'waiter') waiter
			from gv$lock where (ID1,ID2,TYPE) in
			(select ID1,ID2,TYPE from gv$lock where request>0)
			order by blocker) group by sid order by 2 desc;

			alter system kill session '***,###';(***SID号，###SERIAL）杀掉阻塞者会话

		（4）如效果不明显则，通知应用人员停止应用并KILL掉所有会话
			select 'alter system kill session '''||sid||','||serial#||''' immediate;' from v$session where username ='*****';

	补充二线处理办法：
	1.通过以下SQL查看发生行锁的会话信息
	with a as
	(select blocking_instance,
			 username,
			 blocking_instance || '_' || blocking_session "blocker(ins)_blocker(sid)",
			 blocking_session,
			 --b.sql_text,
			 count(*) as cnt
		from gv$session a, gv$sqltext b
	   where a.sql_id = b.sql_id(+) and SECONDS_IN_WAIT > &secs--（为变量，针对不同的数据库设定阈值不同，对应需求中的等待时间）
		 and event = 'enq: TX - row lock contention'
		 and blocking_session_status = 'VALID'
		 and a.inst_id=b.inst_id
	   group by username,
				blocking_instance || '_' || blocking_session,
				--b.sql_text,
				blocking_session,
				event,
				blocking_instance)
	select c.inst_id||'_'||c.sid||'_'||c.serial#  "blocker(inst_id_sid_serial#)",
		   c.username blocker_username,
		   e.owner lockrow_object_owner,
		   e.object_name lockrow_object_name,
		   --a.sql_text locker_waiter_sqltext,
		   f.sql_text "blockersql(for reference only)",
		   case when c.TYPE='BACKGROUND' then 'please attention blocker program' else 'user session' end session_type,
		   c.machine blocker_machine,
		   c.program blocker_program,
		   a.cnt     blocker_cnt,
		   'alter system kill session '''||c.sid||','||c.serial#||',@'||c.inst_id||''' immediate' kill_oper
	  from a, gv$session c, gv$locked_object d, dba_objects e,gv$sqlarea f
	where a.blocking_session = c.sid
	   and a.blocking_instance = c.inst_id
	   and a.blocking_session = d.session_id
	   and a.blocking_instance = d.inst_id
	   and c.sid = d.session_id
	   and c.inst_id = d.inst_id
	   and d.object_id=e.object_id
	   and nvl(c.sql_id,c.prev_sql_id)=f.sql_id(+)
	   and c.inst_id=f.inst_id;
	说明：需要提供输入&secs参数，查询出满足&secs秒行锁阻塞的会话相关信息。
	session_type为user session，联系对应业务负责人，询问是否可以将会话kill。
	session_type为后台进程(please attention blocker program)联系团队负责人，询问是否可以kill会话Oracle19c_发现enq: TX - row lock contention等待争用 | （1）通过报警事件查看等待发生在CDB或某个PDB中。
	（2）确认数据库中等待事件：select event,count(*) from v$session where wait_class !='Idle' group by event;
	（3）如确认等待严重则先保留ASH现场并查询并KILL阻塞者并通知应用：
	保留现场：create table  ****  as select * from v$active_session_history;
	查询阻塞者并KILL
	select sid,count(BLOCKER) from (select INST_ID, SID, TYPE, ID1, ID2, LMODE, REQUEST, CTIME, BLOCK,
	DECODE (BLOCK, 0, '', 'blocker') blocker,
	DECODE (request, 0, '', 'waiter') waiter
	from gv$lock where (ID1,ID2,TYPE) in
	(select ID1,ID2,TYPE from gv$lock where request>0)
	order by blocker) group by sid order by 2 desc;

	alter system kill session '***,###';(***SID号，###SERIAL）杀掉阻塞者会话

	（4）如效果不明显则，通知应用人员停止应用并KILL掉所有会话
	select 'alter system kill session '''||sid||','||serial#||''' immediate;' from v$session where username ='*****';

	补充二线处理办法：
	1.通过以下SQL查看发生行锁的会话信息
	with a as
	(select blocking_instance,
			 username,
			 blocking_instance || '_' || blocking_session "blocker(ins)_blocker(sid)",
			 blocking_session,
			 --b.sql_text,
			 count(*) as cnt
		from gv$session a, gv$sqltext b
	   where a.sql_id = b.sql_id(+) and SECONDS_IN_WAIT > &secs--（为变量，针对不同的数据库设定阈值不同，对应需求中的等待时间）
		 and event = 'enq: TX - row lock contention'
		 and blocking_session_status = 'VALID'
		 and a.inst_id=b.inst_id
	   group by username,
				blocking_instance || '_' || blocking_session,
				--b.sql_text,
				blocking_session,
				event,
				blocking_instance)
	select c.inst_id||'_'||c.sid||'_'||c.serial#  "blocker(inst_id_sid_serial#)",
		   c.username blocker_username,
		   e.owner lockrow_object_owner,
		   e.object_name lockrow_object_name,
		   --a.sql_text locker_waiter_sqltext,
		   f.sql_text "blockersql(for reference only)",
		   case when c.TYPE='BACKGROUND' then 'please attention blocker program' else 'user session' end session_type,
		   c.machine blocker_machine,
		   c.program blocker_program,
		   a.cnt     blocker_cnt,
		   'alter system kill session '''||c.sid||','||c.serial#||',@'||c.inst_id||''' immediate' kill_oper
	  from a, gv$session c, gv$locked_object d, dba_objects e,gv$sqlarea f
	where a.blocking_session = c.sid
	   and a.blocking_instance = c.inst_id
	   and a.blocking_session = d.session_id
	   and a.blocking_instance = d.inst_id
	   and c.sid = d.session_id
	   and c.inst_id = d.inst_id
	   and d.object_id=e.object_id
	   and nvl(c.sql_id,c.prev_sql_id)=f.sql_id(+)
	   and c.inst_id=f.inst_id;
	说明：需要提供输入&secs参数，查询出满足&secs秒行锁阻塞的会话相关信息。
	session_type为user session，联系对应业务负责人，询问是否可以将会话kill。
	session_type为后台进程(please attention blocker program)联系团队负责人，询问是否可以kill会话