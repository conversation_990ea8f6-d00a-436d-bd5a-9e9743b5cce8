# 智能容量报告系统使用说明

## 📋 系统概述

智能容量报告系统是一个集成了LLM分析能力的自动化容量监控和报告生成平台。系统通过API接口获取实时容量数据，使用智能分析算法进行风险评估和趋势分析，最终生成专业的Word格式容量报告。

## 🚀 核心功能

### 1. 智能数据分析
- **多维度数据收集**：存储、数据库、容器、虚拟化四大资源类型
- **实时数据获取**：通过RESTful API接口获取最新容量数据
- **智能风险评估**：基于行业最佳实践的多维度风险评估模型
- **趋势分析**：识别资源使用趋势和潜在风险点

### 2. LLM增强分析
- **本地分析模式**：使用内置规则引擎进行快速分析
- **LLM增强模式**：结合大语言模型提供更深入的洞察
- **智能建议生成**：基于分析结果生成个性化优化建议
- **风险预警**：自动识别和预警潜在的容量风险

### 3. 专业报告生成
- **结构化报告**：包含执行摘要、详细分析、智能建议等章节
- **可视化展示**：使用图标和颜色标识不同的健康状态
- **Word文档导出**：生成专业的.docx格式报告文档
- **中文本地化**：完全支持中文界面和报告内容

## 🛠️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据源系统     │    │   Flask API     │    │   LLM分析引擎   │
│                │    │                │    │                │
│ • 存储系统      │───▶│ • 数据接口      │───▶│ • 风险评估      │
│ • 数据库系统    │    │ • 报告生成      │    │ • 趋势分析      │
│ • 容器平台      │    │ • Word导出      │    │ • 建议生成      │
│ • 虚拟化平台    │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Dify工作流    │
                       │                │
                       │ • 流程编排      │
                       │ • 参数配置      │
                       │ • 结果展示      │
                       └─────────────────┘
```

## 📊 API接口说明

### 1. 容量数据接口

#### 存储容量数据
```http
GET /api/storage
```

#### 数据库容量数据
```http
GET /api/database
```

#### 容器容量数据
```http
GET /api/container
```

#### 虚拟化容量数据
```http
GET /api/virtualization
```

### 2. 报告生成接口

#### 传统报告生成
```http
POST /api/generate_report
Content-Type: application/json

{
  "report_date": "2024-01-15",
  "system_name": "生产环境运维资源容量检查报告"
}
```

#### 智能报告生成
```http
POST /api/generate_smart_report
Content-Type: application/json

{
  "report_date": "2024-01-15",
  "system_name": "生产环境运维资源容量检查报告",
  "llm_config": {
    "api_url": "https://api.openai.com/v1/chat/completions",
    "api_key": "your-api-key"
  }
}
```

#### Word文档导出
```http
POST /api/export_word
Content-Type: application/json

{
  "report_content": "报告内容...",
  "report_date": "2024-01-15",
  "system_name": "系统名称",
  "save_path": "./reports/"
}
```

## 🔧 部署和配置

### 1. 环境要求
- Python 3.8+
- Flask 2.0+
- python-docx
- requests

### 2. 安装步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd llm-capacity-report

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python app.py
```

### 3. 配置说明
- **API服务地址**：默认 http://localhost:5000
- **报告保存路径**：默认 ./reports/
- **LLM配置**：可选，支持OpenAI等兼容接口

## 📝 使用方法

### 方法一：直接API调用

1. **启动Flask服务**
```bash
python app.py
```

2. **生成智能报告**
```bash
python test_smart_report.py
```

3. **查看生成的报告**
- Markdown格式：`智能报告示例.md`
- Word文档：`./reports/报告名称_日期.docx`

### 方法二：Dify工作流

1. **导入工作流配置**
- 使用 `dify_smart_workflow.yml` 配置文件
- 在Dify平台中创建新的工作流应用

2. **配置参数**
- API服务地址：http://localhost:5000
- 报告日期：YYYY-MM-DD格式
- 系统名称：自定义报告标题
- 分析模式：本地分析或LLM增强

3. **运行工作流**
- 填写必要参数
- 执行工作流
- 获取生成的Word报告

## 📈 报告内容说明

### 1. 执行摘要
- **整体状况**：系统容量概览和智能分析结果
- **关键指标**：各类资源的核心数据统计
- **风险评估**：风险等级、评分和问题汇总

### 2. 详细分析
- **存储资源分析**：存储池容量、使用率、健康状态
- **数据库分析**：数据库实例容量和性能状况
- **容器资源分析**：容器集群CPU、内存、存储使用情况
- **虚拟化分析**：虚拟化集群资源分配和使用状况

### 3. 智能建议
- **优化建议**：基于分析结果的具体优化措施
- **风险预警**：潜在风险点和预防措施
- **容量规划**：未来容量需求预测和规划建议

### 4. 技术附录
- **分析方法**：数据来源和分析算法说明
- **阈值标准**：各类资源的健康度判断标准
- **生成信息**：报告生成时间和技术参数

## 🎯 健康度标准

### 存储资源
- 🟢 绿色正常：使用率 < 85%
- 🟡 黄色观察：使用率 85-90%
- 🟠 橙色关注：使用率 90-95%
- 🔴 红色警告：使用率 > 95%

### 数据库资源
- 🟢 绿色正常：使用率 < 80%
- 🟡 黄色观察：使用率 80-85%
- 🟠 橙色关注：使用率 85-95%
- 🔴 红色警告：使用率 > 95%

### 容器资源
- 🟢 绿色正常：CPU/内存使用率 < 70%
- 🟡 黄色观察：CPU/内存使用率 70-80%
- 🟠 橙色关注：CPU/内存使用率 80-90%
- 🔴 红色警告：CPU/内存使用率 > 90%

### 虚拟化资源
- 🟢 绿色正常：CPU/内存使用率 < 65%
- 🟡 黄色观察：CPU/内存使用率 65-75%
- 🟠 橙色关注：CPU/内存使用率 75-85%
- 🔴 红色警告：CPU/内存使用率 > 85%

## 🔍 故障排除

### 常见问题

1. **API服务无法启动**
   - 检查端口5000是否被占用
   - 确认Python环境和依赖包安装正确

2. **报告生成失败**
   - 检查API服务是否正常运行
   - 确认网络连接和防火墙设置

3. **Word文档导出失败**
   - 检查保存路径是否存在和可写
   - 确认python-docx库安装正确

4. **LLM分析不工作**
   - 检查LLM API配置是否正确
   - 确认API密钥有效性和网络连接

### 日志查看
- Flask服务日志：控制台输出
- 错误信息：API响应中的error字段
- 调试信息：设置Flask debug模式

## 📞 技术支持

如有问题或建议，请联系技术支持团队。

---

*智能容量报告系统 v1.0 - 让容量管理更智能*
