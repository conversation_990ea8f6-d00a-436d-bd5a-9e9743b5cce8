import requests
import json
import subprocess


# 调用workflow的方法
def run_workflow(token,user,question):
    """
    调用工作流接口的函数
    参数：
    token (str): 认证令牌，默认为示例令牌
    
    返回：
    dict: 如果返回数据包含 return_exec，则返回其值，否则返回完整响应 JSON
    """
    
    url = "http://172.30.64.1/v1/workflows/run"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {
        "inputs": {"question": question},
        "response_mode": "streaming",
        "user": user
    }
    response = requests.post(url, headers=headers, json=payload, stream=True)
    try:
        # 逐行读取流式数据
        for line in response.iter_lines():
            if line:
                # 每行数据都是以 "data: " 开头的 JSON 字符串
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    # 解析 data 部分的 JSON
                    json_data = json.loads(line[len('data: '):])
                    # 检查并提取 exec_return
                    if 'data' in json_data and 'outputs' in json_data['data']:
                        outputs = json_data['data']['outputs']
                        if 'exec_return' in outputs:
                            return outputs['exec_return']
                    else:
                        # 继续处理或返回完整的数据
                        print(json_data)  # 打印每次接收到的数据
        return {"error": "No exec_return found in the response"}
    
    except requests.exceptions.JSONDecodeError:
        return {"error": "Invalid JSON response", "text": response.text}
    


# 调用chatflow的方法
def run_chatflow(token, user, question, conversation_id):
    """
    调用聊天接口的函数
    参数：
    token (str): 认证令牌
    user (str): 用户标识
    question (str): 用户提问
    conversation_id (str): 对话ID
    
    返回：
    tuple: (full_answer, conversation_id, class_id) - 完整的聊天回答、更新后的对话ID和分类ID
    """
    
    url = "http://172.30.64.1/v1/chat-messages"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {
        "inputs": {},
        "query": question,
        "response_mode": "streaming",
        "user": user,
        "conversation_id": conversation_id
    }
    
    print(f"发送请求到 {url}，conversation_id={conversation_id}")
    
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True)
        response.raise_for_status()  # 检查HTTP错误
        
        # 用于收集完整答案的变量
        full_answer = ""
        new_conversation_id = conversation_id  # 默认使用原始ID
        class_id = None  # 用于存储分类ID
        
        # 逐行读取流式数据
        for line in response.iter_lines():
            if line:
                # 每行数据都是以 "data: " 开头的 JSON 字符串
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    # 解析 data 部分的 JSON
                    json_data = json.loads(line[len('data: '):])
                    # 检查并累积 answer
                    if 'answer' in json_data:
                        full_answer += json_data['answer']
                        if 'conversation_id' in json_data:
                            new_conversation_id = json_data['conversation_id']
                    else:
                        # 检查是否包含outputs和class_id
                        print(f"收到非答案数据: {json_data}")
                        # 尝试从node_finished事件中提取class_id
                        if json_data.get('event') == 'node_finished' and 'data' in json_data:
                            node_data = json_data['data']
                            if 'outputs' in node_data and 'class_id' in node_data['outputs']:
                                class_id = node_data['outputs']['class_id']
                                print(f"找到class_id: {class_id}")
        
        if full_answer:
            return full_answer, new_conversation_id, class_id
        else:
            return "未能获取到回答，请稍后再试。", new_conversation_id, class_id
    
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {str(e)}")
        return f"请求错误: {str(e)}", conversation_id, None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {str(e)}")
        return f"JSON解析错误: {str(e)}", conversation_id, None
    except Exception as e:
        print(f"未知错误: {str(e)}")
        return f"发生错误: {str(e)}", conversation_id, None
    
    