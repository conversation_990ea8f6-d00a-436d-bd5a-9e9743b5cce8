# HTTP版本容量报告生成器使用说明

## 概述

HTTP版本的容量报告生成器通过HTTP请求调用Flask API来生成容量报告和导出Word文档，完全避免了Dify代码节点的语法问题和依赖限制。

## 优势对比

### HTTP版本 vs 代码节点版本

| 特性 | HTTP版本 | 代码节点版本 |
|------|----------|-------------|
| 语法错误风险 | ❌ 无风险 | ⚠️ 有风险 |
| 变量引用复杂度 | ✅ 简单 | ⚠️ 复杂 |
| 依赖管理 | ✅ 服务端管理 | ⚠️ 环境限制 |
| 调试难度 | ✅ 容易 | ⚠️ 困难 |
| 错误处理 | ✅ 完善 | ⚠️ 有限 |
| 可维护性 | ✅ 高 | ⚠️ 低 |

## 文件说明

### 核心文件

- `dify_http_workflow.yml` - HTTP版本的Dify工作流配置
- `app.py` - 增强版Flask API服务器（包含报告生成和导出接口）
- `html_export.py` - Word导出工具模块（使用python-docx生成真正的Word文档）
- `test_http_api.py` - HTTP API功能测试脚本

### 测试文件

- `test_dify_code_node.py` - 原代码节点测试（已修复）
- `test_html_export.py` - HTML导出功能测试

## 使用步骤

### 1. 启动Flask API服务

```bash
cd D:\work\LLM
python app.py
```

服务启动后会显示：
```
* Running on http://127.0.0.1:5000
* Running on http://192.168.8.88:5000
```

### 2. 测试API功能

运行完整测试验证所有功能：

```bash
python test_http_api.py
```

测试包括：
- API健康检查
- 容量报告生成
- Word文档导出
- 完整工作流模拟

### 3. 导入Dify工作流

1. 登录Dify平台 (http://************:980)
2. 进入工作流管理页面
3. 选择"导入工作流"
4. 上传 `dify_http_workflow.yml` 文件
5. 确认导入成功

### 4. 配置工作流参数

在Dify工作流中填写以下参数：

- **报告日期**: 格式为 YYYY-MM-DD，例如：2024-01-15
- **系统名称**: 报告标题，例如：生产环境运维资源容量检查报告
- **API服务地址**: Flask API服务地址，例如：http://localhost:5000
- **Word文件保存路径**: 可选，默认为 D:/work/LLM/reports/

### 5. 运行工作流

点击"运行"按钮，工作流将：

1. **HTTP请求1**: 调用 `/api/generate_report` 生成容量报告
2. **HTTP请求2**: 调用 `/api/export_word` 导出Word文档
3. **显示结果**: 展示报告内容和文件信息

## API接口说明

### 1. 报告生成接口

**接口**: `POST /api/generate_report`

**请求参数**:
```json
{
  "report_date": "2024-01-15",
  "system_name": "生产环境运维资源容量检查报告",
  "api_base_url": "http://localhost:5000"
}
```

**响应格式**:
```json
{
  "success": true,
  "report_content": "# 报告内容...",
  "report_date": "2024-01-15",
  "system_name": "生产环境运维资源容量检查报告",
  "data_source": "API自动获取"
}
```

### 2. Word导出接口

**接口**: `POST /api/export_word`

**请求参数**:
```json
{
  "report_content": "# 报告内容...",
  "report_date": "2024-01-15",
  "system_name": "生产环境运维资源容量检查报告",
  "save_path": "./reports/"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "Word文档生成成功",
  "file_path": "./reports/生产环境运维资源容量检查报告_20240115.docx",
  "file_name": "生产环境运维资源容量检查报告_20240115.docx",
  "file_size": "37.40 KB",
  "file_type": "Microsoft Word文档 (.docx)",
  "note": "生成的Word文档可以直接用Microsoft Word打开"
}
```

### 3. 健康检查接口

**接口**: `GET /api/health`

**响应格式**:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-27T14:48:27.267334",
  "message": "容量监控API服务运行正常"
}
```

## 工作流节点详解

### 1. 开始节点
- 收集用户输入的4个参数
- 支持参数验证和默认值

### 2. 生成容量报告节点 (HTTP请求)
- 调用Flask API的报告生成接口
- 自动获取所有容量数据
- 生成专业格式的Markdown报告
- 超时设置：连接30秒，读取60秒

### 3. 导出Word文档节点 (HTTP请求)
- 调用Flask API的Word导出接口
- 将Markdown转换为真正的Word文档格式(.docx)
- 使用python-docx库生成专业格式的Word文档
- 保存到指定路径并返回详细的文件信息

### 4. 报告输出节点
- 显示完整的报告内容
- 显示Word导出结果和文件信息
- 提供使用提示

## 故障排除

### 问题1: API服务连接失败

**症状**: 工作流显示"连接失败"或"HTTP错误"

**解决方案**:
1. 确认Flask API服务正在运行
2. 检查API服务地址是否正确
3. 运行健康检查：
   ```bash
   curl http://localhost:5000/api/health
   ```

### 问题2: 报告生成失败

**症状**: 第一个HTTP请求返回错误

**解决方案**:
1. 检查请求参数格式是否正确
2. 验证API服务日志
3. 运行测试脚本诊断：
   ```bash
   python test_http_api.py
   ```

### 问题3: Word导出失败

**症状**: 第二个HTTP请求返回错误

**解决方案**:
1. 检查保存路径是否存在写入权限
2. 验证报告内容是否为空
3. 检查磁盘空间是否充足

## 测试和验证

### 完整功能测试

```bash
# 启动API服务
python app.py

# 在另一个终端运行测试
python test_http_api.py
```

### 单独接口测试

```bash
# 测试健康检查
curl http://localhost:5000/api/health

# 测试报告生成
curl -X POST http://localhost:5000/api/generate_report \
  -H "Content-Type: application/json" \
  -d '{"report_date":"2024-01-15","system_name":"测试报告","api_base_url":"http://localhost:5000"}'
```

## 部署建议

### 开发环境
- 使用Flask开发服务器（已配置）
- 端口：5000
- 调试模式：开启

### 生产环境
- 使用Gunicorn或uWSGI
- 配置反向代理（Nginx）
- 启用HTTPS
- 配置日志和监控

### 示例生产配置

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 优化建议

1. **缓存机制**: 对频繁请求的数据添加缓存
2. **异步处理**: 对大文件导出使用异步任务
3. **错误重试**: 在Dify工作流中添加重试逻辑
4. **监控告警**: 添加API性能和错误监控
5. **安全加固**: 添加API认证和访问控制

## 总结

HTTP版本的容量报告生成器完全解决了代码节点版本的所有问题：

✅ **无语法错误风险** - 使用标准HTTP请求，无需复杂的变量引用语法  
✅ **易于调试** - 可以独立测试每个API接口  
✅ **功能完整** - 支持完整的报告生成和Word导出流程  
✅ **高可靠性** - 完善的错误处理和状态反馈  
✅ **易于维护** - 清晰的模块化架构  

推荐使用HTTP版本作为生产环境的首选方案。
