# Flask API + Dify 容量报告系统

## 项目概述

这个项目包含两个部分：
1. **Flask API服务** - 提供四个容量信息接口
2. **Dify工作流** - 调用API接口并生成容量报告

## 项目结构

```
├── app.py                          # Flask应用主文件
├── requirements.txt                # Python依赖包
├── start.bat                       # Windows启动脚本
├── test_api.py                     # API测试脚本
├── dify_capacity_report_app.yml    # 修改后的Dify工作流配置
├── Flask_API_项目说明.md           # 本文件
└── 其他相关文档...
```

## Flask API服务

### 功能特点

- **四个核心接口**：存储、数据库、容器(TAP)、虚拟化
- **自动计算**：使用率、剩余容量、健康状态
- **模拟数据**：包含真实的容量数据结构
- **健康度评估**：根据不同阈值自动判断状态

### API接口说明

#### 1. 存储容量接口
- **URL**: `GET /api/storage`
- **功能**: 获取存储资源池容量信息
- **数据**: 5个存储池，包括嘉兴和后沙峪的虚拟化、数据库、高端存储池

#### 2. 数据库容量接口
- **URL**: `GET /api/database`
- **功能**: 获取数据库实例容量信息
- **数据**: 7个数据库实例，包括Oracle、MySQL、PostgreSQL

#### 3. 容器容量接口
- **URL**: `GET /api/container`
- **功能**: 获取容器集群资源信息
- **数据**: 4个容器集群，包括K8S和TAP容器集群

#### 4. 虚拟化容量接口
- **URL**: `GET /api/virtualization`
- **功能**: 获取虚拟化集群资源信息
- **数据**: 4个虚拟化集群，包括vSphere和Hyper-V

#### 5. 健康检查接口
- **URL**: `GET /api/health`
- **功能**: 检查API服务状态

### 数据结构示例

```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00",
  "data": {
    "storage_pools": [
      {
        "pool_name": "嘉兴中端虚拟化存储池",
        "pool_id": "JX-M-VM-Prod",
        "total_capacity_gb": 769034,
        "used_capacity_gb": 685234,
        "available_capacity_gb": 83800,
        "usage_rate": 89.11,
        "daily_change": -0.72,
        "health_status": "正常",
        "has_anomaly": false,
        "measures": "无需措施"
      }
    ],
    "summary": {
      "total_pools": 5,
      "normal_pools": 5,
      "warning_pools": 0
    }
  }
}
```

## 启动Flask服务

### 方法1：使用启动脚本
```bash
# Windows
start.bat

# 或者手动执行
pip install -r requirements.txt
python app.py
```

### 方法2：手动启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py
```

服务将在 `http://localhost:5000` 启动

## 测试API接口

### 使用测试脚本
```bash
python test_api.py
```

### 手动测试
```bash
# 测试健康检查
curl http://localhost:5000/api/health

# 测试存储接口
curl http://localhost:5000/api/storage

# 测试数据库接口
curl http://localhost:5000/api/database

# 测试容器接口
curl http://localhost:5000/api/container

# 测试虚拟化接口
curl http://localhost:5000/api/virtualization
```

## Dify工作流配置

### 工作流结构

```
开始 → 获取存储数据 → 获取数据库数据 → 获取容器数据 → 获取虚拟化数据 → LLM生成报告 → 输出报告
```

### 输入参数

1. **报告日期** (必填)
2. **系统名称** (必填)
3. **API服务地址** (可选，默认: http://localhost:5000)

### HTTP请求节点配置

每个HTTP请求节点都配置为：
- **方法**: GET
- **URL**: `{{#start.api_base_url#}}/api/{endpoint}`
- **超时**: 30秒
- **头部**: `Content-Type: application/json`

### LLM节点配置

- **模型**: GPT-4 (推荐) 或 GPT-3.5-turbo
- **温度**: 0.2 (确保输出稳定)
- **输入**: 四个API的返回数据
- **输出**: 标准格式的容量报告

## 部署步骤

### 1. 启动Flask服务
```bash
# 进入项目目录
cd /path/to/project

# 启动Flask服务
python app.py
```

### 2. 导入Dify工作流
1. 登录Dify平台: http://************:980
2. 创建新应用 → 导入DSL
3. 上传 `dify_capacity_report_app.yml`
4. 确认配置无误后保存

### 3. 测试完整流程
1. 在Dify应用中输入：
   - 报告日期: 2024-01-15
   - 系统名称: 生产环境容量检查报告
   - API服务地址: http://localhost:5000
2. 运行工作流
3. 查看生成的容量报告

## 生成的报告格式

报告将包含四个部分，每部分都有：

### 1. 存储资源容量及健康度排查
- 资源池表格（总容量、使用率、变化情况等）
- 健康度说明（绿色/黄色/红色标准）
- 今日状态总结
- 发现问题详情
- 应对措施和预案

### 2. 数据库资源容量及健康度排查
- 数据库实例表格
- 健康度评估
- 状态分析

### 3. 容器资源容量及健康度排查
- 容器集群表格（CPU/内存/存储使用率）
- 健康度评估
- 状态分析

### 4. 虚拟化资源容量及健康度排查
- 虚拟化集群表格
- 健康度评估
- 状态分析

## 自定义配置

### 修改API数据
编辑 `app.py` 文件中的数据结构，可以：
- 添加新的资源池
- 修改容量数值
- 调整健康度阈值
- 添加新的字段

### 修改报告格式
编辑 `dify_capacity_report_app.yml` 中的LLM提示词，可以：
- 调整表格格式
- 修改健康度标准
- 添加新的分析维度
- 改变输出结构

### 集成真实数据
可以修改Flask接口，集成真实的监控系统：
- 连接存储管理系统API
- 集成数据库监控工具
- 对接容器平台API
- 连接虚拟化管理平台

## 故障排除

### 常见问题

1. **Flask服务启动失败**
   - 检查端口5000是否被占用
   - 确认Python环境和依赖包安装正确

2. **API调用失败**
   - 确认Flask服务正在运行
   - 检查防火墙设置
   - 验证API地址配置

3. **Dify工作流执行失败**
   - 检查HTTP请求节点配置
   - 确认API服务地址正确
   - 查看Dify执行日志

4. **报告生成不完整**
   - 检查LLM模型配置
   - 确认API返回数据格式正确
   - 查看提示词是否准确

## 扩展功能

### 1. 数据持久化
- 添加数据库存储历史数据
- 实现数据对比分析
- 支持趋势图表生成

### 2. 实时监控
- 集成真实监控系统
- 实现自动数据更新
- 添加告警功能

### 3. 多环境支持
- 支持多个数据中心
- 实现环境切换
- 添加权限控制

### 4. 报告定制
- 支持自定义报告模板
- 实现多种输出格式
- 添加图表和可视化

---

**注意事项：**
- 确保Flask服务在Dify工作流运行前启动
- API服务地址要在Dify中正确配置
- 建议在生产环境中使用更安全的API认证机制
