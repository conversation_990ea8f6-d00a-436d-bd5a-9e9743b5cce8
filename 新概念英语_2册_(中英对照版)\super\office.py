import os
import re
import time
from docx import Document
from docx.shared import Pt
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls

def add_code_block(doc, code):
    """在 Word 文档中添加代码块"""
    paragraph = doc.add_paragraph()
    run = paragraph.add_run(code)
    run.font.name = 'Courier New'
    run.font.size = Pt(10)
    shading_elm = parse_xml(r'<w:shd {} w:fill="D3D3D3"/>'.format(nsdecls('w')))
    paragraph._element.get_or_add_pPr().append(shading_elm)

def markdown_to_docx(md_text):
    """将 Markdown 转换为 Word，并保存到当前脚本目录下的 `docx/` 文件夹"""
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))  
    output_dir = os.path.join(script_dir, "docx")  
    
    # 确保 `docx` 目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 生成带时间戳的文件名
    timestamp = time.strftime("%Y%m%d%H%M%S")  
    filename = f"{timestamp}_output.docx"
    output_path = os.path.join(output_dir, filename)  # 确保文件保存在 `docx/`

    # 创建 Word 文档
    doc = Document()
    lines = md_text.split("\n")

    is_code_block = False  
    code_block = ""  

    for line in lines:
        line = line.strip()

        # 处理代码块
        if line.startswith("```"):  
            is_code_block = not is_code_block
            if not is_code_block and code_block:  
                add_code_block(doc, code_block.strip())
                code_block = ""  
            continue
        elif is_code_block:
            code_block += line + "\n"
            continue

        # 处理标题
        if re.match(r"^#{1,6} ", line):
            level = line.count("#")  
            doc.add_heading(line[level+1:].strip(), level=min(level, 4))  
            continue

        # 处理加粗文本
        line = re.sub(r"\*\*(.*?)\*\*", r"\1", line)  

        # 处理行内代码
        line = re.sub(r"`(.*?)`", r"\1", line)  

        # 普通段落
        if line:
            doc.add_paragraph(line)

    # 保存文件
    doc.save(output_path)
    
    # 返回可通过HTTP访问的相对URL
    download_url = f'/download/{filename}'
    print(f"Markdown 内容已转换为 {output_path}")
    print(f"下载URL：{download_url}")
    
    return download_url
