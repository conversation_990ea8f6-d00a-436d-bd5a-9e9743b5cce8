import requests
import json
import subprocess
import dify_api
import superflow
from flask import Flask, request, jsonify, render_template, send_file
import os
# 导入蓝图
from tools.api_server import api_blueprint

app = Flask(__name__, static_folder='static')
# 注册蓝图
app.register_blueprint(api_blueprint, url_prefix='/api')

@app.route('/', methods=['GET'])
def index():
    return render_template('index.html')

@app.route('/get_answer', methods=['POST'])
def get_answer():
    data = request.get_json()
    if not data:
        return jsonify({'error': '无效的JSON数据'}), 400

    question = data.get('question')
    if not question:
        return jsonify({'error': '未提供问题'}), 400

    try:
        result = superflow.superflow(user='miku318', question=question)
        
        # 确保结果是字典类型，包含 report 和 download_link
        if isinstance(result, dict) and 'report' in result and 'download_link' in result:
            return jsonify({
                'output': result  # 直接传递结果字典
            })
        else:
            # 如果结果不是预期的格式，返回一个标准格式
            return jsonify({
                'output': {
                    'report': str(result),  # 将结果转换为字符串
                    'download_link': None
                }
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 添加一个用于下载文件的路由
@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    # 确定文件所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    docx_dir = os.path.join(script_dir, "docx")
    
    # 构建完整的文件路径
    file_path = os.path.join(docx_dir, filename)
    
    # 检查文件是否存在
    if os.path.exists(file_path):
        # 设置为附件下载，并使用原始文件名
        return send_file(file_path, as_attachment=True, download_name=filename)
    else:
        return jsonify({'error': '文件不存在'}), 404

if __name__ == '__main__':
   app.run(host='0.0.0.0', port=666)
   
   # question=input("请输入内容: ")
   # ans=superflow.superflow(user='miku318',question=question)
   # print('【hello】'+ans)
