app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 生成
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: knowledge-retrieval
      id: 1749781175126-source-1749781181931-target
      source: '1749781175126'
      sourceHandle: source
      target: '1749781181931'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: knowledge-retrieval
        targetType: llm
      id: 1749781181931-source-llm-target
      source: '1749781181931'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: q
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: q
      height: 89
      id: '1749781175126'
      position:
        x: -245.2224232159387
        y: 282
      positionAbsolute:
        x: -245.2224232159387
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1749781181931'
          - result
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: deepseek-ai/DeepSeek-R1
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: edbf0770-ae0f-4310-8897-96481f6c2d2b
          role: system
          text: 请根据{{#1749781175126.q#}}中的要求根据{{#context#}}查询到的文档模式生成一个类似的报告，最后生成的结果以markdown格式输出，请注意只能输出最后的markdown内容，不需要其他的解释及思考过程，输出的Markdown可以在markdown浏览器正常显示
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: llm
      position:
        x: 696.0889692863755
        y: 282
      positionAbsolute:
        x: 696.0889692863755
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#llm.text#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1339.5347201899772
        y: 282
      positionAbsolute:
        x: 1339.5347201899772
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids:
        - r6L11EsnXQMFaXtvZyXPep3KwfEqBvV00wXlDu/56ByNPHFgTbdRgEoKxkmcvl7D
        desc: ''
        multiple_retrieval_config:
          reranking_enable: false
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 4
        query_variable_selector:
        - '1749781175126'
        - sys.query
        retrieval_mode: multiple
        selected: true
        title: 知识检索
        type: knowledge-retrieval
      height: 91
      id: '1749781181931'
      position:
        x: 207
        y: 282
      positionAbsolute:
        x: 207
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 307.0716564089879
      y: 239.6508364073984
      zoom: 0.5921358059268162
