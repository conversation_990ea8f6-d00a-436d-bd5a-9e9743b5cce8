#!/usr/bin/env python3
"""
测试Dify工作流代码节点的逻辑
验证API数据获取功能是否正常工作
"""

import requests
import json

def fetch_api_data(base_url):
    """获取所有API数据"""
    endpoints = ['storage', 'database', 'container', 'virtualization']
    results = {}
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}/api/{endpoint}"
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                results[endpoint] = response.json()
            else:
                results[endpoint] = {
                    "error": f"HTTP {response.status_code}",
                    "message": f"无法获取{endpoint}数据"
                }
        except Exception as e:
            results[endpoint] = {
                "error": "连接失败",
                "message": f"无法连接到{endpoint}接口: {str(e)}"
            }
    
    return results

def test_api_fetch():
    """测试API数据获取功能"""
    
    # 测试参数
    api_base_url = "http://localhost:5000"
    
    print("开始测试API数据获取功能...")
    print(f"API基础地址: {api_base_url}")
    
    try:
        # 获取所有API数据
        api_data = fetch_api_data(api_base_url)
        
        # 模拟Dify代码节点的返回格式
        result = {
            "storage_data": json.dumps(api_data.get('storage', {}), ensure_ascii=False, indent=2),
            "database_data": json.dumps(api_data.get('database', {}), ensure_ascii=False, indent=2),
            "container_data": json.dumps(api_data.get('container', {}), ensure_ascii=False, indent=2),
            "virtualization_data": json.dumps(api_data.get('virtualization', {}), ensure_ascii=False, indent=2),
            "api_status": "success" if all('error' not in data for data in api_data.values()) else "partial_failure"
        }
        
        print(f"[PASS] API数据获取成功")
        print(f"API状态: {result['api_status']}")
        
        # 显示各个端点的状态
        for endpoint, data in api_data.items():
            if 'error' in data:
                print(f"[WARN] {endpoint}: {data['message']}")
            else:
                print(f"[PASS] {endpoint}: 数据获取成功")
        
        # 显示数据样例
        print("\n=== 存储数据样例 ===")
        storage_data = api_data.get('storage', {})
        if 'error' not in storage_data and 'data' in storage_data:
            pools = storage_data['data']
            if isinstance(pools, list) and len(pools) > 0:
                for i, pool in enumerate(pools[:2]):  # 显示前2个存储池
                    print(f"存储池{i+1}: {pool.get('pool_name', 'N/A')}")
                    print(f"  总容量: {pool.get('total_capacity_gb', 'N/A')} GB")
                    print(f"  使用率: {pool.get('usage_percentage', 'N/A')}%")
            else:
                print("  无存储池数据")
        else:
            print("  存储数据获取失败或格式错误")

        print("\n=== 数据库数据样例 ===")
        database_data = api_data.get('database', {})
        if 'error' not in database_data and 'data' in database_data:
            instances = database_data['data']
            if isinstance(instances, list) and len(instances) > 0:
                for i, instance in enumerate(instances[:2]):  # 显示前2个数据库实例
                    print(f"数据库{i+1}: {instance.get('instance_name', 'N/A')}")
                    print(f"  总容量: {instance.get('total_capacity_gb', 'N/A')} GB")
                    print(f"  使用率: {instance.get('usage_percentage', 'N/A')}%")
            else:
                print("  无数据库实例数据")
        else:
            print("  数据库数据获取失败或格式错误")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] API数据获取失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_url_format():
    """测试URL格式处理"""
    
    test_cases = [
        "localhost:5000",
        "http://localhost:5000",
        "https://localhost:5000",
        "*************:5000",
        "http://*************:5000"
    ]
    
    print("\n=== 测试URL格式处理 ===")
    
    for test_url in test_cases:
        # 模拟代码节点中的URL处理逻辑
        api_base_url = test_url
        if not api_base_url.startswith('http'):
            api_base_url = f"http://{api_base_url}"
        
        print(f"输入: {test_url} -> 输出: {api_base_url}")

if __name__ == "__main__":
    print("Dify工作流代码节点测试")
    print("=" * 50)
    
    # 测试URL格式处理
    test_url_format()
    
    # 测试API数据获取
    success = test_api_fetch()
    
    if success:
        print("\n[PASS] 所有测试通过，代码节点逻辑正常")
    else:
        print("\n[FAIL] 测试失败，请检查API服务是否正常运行")
        print("提示：请先启动Flask API服务 (python app.py)")
    
    exit(0 if success else 1)
