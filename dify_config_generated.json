{"api_base_url": "http://localhost:5000", "workflow_steps": [{"step": 1, "name": "开始节点", "type": "start", "variables": [{"name": "api_base_url", "type": "text-input", "label": "API服务地址", "default": "http://localhost:5000", "required": true}, {"name": "report_date", "type": "text-input", "label": "报告日期", "default": "2024-01-15", "required": true}, {"name": "system_name", "type": "text-input", "label": "系统名称", "default": "生产环境运维资源容量检查报告", "required": true}]}, {"step": 2, "name": "生成智能报告", "type": "http-request", "method": "POST", "url": "http://localhost:5000/api/generate_smart_report", "headers": {"Content-Type": "application/json"}, "body": {"report_date": "{{#start.report_date#}}", "system_name": "{{#start.system_name#}}"}, "timeout": 60}, {"step": 3, "name": "导出Word文档", "type": "http-request", "method": "POST", "url": "http://localhost:5000/api/export_word", "headers": {"Content-Type": "application/json"}, "body": {"report_content": "{{#生成智能报告.body.report_content#}}", "report_date": "{{#生成智能报告.body.report_date#}}", "system_name": "{{#生成智能报告.body.system_name#}}", "save_path": "./reports/"}, "timeout": 30}, {"step": 4, "name": "结束节点", "type": "end", "outputs": ["success", "file_path", "file_size", "report_type", "data_source"]}]}