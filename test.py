import asyncio
from autogen_agentchat.agents import Assistant<PERSON><PERSON>,UserProxyAgent
from autogen_agentchat.ui import <PERSON>sole
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from autogen_ext.tools.mcp import SseServerParams, mcp_server_tools
from autogen_agentchat.teams import RoundRobinGroupChat,MagenticOneGroupChat,Swarm,SelectorGroupChat
from autogen_agentchat.conditions import HandoffTermination,TextMentionTermination, MaxMessageTermination

from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor
from autogen_ext.tools.code_execution import PythonCodeExecutionTool

import logging
from autogen_core import EVENT_LOGGER_NAME

# 启用详细日志记录
# logging.basicConfig(level=logging.WARNING)
# logger = logging.getLogger(EVENT_LOGGER_NAME)
# logger.addHandler(logging.StreamHandler())
# logger.setLevel(logging.DEBUG)

# 创建 MCP 客户端并连接到你的 MCP 服务
server_params = SseServerParams(
        url="http://localhost:8000/sse",  # 使用回环地址
        timeout=30,  # 增加超时时间
        sse_read_timeout=600,  # 增加SSE读取超时时间
    )

# Define a model client. You can use other model client that implements
model_client = OpenAIChatCompletionClient(
    model="Qwen/Qwen2.5-72B-Instruct-128K",
    base_url="https://api.siliconflow.cn/v1",
    api_key="sk-klqzerngqilswjgvubekqcevgmsynkpdhzdsccrhjxprhjcf",
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "structured_output": True,  # 添加structured_output字段
        "family": "unknown",
    },
)

# 流式传输
async def assistant_run_stream() -> None:
    # Option 2: use Console to print all messages as they appear.
    await Console(
        agent.on_messages_stream(
            [TextMessage(content="Find information on AutoGen", source="user")],
            cancellation_token=CancellationToken(),
        ),
        output_stats=True,  # Enable stats printing.
    )

# Run the agent and stream the messages to the console.
async def main() -> None:
    try:
        # 获取工具列表 - 使用 await 调用异步函数
        print("正在连接MCP服务器...")
        tools = await mcp_server_tools(server_params)
        print(f"成功获取工具: {tools}")
        autogen_tool = PythonCodeExecutionTool(LocalCommandLineCodeExecutor(work_dir="coding"))
        all_tools = tools + [autogen_tool]
        
        user_proxy = UserProxyAgent("user_proxy",input_func=input)
        # Define an AssistantAgent with the model, tool, system message, and reflection enabled.
        # The system message instructs the agent via natural language.
        agent = AssistantAgent(
            name="miku_agent",
            model_client=model_client,  # 确保你已经定义了 model_client
            tools=all_tools,        # 使用从 MCP 服务获取的工具
            system_message="""
            你是一个AI助手，擅长回答用户提出的问题。
            你会使用mcp工具帮助用户解决问题.
            """,
            reflect_on_tool_use=True,
            model_client_stream=True   # 启用流式传输
          
        )
        

        coder_agent = AssistantAgent(
            "coder_executor",OpenAIChatCompletionClient(
                model="Qwen/Qwen2.5-72B-Instruct-128K",
                base_url="https://api.siliconflow.cn/v1",
                api_key="sk-klqzerngqilswjgvubekqcevgmsynkpdhzdsccrhjxprhjcf",
                model_info={
                    "vision": True,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,  # 添加structured_output字段
                    "family": "unknown",
                },
            ), tools=[autogen_tool], reflect_on_tool_use=True
        )



        
        termination = TextMentionTermination("exit") 
        group_chat = RoundRobinGroupChat([agent,user_proxy],
                                       termination_condition=termination
                                       )
        task = input("请输入您想要的: ")
        await Console(group_chat.run_stream(task=task))

    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # Close the connection to the model client.
        await model_client.close()

# NOTE: if running this inside a Python script you'll need to use asyncio.run(main()).
asyncio.run(main())