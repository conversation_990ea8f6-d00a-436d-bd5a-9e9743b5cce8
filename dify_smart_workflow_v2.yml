version: "0.1.0"
kind: app
data:
  title: "智能容量报告生成器"
  description: "通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告"
  default_language: "zh-Hans"
  icon: "📊"
  icon_background: "#3B82F6"
  mode: "workflow"
  
  workflow:
    conversation_variables: []
    environment_variables: []
    
    graph:
      edges:
        - id: "start-generate_smart_report"
          source: "start"
          target: "generate_smart_report"
        - id: "generate_smart_report-export_word"
          source: "generate_smart_report"
          target: "export_word"
        - id: "export_word-end"
          source: "export_word"
          target: "end"
      
      nodes:
        - id: "start"
          data:
            type: "start"
            title: "开始"
            desc: "智能容量报告生成工作流开始节点"
            selected: false
            variables:
              - variable: "api_base_url"
                type: "text-input"
                label: "API服务地址"
                description: "Flask API服务的基础URL地址"
                required: true
                max_length: 256
                default: "http://************:5000"
                options: []
              - variable: "report_date"
                type: "text-input"
                label: "报告日期"
                description: "容量报告的日期，格式：YYYY-MM-DD"
                required: true
                max_length: 256
                default: "2024-01-15"
                options: []
              - variable: "system_name"
                type: "text-input"
                label: "系统名称"
                description: "容量报告的系统名称"
                required: true
                max_length: 256
                default: "生产环境运维资源容量检查报告"
                options: []
              - variable: "llm_api_url"
                type: "text-input"
                label: "LLM API地址"
                description: "LLM服务的API地址（可选，留空使用本地分析）"
                required: false
                max_length: 256
                default: ""
                options: []
              - variable: "llm_api_key"
                type: "text-input"
                label: "LLM API密钥"
                description: "LLM服务的API密钥（可选）"
                required: false
                max_length: 256
                default: ""
                options: []
              - variable: "save_path"
                type: "text-input"
                label: "保存路径"
                description: "Word文档保存路径"
                required: true
                max_length: 256
                default: "./reports/"
                options: []
          position:
            x: 80
            y: 282
          positionAbsolute:
            x: 80
            y: 282
          height: 116
          width: 244
          selected: false
          sourcePosition: "right"
          targetPosition: "left"
          type: "custom"
        
        - id: "generate_smart_report"
          data:
            type: "http-request"
            title: "生成智能容量报告"
            desc: "调用API生成智能容量分析报告"
            selected: false
            method: "post"
            url: "{{#start.api_base_url#}}/api/generate_smart_report"
            headers: "Content-Type: application/json"
            params: ""
            body:
              type: "json"
              data: |
                {
                  "report_date": "{{#start.report_date#}}",
                  "system_name": "{{#start.system_name#}}",
                  "llm_config": {
                    "api_url": "{{#start.llm_api_url#}}",
                    "api_key": "{{#start.llm_api_key#}}"
                  }
                }
            authorization:
              type: "no-auth"
              config: null
            timeout:
              connect: 10
              read: 60
              write: 10
          position:
            x: 384
            y: 282
          positionAbsolute:
            x: 384
            y: 282
          height: 116
          width: 244
          selected: false
          sourcePosition: "right"
          targetPosition: "left"
          type: "custom"
        
        - id: "export_word"
          data:
            type: "http-request"
            title: "导出Word文档"
            desc: "将生成的报告导出为Word文档格式"
            selected: false
            method: "post"
            url: "{{#start.api_base_url#}}/api/export_word"
            headers: "Content-Type: application/json"
            params: ""
            body:
              type: "json"
              data: |
                {
                  "report_content": "{{#generate_smart_report.body.report_content#}}",
                  "report_date": "{{#generate_smart_report.body.report_date#}}",
                  "system_name": "{{#generate_smart_report.body.system_name#}}",
                  "save_path": "{{#start.save_path#}}"
                }
            authorization:
              type: "no-auth"
              config: null
            timeout:
              connect: 10
              read: 30
              write: 10
          position:
            x: 688
            y: 282
          positionAbsolute:
            x: 688
            y: 282
          height: 116
          width: 244
          selected: false
          sourcePosition: "right"
          targetPosition: "left"
          type: "custom"
        
        - id: "end"
          data:
            type: "end"
            title: "完成"
            desc: "智能容量报告生成工作流结束节点"
            selected: false
            outputs:
              - variable: "report_success"
                type: "boolean"
                value_selector:
                  - "generate_smart_report"
                  - "body"
                  - "success"
              - variable: "report_type"
                type: "string"
                value_selector:
                  - "generate_smart_report"
                  - "body"
                  - "report_type"
              - variable: "data_source"
                type: "string"
                value_selector:
                  - "generate_smart_report"
                  - "body"
                  - "data_source"
              - variable: "llm_enabled"
                type: "boolean"
                value_selector:
                  - "generate_smart_report"
                  - "body"
                  - "llm_enabled"
              - variable: "word_file_path"
                type: "string"
                value_selector:
                  - "export_word"
                  - "body"
                  - "file_path"
              - variable: "word_file_size"
                type: "string"
                value_selector:
                  - "export_word"
                  - "body"
                  - "file_size"
              - variable: "word_file_type"
                type: "string"
                value_selector:
                  - "export_word"
                  - "body"
                  - "file_type"
          position:
            x: 992
            y: 282
          positionAbsolute:
            x: 992
            y: 282
          height: 116
          width: 244
          selected: false
          sourcePosition: "right"
          targetPosition: "left"
          type: "custom"
      
      viewport:
        x: 0
        y: 0
        zoom: 1
  
  model_config:
    agent_mode:
      enabled: false
      max_iteration: 5
      strategy: "function_call"
      tools: []
    
    annotation_reply:
      enabled: false
    
    chat_prompt_config: {}
    completion_prompt_config: {}
    
    dataset_configs:
      datasets:
        datasets: []
        retrieval_model: "single"
      external_retrieval_model:
        enabled: false
      reranking_model:
        enabled: false
        mode: ""
        model: ""
    
    dataset_query_variable: ""
    
    file_upload:
      image:
        detail: "high"
        enabled: false
        number_limits: 3
        transfer_methods:
          - "remote_url"
          - "local_file"
    
    model:
      completion_params: {}
      mode: ""
      name: ""
      provider: ""
    
    more_like_this:
      enabled: false
    
    opening_statement: |
      欢迎使用智能容量报告生成器！
      
      本工具将为您提供：
      🔍 自动获取系统容量数据
      🧠 使用LLM进行智能分析
      📊 生成专业的容量报告
      📄 导出为Word文档格式
      
      请填写以下参数开始生成报告：
    
    pre_prompt: ""
    prompt_type: "simple"
    
    retriever_resource:
      enabled: false
    
    sensitive_word_avoidance:
      enabled: false
      type: ""
      configs: []
    
    speech_to_text:
      enabled: false
    
    suggested_questions:
      - "生成今日的生产环境容量报告"
      - "使用LLM分析生成智能容量报告"
      - "生成包含风险评估的容量报告"
      - "导出专业格式的Word容量报告"
    
    suggested_questions_after_answer:
      enabled: false
    
    text_to_speech:
      enabled: false
      language: ""
      voice: ""
    
    user_input_form:
      - text-input:
          default: "http://************:5000"
          label: "API服务地址"
          max_length: 256
          variable: "api_base_url"
      - text-input:
          default: "2024-01-15"
          label: "报告日期"
          max_length: 256
          variable: "report_date"
      - text-input:
          default: "生产环境运维资源容量检查报告"
          label: "系统名称"
          max_length: 256
          variable: "system_name"
      - text-input:
          default: ""
          label: "LLM API地址（可选）"
          max_length: 256
          variable: "llm_api_url"
      - text-input:
          default: ""
          label: "LLM API密钥（可选）"
          max_length: 256
          variable: "llm_api_key"
      - text-input:
          default: "./reports/"
          label: "保存路径"
          max_length: 256
          variable: "save_path"
