# Dify智能容量报告系统使用说明

## 🎯 系统概述

智能容量报告系统现已升级为使用Dify原生LLM节点的工作流，实现了更加智能和专业的容量分析报告生成。

### 🔄 新工作流设计

```
开始节点 → 获取容量数据 → LLM智能分析 → 导出Word文档 → 输出结果
```

## 📋 工作流节点说明

### 1. 开始节点 (start)
**功能**：配置工作流参数
**输入参数**：
- `api_base_url`：API服务地址（默认：http://192.168.233.119:5000）
- `report_date`：报告日期（默认：2025-06-30）
- `system_name`：系统名称（默认：生产环境运维资源容量检查报告）
- `enable_llm`：启用LLM分析（默认：true）

### 2. HTTP请求节点 - 获取容量数据 (get-capacity-data)
**功能**：通过API获取所有容量数据
**请求方式**：POST
**端点**：`/api/get_all_capacity_data`
**输出**：包含存储、数据库、容器、虚拟化的完整容量数据

### 3. LLM节点 - 智能分析 (llm-analysis)
**功能**：使用Dify原生LLM节点进行智能分析
**模型配置**：
- 模型：gpt-4（可配置）
- 温度：0.2（确保输出稳定）
- 模式：chat

**系统提示词**：专业的IT容量规划专家角色，包含：
- 详细的报告格式要求
- 健康度评估标准
- 表格格式规范
- 风险评估指导

### 4. HTTP请求节点 - 导出Word文档 (export-word)
**功能**：将LLM分析结果导出为Word文档
**请求方式**：POST
**端点**：`/api/export_word`
**输入**：LLM分析生成的报告内容

### 5. 答案节点 (answer)
**功能**：输出完整的工作流执行结果

## 🚀 使用步骤

### 步骤1：导入工作流配置

1. **选择配置文件**：
   - 推荐：`智能容量报告系统.json`（JSON格式，兼容性更好）
   - 备选：`智能容量报告系统.yml`（YAML格式，更易读）

2. **在Dify平台导入**：
   - 登录Dify平台：http://172.30.224.1:980
   - 选择"工作流" → "导入"
   - 上传配置文件
   - 确认导入成功

### 步骤2：配置LLM模型

1. **模型选择**：
   - 推荐：GPT-4（分析质量最佳）
   - 备选：Claude-3、GPT-3.5-turbo

2. **参数配置**：
   - 温度：0.2（确保输出稳定）
   - 最大令牌：4000（支持长报告）
   - 模式：chat

### 步骤3：运行工作流

1. **配置参数**：
   ```
   API服务地址：http://192.168.233.119:5000
   报告日期：2025-06-30
   系统名称：生产环境运维资源容量检查报告
   启用LLM分析：true
   ```

2. **执行工作流**：
   - 点击"运行"按钮
   - 等待执行完成（预计2-3分钟）
   - 查看输出结果

## 📊 报告格式说明

### 报告结构
1. **存储资源容量及健康度排查**
   - 详细的存储池使用情况表格
   - 基于使用率的健康度评估（绿色<90%，黄色90-95%，红色>95%）

2. **数据库资源容量及健康度排查**
   - 数据库实例容量分析
   - 健康度阈值（绿色<85%，黄色85-95%，红色>95%）

3. **容器资源容量及健康度排查**
   - Kubernetes集群资源使用情况
   - CPU/内存/存储多维度分析

4. **虚拟化资源容量及健康度排查**
   - VMware/Hyper-V集群分析
   - 资源池使用率统计

5. **总体风险评估和建议**
   - 整体健康度评估
   - 主要风险点识别
   - 优化建议和行动计划

## 🎯 系统优势

### 1. 使用Dify原生LLM节点
- ✅ 无需外部LLM API配置
- ✅ 更好的稳定性和可靠性
- ✅ 统一的模型管理

### 2. 专业的容量分析
- ✅ 基于行业标准的健康度阈值
- ✅ 多维度资源分析
- ✅ 智能风险识别

### 3. 标准化报告格式
- ✅ 专业的表格格式
- ✅ 清晰的健康度指标
- ✅ 详细的应对措施

### 4. 自动化文档生成
- ✅ 原生Word文档格式
- ✅ 专业的排版和格式
- ✅ 即时可用的报告文档

## 🔧 故障排除

### 常见问题

1. **工作流导入失败**
   - 检查Dify平台版本兼容性
   - 尝试使用JSON格式配置文件
   - 确认配置文件格式正确

2. **API连接失败**
   - 确认Flask API服务正在运行
   - 检查IP地址和端口配置
   - 验证网络连接

3. **LLM分析失败**
   - 检查LLM模型配置
   - 确认API密钥有效
   - 验证模型访问权限

4. **Word导出失败**
   - 检查保存路径权限
   - 确认磁盘空间充足
   - 验证报告内容格式

### 测试工具

使用 `测试Dify工作流配置.py` 脚本验证系统功能：

```bash
python 测试Dify工作流配置.py
```

## 📈 性能指标

- **数据获取**：< 1秒
- **LLM分析**：30-60秒（取决于模型）
- **Word导出**：< 1秒
- **总执行时间**：2-3分钟
- **报告长度**：3000-5000字符
- **文档大小**：30-50KB

## 🎉 总结

新的Dify智能容量报告系统实现了：

1. **完全集成的工作流**：使用Dify原生节点，无需外部依赖
2. **专业的分析能力**：基于LLM的智能容量分析
3. **标准化的输出**：符合企业级报告要求
4. **自动化的流程**：一键生成完整的容量报告

系统已准备就绪，可以立即在Dify平台中使用！

## 📋 文件清单

### 配置文件
- `智能容量报告系统.yml` - YAML格式工作流配置
- `智能容量报告系统.json` - JSON格式工作流配置

### API服务
- `app.py` - Flask API服务器
- 新增端点：`/api/get_all_capacity_data` - 获取所有容量数据

### 测试工具
- `测试Dify工作流配置.py` - 工作流功能测试脚本
- `测试智能容量报告系统.py` - 完整系统测试脚本

### 文档
- `Dify智能容量报告系统使用说明.md` - 本使用说明
- `配置文件更新说明.md` - 配置文件变更详情

所有文件已准备就绪，可以立即开始使用！
