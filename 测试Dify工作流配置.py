#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dify工作流配置和API端点
验证新的工作流设计：获取容量数据 → LLM分析 → 导出Word文档
"""

import requests
import json
import time
from datetime import datetime
import os

def test_api_connection(base_url):
    """测试API连接"""
    print("🔍 测试API连接...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API连接成功: {data.get('message', 'API服务正常')}")
            return True
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接异常: {str(e)}")
        return False

def test_get_all_capacity_data(base_url):
    """测试获取所有容量数据API（新增的端点）"""
    print("\n📊 测试获取所有容量数据API...")
    
    test_data = {
        "report_date": "2025-06-30",
        "system_name": "生产环境运维资源容量检查报告"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/get_all_capacity_data",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 容量数据获取成功!")
                print(f"📊 数据统计:")
                
                capacity_data = data.get('data', {})
                summary = capacity_data.get('summary', {})
                
                print(f"   - 存储池数量: {summary.get('total_storage_pools', 0)}")
                print(f"   - 数据库实例数量: {summary.get('total_database_instances', 0)}")
                print(f"   - 容器集群数量: {summary.get('total_container_clusters', 0)}")
                print(f"   - 虚拟化集群数量: {summary.get('total_vm_clusters', 0)}")
                print(f"   - 数据收集时间: {capacity_data.get('report_info', {}).get('data_collection_time', 'N/A')}")
                print(f"   - 请求耗时: {end_time - start_time:.2f} 秒")
                
                # 显示数据结构示例
                print(f"\n📋 数据结构示例:")
                storage_data = capacity_data.get('storage_capacity', {})
                if storage_data.get('pools'):
                    first_pool = storage_data['pools'][0]
                    print(f"   存储池示例: {first_pool.get('name', 'N/A')} - {first_pool.get('usage_percent', 0)}% 使用率")
                
                return capacity_data
            else:
                print(f"❌ 容量数据获取失败: {data.get('message', '未知错误')}")
                return None
        else:
            print(f"❌ 容量数据获取请求失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 容量数据获取异常: {str(e)}")
        return None

def simulate_llm_analysis(capacity_data):
    """模拟LLM分析过程（用于测试）"""
    print("\n🧠 模拟LLM分析过程...")
    
    if not capacity_data:
        print("❌ 没有容量数据可供分析")
        return None
    
    # 模拟LLM分析生成的报告内容
    report_info = capacity_data.get('report_info', {})
    system_name = report_info.get('system_name', '容量检查报告')
    report_date = report_info.get('report_date', datetime.now().strftime('%Y-%m-%d'))
    
    mock_report = f"""# {system_name}

**报告日期：** {report_date}
**数据来源：** API自动获取 + Dify LLM智能分析
**生成时间：** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 1. 存储资源容量及健康度排查

存储资源池本次排查情况如下：

| 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|-------------|------------|-------------------|------------------------|----------|
| 存储池1 | 生产存储池-SAN | 10240 | 78.5 | +2.3% | 正常 | 持续监控 |
| 存储池2 | 备份存储池-NAS | 5120 | 65.2 | +1.8% | 正常 | 持续监控 |
| 存储池3 | 归档存储池-对象存储 | 20480 | 45.6 | +0.9% | 正常 | 持续监控 |

**健康度说明：**
- 🟢 绿色：正常值 （存储使用率<90%）运行良好。
- 🟡 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 🔴 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 🟢 所有存储池运行正常，使用率均在安全范围内

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 2. 数据库资源容量及健康度排查

数据库资源池本次排查情况如下：

| 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|-------------|------------|-------------------|------------------------|----------|
| 数据库1 | 生产MySQL集群 | 2048 | 72.3 | +1.5% | 正常 | 持续监控 |
| 数据库2 | 分析PostgreSQL集群 | 4096 | 58.9 | +2.1% | 正常 | 持续监控 |

**健康度说明：**
- 🟢 绿色：正常值 （数据库使用率<85%）运行良好。
- 🟡 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 🔴 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 🟢 所有数据库集群运行正常

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 3. 容器资源容量及健康度排查

容器资源池本次排查情况如下：

| 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
| K8S集群1 | 生产Kubernetes集群 | 65.4 | 71.2 | 58.9 | CPU+3.2%, 内存+2.8% | 正常 | 持续监控 |
| K8S集群2 | 测试Kubernetes集群 | 45.6 | 52.3 | 41.7 | CPU+1.8%, 内存+1.5% | 正常 | 持续监控 |

**健康度说明：**
- 🟢 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。
- 🟡 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 🔴 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 🟢 所有容器集群运行正常

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 4. 虚拟化资源容量及健康度排查

虚拟化资源池本次排查情况如下：

| 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
|--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
| VMware集群1 | 生产vSphere集群 | 68.7 | 74.5 | 62.3 | CPU+2.1%, 内存+3.4% | 正常 | 持续监控 |
| VMware集群2 | 开发vSphere集群 | 42.8 | 48.9 | 35.6 | CPU+1.2%, 内存+0.8% | 正常 | 持续监控 |

**健康度说明：**
- 🟢 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。
- 🟡 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
- 🔴 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

**今日状态：** 🟢 所有虚拟化集群运行正常

**发现问题详情：** 今日未发现问题

**应对措施和预案：** 不涉及

## 5. 总体风险评估和建议

**整体健康度评估：** 🟢 系统整体运行状况良好，所有资源池使用率均在正常范围内

**主要风险点：** 暂无重大风险点，建议继续保持现有监控频率

**优化建议：** 
1. 继续监控存储池使用率增长趋势
2. 关注生产环境资源使用率变化
3. 建议制定容量扩展预案

**下一步行动计划：** 
1. 维持日常监控
2. 准备季度容量规划评估
3. 更新容量预警阈值配置

---

**报告生成信息：**
- 分析引擎：Dify LLM智能分析
- 数据来源：API自动获取
- 报告格式：专业容量规划报告
- 生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    print("✅ LLM分析完成（模拟）")
    print(f"   报告长度: {len(mock_report)} 字符")
    print(f"   预计阅读时间: {len(mock_report) // 500 + 1} 分钟")
    
    return mock_report

def test_word_export(base_url, report_content, report_date, system_name):
    """测试Word文档导出"""
    print("\n📄 测试Word文档导出...")
    
    test_data = {
        "report_content": report_content,
        "report_date": report_date,
        "system_name": system_name,
        "save_path": "./reports/"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/export_word",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Word文档导出成功!")
                print(f"📄 文档信息:")
                print(f"   - 文件路径: {data.get('file_path', 'N/A')}")
                print(f"   - 文件名称: {data.get('file_name', 'N/A')}")
                print(f"   - 文件大小: {data.get('file_size', 'N/A')}")
                print(f"   - 文件类型: {data.get('file_type', 'N/A')}")
                print(f"   - 导出耗时: {end_time - start_time:.2f} 秒")
                return data
            else:
                print(f"❌ Word文档导出失败: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ Word文档导出请求失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Word文档导出异常: {str(e)}")
        return None

def test_dify_workflow_simulation():
    """测试Dify工作流模拟"""
    print("🚀 开始测试Dify工作流模拟")
    print("=" * 60)
    
    # 配置参数
    base_url = "http://192.168.233.119:5000"
    report_date = "2025-06-30"
    system_name = "生产环境运维资源容量检查报告"
    
    print(f"📋 测试配置:")
    print(f"   - API地址: {base_url}")
    print(f"   - 报告日期: {report_date}")
    print(f"   - 系统名称: {system_name}")
    print("=" * 60)
    
    # 步骤1: 测试API连接
    if not test_api_connection(base_url):
        print("❌ API连接失败，终止测试")
        return False
    
    # 步骤2: 获取容量数据（对应Dify工作流的第一个HTTP请求节点）
    capacity_data = test_get_all_capacity_data(base_url)
    if not capacity_data:
        print("❌ 容量数据获取失败，终止测试")
        return False
    
    # 步骤3: 模拟LLM分析（对应Dify工作流的LLM节点）
    report_content = simulate_llm_analysis(capacity_data)
    if not report_content:
        print("❌ LLM分析失败，终止测试")
        return False
    
    # 步骤4: 导出Word文档（对应Dify工作流的第二个HTTP请求节点）
    word_result = test_word_export(base_url, report_content, report_date, system_name)
    if not word_result:
        print("❌ Word文档导出失败，终止测试")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Dify工作流模拟测试完成!")
    print("✅ 所有步骤执行成功:")
    print("   1. ✅ API连接正常")
    print("   2. ✅ 容量数据获取成功")
    print("   3. ✅ LLM分析完成（模拟）")
    print("   4. ✅ Word文档导出成功")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    print("🔧 Dify工作流配置测试工具")
    print("测试新的工作流设计：获取容量数据 → LLM分析 → 导出Word文档")
    print()
    
    # 运行测试
    success = test_dify_workflow_simulation()
    
    if success:
        print("\n🎯 测试结论:")
        print("✅ 新的Dify工作流配置可以正常工作")
        print("✅ API端点功能完整")
        print("✅ 数据流转正常")
        print("✅ 可以导入到Dify平台使用")
        print("\n📋 下一步:")
        print("1. 在Dify平台导入 智能容量报告系统.yml 配置文件")
        print("2. 配置LLM模型（如GPT-4、Claude等）")
        print("3. 运行工作流验证实际效果")
    else:
        print("\n❌ 测试失败，请检查API服务和配置")
