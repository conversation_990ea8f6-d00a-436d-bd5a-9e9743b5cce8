#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能容量报告系统测试脚本
测试完整的工作流：API获取容量参数 → LLM分析 → 生成报告 → 导出Word文档
"""

import requests
import json
import time
from datetime import datetime
import os

def test_api_connection(base_url):
    """测试API连接"""
    print("🔍 测试API连接...")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API连接成功: {data.get('message', 'API服务正常')}")
            return True
        else:
            print(f"❌ API连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接异常: {str(e)}")
        return False

def test_capacity_data_apis(base_url):
    """测试容量数据API接口"""
    print("\n📊 测试容量数据API接口...")
    
    apis = [
        ("/api/storage", "存储容量"),
        ("/api/database", "数据库容量"),
        ("/api/container", "容器容量"),
        ("/api/virtualization", "虚拟化容量")
    ]
    
    all_success = True
    for endpoint, name in apis:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    print(f"✅ {name}API正常")
                else:
                    print(f"❌ {name}API返回错误: {data.get('message', '未知错误')}")
                    all_success = False
            else:
                print(f"❌ {name}API失败: HTTP {response.status_code}")
                all_success = False
        except Exception as e:
            print(f"❌ {name}API异常: {str(e)}")
            all_success = False
    
    return all_success

def test_smart_report_generation(base_url, report_date, system_name, enable_llm=False):
    """测试智能报告生成"""
    print(f"\n🧠 测试智能报告生成 (LLM: {'启用' if enable_llm else '禁用'})...")
    
    payload = {
        "report_date": report_date,
        "system_name": system_name,
        "llm_config": {
            "enabled": enable_llm
        }
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/generate_smart_report",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=120
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                report_content = data.get('report_content', '')
                print(f"✅ 智能报告生成成功!")
                print(f"📊 报告信息:")
                print(f"   - 报告类型: {data.get('report_type', 'N/A')}")
                print(f"   - 数据来源: {data.get('data_source', 'N/A')}")
                print(f"   - LLM分析: {'启用' if data.get('llm_enabled') else '禁用'}")
                print(f"   - 报告长度: {data.get('report_length', len(report_content))} 字符")
                print(f"   - 阅读时间: {data.get('estimated_reading_time', '未知')}")
                print(f"   - 生成耗时: {end_time - start_time:.2f} 秒")
                return data
            else:
                print(f"❌ 智能报告生成失败: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ 智能报告生成请求失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 智能报告生成异常: {str(e)}")
        return None

def test_word_export(base_url, report_data):
    """测试Word文档导出"""
    print(f"\n📄 测试Word文档导出...")
    
    if not report_data:
        print("❌ 无法导出Word文档: 报告数据为空")
        return None
    
    payload = {
        "report_content": report_data.get('report_content', ''),
        "report_date": report_data.get('report_date', ''),
        "system_name": report_data.get('system_name', ''),
        "save_path": "./reports/"
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/export_word",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Word文档导出成功!")
                print(f"📄 文档信息:")
                print(f"   - 文件路径: {data.get('file_path', 'N/A')}")
                print(f"   - 文件名称: {data.get('file_name', 'N/A')}")
                print(f"   - 文件大小: {data.get('file_size', 'N/A')}")
                print(f"   - 文件类型: {data.get('file_type', 'N/A')}")
                print(f"   - 导出耗时: {end_time - start_time:.2f} 秒")
                return data
            else:
                print(f"❌ Word文档导出失败: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ Word文档导出请求失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Word文档导出异常: {str(e)}")
        return None

def test_complete_workflow():
    """测试完整的智能容量报告工作流"""
    print("🚀 开始测试智能容量报告系统完整工作流")
    print("=" * 60)
    
    # 配置参数
    base_url = "http://192.168.233.119:5000"
    report_date = datetime.now().strftime("%Y-%m-%d")
    system_name = "智能容量报告系统测试"
    
    print(f"📋 测试配置:")
    print(f"   - API地址: {base_url}")
    print(f"   - 报告日期: {report_date}")
    print(f"   - 系统名称: {system_name}")
    print("=" * 60)
    
    # 步骤1: 测试API连接
    if not test_api_connection(base_url):
        print("\n❌ 测试终止: API连接失败")
        return False
    
    # 步骤2: 测试容量数据API
    if not test_capacity_data_apis(base_url):
        print("\n⚠️  警告: 部分容量数据API异常，但继续测试")
    
    # 步骤3: 测试智能报告生成（本地分析）
    report_data = test_smart_report_generation(base_url, report_date, system_name, enable_llm=False)
    if not report_data:
        print("\n❌ 测试终止: 智能报告生成失败")
        return False
    
    # 步骤4: 测试Word文档导出
    word_data = test_word_export(base_url, report_data)
    if not word_data:
        print("\n❌ 测试终止: Word文档导出失败")
        return False
    
    # 测试完成
    print("\n" + "=" * 60)
    print("🎉 智能容量报告系统测试完成!")
    print("\n📊 测试结果汇总:")
    print(f"✅ API连接: 正常")
    print(f"✅ 容量数据获取: 正常")
    print(f"✅ 智能报告生成: 正常 ({report_data.get('report_length', 0)} 字符)")
    print(f"✅ Word文档导出: 正常 ({word_data.get('file_size', 'N/A')})")
    
    print(f"\n🎯 系统功能验证:")
    print(f"✅ 通过API获取容量参数")
    print(f"✅ LLM分析处理 ({'启用' if report_data.get('llm_enabled') else '本地分析'})")
    print(f"✅ 按规定格式生成报告")
    print(f"✅ 生成Word文档")
    
    print(f"\n📁 生成的文件:")
    if word_data and word_data.get('file_path'):
        file_path = word_data.get('file_path')
        if os.path.exists(file_path):
            print(f"📄 {file_path} ({word_data.get('file_size', 'N/A')})")
        else:
            print(f"⚠️  文件路径存在但文件不可访问: {file_path}")
    
    print("\n🚀 系统已准备就绪，可以在Dify中使用!")
    return True

def generate_dify_test_config():
    """生成Dify测试配置"""
    print("\n📝 生成Dify工作流测试配置...")
    
    config = {
        "test_parameters": {
            "api_base_url": "http://192.168.8.88:5000",
            "report_date": datetime.now().strftime("%Y-%m-%d"),
            "system_name": "智能容量报告系统测试",
            "enable_llm": "false"
        },
        "expected_outputs": {
            "report_generated": True,
            "report_type": "smart",
            "data_source": "API自动获取 + 本地分析",
            "llm_enabled": False,
            "export_success": True
        },
        "workflow_steps": [
            "1. 验证API连接状态",
            "2. 通过API获取容量参数",
            "3. 使用LLM分析生成智能报告",
            "4. 导出Word格式文档",
            "5. 返回完整结果信息"
        ]
    }
    
    with open("dify_test_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ Dify测试配置已保存到: dify_test_config.json")

if __name__ == "__main__":
    try:
        # 运行完整工作流测试
        success = test_complete_workflow()
        
        if success:
            # 生成Dify测试配置
            generate_dify_test_config()
            
            print("\n" + "=" * 60)
            print("🎯 下一步操作建议:")
            print("1. 在Dify平台导入 '智能容量报告系统.json' 配置文件")
            print("2. 或者使用 '智能容量报告系统.yml' YAML配置文件")
            print("3. 配置参数使用测试验证的数值")
            print("4. 运行Dify工作流验证完整功能")
            print("=" * 60)
        else:
            print("\n❌ 测试失败，请检查API服务状态和配置")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
