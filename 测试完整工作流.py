#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整工作流程
模拟Dify工作流的完整执行过程，包括：
1. 获取容量数据
2. LLM分析（模拟）
3. 导出Word文档
"""

import requests
import json
from datetime import datetime

def test_complete_workflow():
    """测试完整的工作流程"""
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试完整工作流程 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 步骤1: 获取容量数据
    print("步骤1: 获取容量数据...")
    try:
        response = requests.post(f"{base_url}/api/get_capacity_data", 
                               json={"query_type": "all"}, 
                               timeout=10)
        
        if response.status_code == 200:
            capacity_data = response.json()
            print("✅ 容量数据获取成功")
            print(f"数据大小: {len(json.dumps(capacity_data, ensure_ascii=False))} 字符")
        else:
            print(f"❌ 容量数据获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 容量数据获取异常: {str(e)}")
        return False
    
    # 步骤2: 模拟LLM分析
    print("\n步骤2: 模拟LLM分析...")
    
    # 这里模拟LLM分析的结果
    llm_analysis = """# IT基础设施容量分析报告

## 执行摘要

本报告基于2025年7月1日的系统监控数据，对IT基础设施的容量使用情况进行全面分析。

### 关键发现
- 存储容量整体健康，使用率在安全范围内
- 数据库性能良好，连接数正常
- 容器资源使用合理
- 虚拟化平台运行稳定

## 存储容量分析

### 存储池概览
| 存储池名称 | 总容量(TB) | 已用容量(TB) | 使用率(%) | 健康状态 |
|-----------|-----------|-------------|----------|----------|
| 主存储池 | 100.0 | 75.5 | 75.5 | 🟢 正常 |
| 备份存储池 | 50.0 | 30.2 | 60.4 | 🟢 正常 |
| 归档存储池 | 200.0 | 120.8 | 60.4 | 🟢 正常 |

**风险评估**: 所有存储池使用率均低于85%警戒线，容量充足。

## 数据库容量分析

### 数据库实例状态
| 数据库名称 | 数据大小(GB) | 连接数 | CPU使用率(%) | 内存使用率(%) | 健康状态 |
|-----------|-------------|--------|-------------|-------------|----------|
| 生产数据库 | 500.5 | 45/100 | 65.2 | 70.8 | 🟢 正常 |
| 测试数据库 | 120.3 | 15/50 | 35.1 | 45.6 | 🟢 正常 |
| 开发数据库 | 80.7 | 8/30 | 25.4 | 38.2 | 🟢 正常 |

**风险评估**: 数据库连接数和资源使用率均在正常范围内。

## 容器容量分析

### 容器集群状态
| 集群名称 | 节点数 | Pod数量 | CPU使用率(%) | 内存使用率(%) | 健康状态 |
|---------|--------|---------|-------------|-------------|----------|
| 生产集群 | 5 | 120 | 68.5 | 72.3 | 🟢 正常 |
| 测试集群 | 3 | 45 | 45.2 | 55.8 | 🟢 正常 |
| 开发集群 | 2 | 25 | 35.7 | 42.1 | 🟢 正常 |

**风险评估**: 容器资源使用率合理，扩展性良好。

## 虚拟化容量分析

### 虚拟化平台状态
| 主机名称 | 虚拟机数量 | CPU使用率(%) | 内存使用率(%) | 存储使用率(%) | 健康状态 |
|---------|-----------|-------------|-------------|-------------|----------|
| ESX-Host-01 | 15 | 62.8 | 68.4 | 55.2 | 🟢 正常 |
| ESX-Host-02 | 12 | 58.3 | 65.1 | 48.7 | 🟢 正常 |
| ESX-Host-03 | 18 | 71.2 | 74.6 | 62.3 | 🟢 正常 |

**风险评估**: 虚拟化平台运行稳定，资源分配合理。

## 建议和行动计划

### 短期建议（1-3个月）
1. 继续监控存储使用趋势
2. 优化数据库查询性能
3. 评估容器资源分配策略

### 中期建议（3-6个月）
1. 规划存储扩容方案
2. 升级数据库版本
3. 优化虚拟化资源配置

### 长期建议（6-12个月）
1. 实施自动化容量管理
2. 部署智能监控系统
3. 建立容量预测模型

## 风险评估和缓解措施

### 当前风险等级: 🟢 低风险

### 潜在风险点
1. 存储增长趋势需要持续关注
2. 数据库连接数峰值时段需要监控
3. 容器集群在高负载时的表现需要测试

### 缓解措施
1. 建立容量预警机制
2. 制定应急扩容方案
3. 定期进行容量压力测试

---

**报告生成时间**: 2025-07-01 14:30:00
**数据来源**: IT基础设施监控系统
**报告有效期**: 30天
**下次更新**: 2025-08-01
"""
    
    print("✅ LLM分析完成")
    print(f"分析结果长度: {len(llm_analysis)} 字符")
    
    # 步骤3: 导出Word文档
    print("\n步骤3: 导出Word文档...")
    try:
        export_data = {
            "report_content": llm_analysis,
            "report_date": "2025-07-01",
            "system_name": "完整工作流测试报告",
            "save_path": "D:/work/LLM/reports/"
        }
        
        response = requests.post(f"{base_url}/api/export_word", 
                               json=export_data, 
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Word文档导出成功")
            print(f"文件路径: {result.get('file_path')}")
            print(f"文件大小: {result.get('file_size')}")
            return True
        else:
            print(f"❌ Word文档导出失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Word文档导出异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_complete_workflow()
    if success:
        print("\n🎉 完整工作流程测试成功！")
    else:
        print("\n❌ 完整工作流程测试失败！")
