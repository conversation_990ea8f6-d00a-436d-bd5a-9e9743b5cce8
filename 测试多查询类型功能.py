#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多查询类型功能
测试智能容量报告系统的多种查询模式
"""

import requests
import json
from datetime import datetime

# API配置
API_BASE_URL = "http://127.0.0.1:5000"
TEST_DATA = {
    "report_date": "2025-06-30",
    "system_name": "生产环境运维资源容量检查报告"
}

def test_query_type(query_type, description):
    """测试指定查询类型"""
    print(f"\n{'='*60}")
    print(f"🧪 测试 {description}")
    print(f"{'='*60}")
    
    try:
        # 准备请求数据
        request_data = TEST_DATA.copy()
        request_data["query_type"] = query_type
        
        # 发送请求
        print(f"📡 发送请求到: {API_BASE_URL}/api/get_capacity_data")
        print(f"📋 请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            f"{API_BASE_URL}/api/get_capacity_data",
            json=request_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📊 响应状态: {result.get('success')}")
            print(f"📝 响应消息: {result.get('message')}")
            print(f"🔍 查询类型: {result.get('query_type')}")
            print(f"⏰ 时间戳: {result.get('timestamp')}")
            
            # 分析返回的数据结构
            data = result.get('data', {})
            report_info = data.get('report_info', {})
            summary = data.get('summary', {})
            
            print(f"\n📋 报告信息:")
            print(f"  - 报告日期: {report_info.get('report_date')}")
            print(f"  - 系统名称: {report_info.get('system_name')}")
            print(f"  - 查询类型: {report_info.get('query_type')}")
            print(f"  - 数据收集时间: {report_info.get('data_collection_time')}")
            
            print(f"\n📊 数据摘要:")
            for key, value in summary.items():
                print(f"  - {key}: {value}")
            
            # 检查包含的数据类型
            print(f"\n🔍 包含的数据类型:")
            data_types = []
            if 'storage_capacity' in data:
                storage_pools = len(data['storage_capacity'].get('pools', []))
                data_types.append(f"存储容量 ({storage_pools}个存储池)")
            if 'database_capacity' in data:
                db_instances = len(data['database_capacity'].get('instances', []))
                data_types.append(f"数据库容量 ({db_instances}个实例)")
            if 'container_capacity' in data:
                container_clusters = len(data['container_capacity'].get('clusters', []))
                data_types.append(f"容器容量 ({container_clusters}个集群)")
            if 'virtualization_capacity' in data:
                vm_clusters = len(data['virtualization_capacity'].get('clusters', []))
                data_types.append(f"虚拟化容量 ({vm_clusters}个集群)")
            
            for data_type in data_types:
                print(f"  ✅ {data_type}")
            
            print(f"\n🎯 测试结果: 成功 ✅")
            return True
            
        else:
            print(f"❌ 请求失败!")
            print(f"📊 状态码: {response.status_code}")
            print(f"📝 响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_api_connectivity():
    """测试API连接性"""
    print(f"🚀 开始测试API连接性")
    print(f"🔗 API地址: {API_BASE_URL}")
    
    try:
        # 测试基础存储API
        response = requests.get(f"{API_BASE_URL}/api/storage", timeout=10)
        if response.status_code == 200:
            print(f"✅ API连接成功!")
            return True
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print(f"🎯 智能容量报告系统 - 多查询类型功能测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 API地址: {API_BASE_URL}")
    
    # 测试API连接性
    if not test_api_connectivity():
        print(f"\n❌ API连接失败，无法继续测试")
        return
    
    # 定义测试用例
    test_cases = [
        ("all", "查询所有容量（存储+数据库+容器+虚拟化）"),
        ("storage", "只查询存储容量"),
        ("database", "只查询数据库容量"),
        ("container", "只查询容器容量"),
        ("virtualization", "只查询虚拟化容量")
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for query_type, description in test_cases:
        if test_query_type(query_type, description):
            success_count += 1
    
    # 测试无效查询类型
    print(f"\n{'='*60}")
    print(f"🧪 测试无效查询类型")
    print(f"{'='*60}")
    
    try:
        invalid_data = TEST_DATA.copy()
        invalid_data["query_type"] = "invalid_type"
        
        response = requests.post(
            f"{API_BASE_URL}/api/get_capacity_data",
            json=invalid_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ 无效查询类型正确处理!")
            print(f"📝 错误消息: {result.get('message')}")
            success_count += 1
        else:
            print(f"❌ 无效查询类型处理异常，状态码: {response.status_code}")
        
        total_count += 1
        
    except Exception as e:
        print(f"❌ 无效查询类型测试异常: {str(e)}")
        total_count += 1
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print(f"📊 测试总结")
    print(f"{'='*60}")
    print(f"✅ 成功测试: {success_count}/{total_count}")
    print(f"❌ 失败测试: {total_count - success_count}/{total_count}")
    print(f"📈 成功率: {(success_count/total_count)*100:.1f}%")
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过! 多查询类型功能正常工作!")
    else:
        print(f"\n⚠️  部分测试失败，请检查API实现")
    
    print(f"\n🔧 下一步:")
    print(f"1. 确保Flask API服务正在运行")
    print(f"2. 在Dify平台导入更新后的配置文件")
    print(f"3. 配置LLM模型和参数")
    print(f"4. 测试完整的工作流")

if __name__ == "__main__":
    main()
