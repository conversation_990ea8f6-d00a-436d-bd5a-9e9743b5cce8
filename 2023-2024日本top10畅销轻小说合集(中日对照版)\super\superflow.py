import requests
import json
import subprocess
import dify_api
import dify_workflow
import office
import re
import os
import time
import threading
import signal
from typing import Dict, Any


# 编排
def superflow(user, question, conversation_id=""):
    # 编排入口
    user=user
    
    # 获取对话结果、会话ID和分类ID
    make_plan_ans, conversation_id, class_id = dify_workflow.get_chat(
        user=user,
        question=question,
        conversation_id=conversation_id
    )
    
    # 检查分类ID
    if class_id is not None:
        print(f"问题分类ID: {class_id}")
        # 只有当class_id为1时才执行shell命令
        if class_id != "1":
            print(f"用户问题不需要执行shell命令，class_id={class_id}")
            return {
                "report": make_plan_ans,
                "download_link": None
            }, conversation_id
    else:
        # 如果没有分类ID，尝试从响应文本中判断
        print("未找到class_id，尝试从响应中判断")
        if "用户的问题涉及执行shell相关的指令" not in make_plan_ans and "class_id\": \"1" not in make_plan_ans:
            print("响应中未发现shell相关指令标识，不执行shell命令")
            return {
                "report": make_plan_ans,
                "download_link": None
            }, conversation_id
    
    # 执行到这里说明需要执行shell命令
    print("用户问题需要执行shell命令")
    
    # 流程string转list
    list_plan = split_plan(make_plan_ans)
    # 遍历整个list（遍历步骤）

    command_log_all = ''
    for i in list_plan:
        # 获取单条流程转shell
        print("【切割内容】\n"+i)
        shell_command = dify_workflow.get_shell(user=user, question=i)
        # 执行shell返回的结果
        print("【执行command】")
        shell_result = exec_command(shell_command)
        print("【command结束】")
        # 记录文字
        command_log = '执行 '+shell_command+' 生成结果如下\n'+shell_result+'\n\n'
        command_log_all += command_log
    
    # 构建总结内容
    summarize_all = '【用户的问题】\n'+question+'\n'+'【流程】\n'+make_plan_ans+'\n'+'【执行过程】\n'+command_log_all+'\n'
    print("-----------------【summarize_all-----------------】")
    print("\n"+summarize_all)
    summarize_report = dify_workflow.get_summary(user=user, question=summarize_all)
    dock_download_link = office.markdown_to_docx(md_text=summarize_report)
    
    # 返回包含报告和下载链接的字典
    return {
        "report": summarize_report,
        "download_link": dock_download_link
    }, conversation_id


def exec_command(command: str, timeout: int = 61) -> str:
    """
    执行 shell 命令并返回输出，保留换行符，设置超时时间。
    即使是持续运行的命令，也会在超时后返回已收集的输出。
    
    :param command: 需要执行的单一 shell 命令，例如 'ls', 'pwd', 'cat XXX'
    :param timeout: 命令运行的最大时间（秒），超时则终止并返回已收集的输出
    :return: 命令执行的完整输出，或错误信息
    """
    # 存储进程信息和输出
    process_info: Dict[str, Any] = {
        "stdout": "",
        "stderr": "",
        "process": None,
        "timed_out": False
    }
    
    def target():
        """在线程中执行命令并收集输出"""
        try:
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            process_info["process"] = process
            
            # 读取输出直到进程结束或超时
            stdout, stderr = process.communicate()
            process_info["stdout"] = stdout
            process_info["stderr"] = stderr
            
        except Exception as e:
            process_info["stderr"] = str(e)
    
    # 创建并启动线程
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    
    # 等待线程完成或超时
    start_time = time.time()
    while thread.is_alive() and (time.time() - start_time) < timeout:
        thread.join(0.1)  # 短暂等待，允许检查超时
    
    # 检查是否超时
    if thread.is_alive():
        process_info["timed_out"] = True
        
        # 尝试终止进程
        if process_info["process"] and process_info["process"].poll() is None:
            try:
                # 在Linux/Unix上使用SIGTERM
                process_info["process"].terminate()
                # 给进程一点时间来清理
                time.sleep(0.5)
                # 如果进程仍在运行，强制终止
                if process_info["process"].poll() is None:
                    process_info["process"].kill()
            except:
                pass  # 忽略终止过程中的错误
        
        # 尝试获取已有的输出
        if process_info["process"]:
            try:
                # 非阻塞方式获取已有输出
                stdout, stderr = process_info["process"].communicate(timeout=0.5)
                if stdout:
                    process_info["stdout"] = stdout
                if stderr:
                    process_info["stderr"] = stderr
            except:
                pass
        
        return f"Command timed out after {timeout} seconds. Partial output:\n{process_info['stdout']}"
    
    # 命令正常完成
    if process_info["process"] and process_info["process"].returncode != 0:
        return f"Error Code: 2887\nError Message: {process_info['stderr'].strip()}"
    
    return process_info["stdout"]

def split_plan(text):
    """
    将形如 "1. XXX\n2. XXX" 的字符串按编号拆分为列表。
    
    :param text: 包含编号和命令的字符串（编号和换行符作为标识）
    :return: 拆分后的列表，形如 ['1. XXX', '2. XXX']
    """
    # 确保text是字符串
    if not isinstance(text, str):
        print(f"警告: split_plan 收到非字符串输入: {type(text)}")
        text = str(text)
    
    try:
        pattern = re.compile(r'(\d+\.\s*.+?)(?=\n\d+\.|$)', re.DOTALL)
        matches = pattern.findall(text)
        
        result = [match.strip() for match in matches]
        
        # 如果没有匹配到任何内容，返回原始文本作为单个项目
        if not result:
            return [text]
            
        return result
    except Exception as e:
        print(f"split_plan 错误: {str(e)}")
        # 出错时返回原始文本作为单个项目
        return [text]
