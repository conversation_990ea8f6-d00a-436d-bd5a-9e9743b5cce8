app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 编排chatflow
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.7@8b9d2f57d314120744c245b6fe4f8701e1a7490a500d9fb74e9e9dceeaea5f70
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: llm-source-1742455924941-target
      source: llm
      sourceHandle: source
      target: '1742455924941'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: question-classifier
      id: 1742438709824-source-1742457462235-target
      source: '1742438709824'
      sourceHandle: source
      target: '1742457462235'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1742457462235-1-llm-target
      source: '1742457462235'
      sourceHandle: '1'
      target: llm
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1742457462235-2-17424574801000-target
      source: '1742457462235'
      sourceHandle: '2'
      target: '17424574801000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 17424574801000-source-1742457514682-target
      source: '17424574801000'
      sourceHandle: source
      target: '1742457514682'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1742457462235-1742458018813-1742458150692-target
      source: '1742457462235'
      sourceHandle: '1742458018813'
      target: '1742458150692'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1742458150692-source-1742458260654-target
      source: '1742458150692'
      sourceHandle: source
      target: '1742458260654'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: answer
      id: 1742455924941-source-answer-target
      source: '1742455924941'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 54
      id: '1742438709824'
      position:
        x: -75.07427792459976
        y: 303.8252687449437
      positionAbsolute:
        x: -75.07427792459976
        y: 303.8252687449437
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 68aa4f71-608c-48d6-bf71-3c165fbc0e72
          role: system
          text: '# 你首先会获取用户提出的问题{{#sys.query#}}

            # 根据用户提供的问题你需要做出操作步骤编排。

            # 你会用编号标注出需要执行的步骤。你不会解释内容。

            # 对于不确定的你需要增加确认步骤。该场景包括但不限于“打开某个文件但是你不确定该文件路径因此需要先通过搜素获取路径”等

            # 你不能编排如”删除“，“修改”功能相关的步骤


            # 可参考的示例如下：

            <exmaples>

            <example>

            Q: 获取aa.txt 内容

            A: 1.全局查找aa.txt所在的目录位置\n2.在查询到的目录中使用cat指令

            </example>

            </examples>

            '
        selected: false
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: llm
      position:
        x: 778.9999999999998
        y: 246.0000000000001
      positionAbsolute:
        x: 778.9999999999998
        y: 246.0000000000001
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1742455924941.return_exec#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 105
      id: answer
      position:
        x: 1622.3195079107732
        y: 219.37676245547863
      positionAbsolute:
        x: 1622.3195079107732
        y: 219.37676245547863
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: '# 你会提取{{#llm.text#}}中生成的流程编排。

          ## 该内容包含例如：1. XXXXXX\n2. XXXXX\n3. XXXX格式的内容。

          ### 不同编号内容用“\n”分割，每条编号中不应该出现“\n”，如果包含"\n"需要删除掉。

          ### 提取该内容转为为形如“1. XXXXXX\n2. XXXXX\n3. XXXX"的string格式。

          # 你的输出应该是一个sting。

          '
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2-VL-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: '提取段落文字中编号部分，例如：

            1. XXXXX

            2. XXXXX

            3. XXXXX'
          name: return_exec
          required: false
          type: string
        query:
        - llm
        - text
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1742455924941'
      position:
        x: 1116.9999999999998
        y: 236.0000000000001
      positionAbsolute:
        x: 1116.9999999999998
        y: 236.0000000000001
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: '# 用户的问题涉及执行shell相关的指令'
        - id: '2'
          name: '# 用户的问题只是与大模型交流'
        - id: '1742458018813'
          name: '# 不确定用户的行为是否需要你帮忙执行一些shell操作'
        desc: ''
        instruction: '# 你会接收{{#sys.query#}}中用户提出的问题。

          # 你会根据当前场景和问题的内容分析此时用户的行为适用于哪种分类情况，并判断：

          ## 分类1： 用户的问题涉及到shell指令执行与分析，用户的行为上更倾向于让你去解决问题。

          ## 分类2： 用户的问题主要是在询问你的解答，用户的行为不希望你去执行一些操作。

          # 如果你不好分析用户此时的行为进行问题分类，你可以选择分类3向用户提问明确用户的意图。'
        instructions: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        query_variable_selector:
        - '1742438709824'
        - sys.query
        selected: false
        title: 问题分类器
        topics: []
        type: question-classifier
        vision:
          enabled: false
      height: 242
      id: '1742457462235'
      position:
        x: 249.89968917534088
        y: 303.8252687449437
      positionAbsolute:
        x: 249.89968917534088
        y: 303.8252687449437
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 68aa4f71-608c-48d6-bf71-3c165fbc0e72
          role: system
          text: '# 你首先会获取用户提出的问题{{#sys.query#}}

            # 根据用户提供的问题你需要做出相应的解答。'
        selected: false
        title: LLM (1)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '17424574801000'
      position:
        x: 893.946143474398
        y: 544.4611845199705
      positionAbsolute:
        x: 893.946143474398
        y: 544.4611845199705
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#17424574801000.text#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 105
      id: '1742457514682'
      position:
        x: 1197.946143474398
        y: 544.4611845199705
      positionAbsolute:
        x: 1197.946143474398
        y: 544.4611845199705
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 953c629d-e847-43e3-951e-a58e4383274c
          role: system
          text: "# 你会接收{{#sys.query#}}的信息\n# 你不确定用户此时的行为是需要你帮忙执行一些shell操作还是想你咨询一些解决办法\n\
            # 梳理你对用户问题的理解并向用户确认你理解的内容是否正确\n## 例如： \n### 用户提问：看看/opt目录 你应该回答：请问是需要我帮助你执行`ls\
            \ /opt`指令来查看/opt目录下的内容吗？\n### 用户提问：什么ip 你应该回答：是否需要我帮助你解释什么是ip吗？\n\n# 不要编造回答"
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1742458150692'
      position:
        x: 893.946143474398
        y: 678.0559779399587
      positionAbsolute:
        x: 893.946143474398
        y: 678.0559779399587
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1742458150692.text#}}'
        desc: ''
        selected: false
        title: 直接回复 3
        type: answer
        variables: []
      height: 105
      id: '1742458260654'
      position:
        x: 1197.946143474398
        y: 673.4611845199705
      positionAbsolute:
        x: 1197.946143474398
        y: 673.4611845199705
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 77.2836463118507
      y: 85.32178378351989
      zoom: 0.659753955386449
