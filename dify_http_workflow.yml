app:
  description: 基于HTTP请求的容量报告生成器 - 避免代码节点语法问题，使用HTTP API调用方式生成专业的IT基础设施容量分析报告并导出为Word文档
  icon: 📊
  icon_background: '#FFEAD5'
  mode: workflow
  name: 容量报告生成器(HTTP版)
kind: app
version: 0.1.2
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: http-request
      id: start-http-generate-report
      source: start
      sourceHandle: source
      target: http-generate-report
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: http-request
      id: http-generate-report-http-export-word
      source: http-generate-report
      sourceHandle: source
      target: http-export-word
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: answer
      id: http-export-word-answer
      source: http-export-word
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 收集报告生成所需的基本参数
        selected: false
        title: 开始
        type: start
        variables:
        - description: 报告日期，格式：YYYY-MM-DD
          label: 报告日期
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: report_date
        - description: 报告标题，例如：生产环境运维资源容量检查报告
          label: 系统名称
          max_length: 100
          options: []
          required: true
          type: text-input
          variable: system_name
        - description: Flask API服务地址，例如：http://localhost:5000
          label: API服务地址
          max_length: 200
          options: []
          required: true
          type: text-input
          variable: api_base_url
        - description: Word文件保存路径，可选，默认为 D:/work/LLM/reports/
          label: Word文件保存路径
          max_length: 200
          options: []
          required: false
          type: text-input
          variable: save_path
      height: 116
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data: |-
            {
              "report_date": "{{#start.report_date#}}",
              "system_name": "{{#start.system_name#}}",
              "api_base_url": "{{#start.api_base_url#}}"
            }
          type: json
        desc: 调用Flask API生成容量报告
        headers: |-
          Content-Type: application/json
          Accept: application/json
        method: post
        selected: false
        timeout:
          connect: 30
          read: 60
          write: 60
        title: 生成容量报告
        type: http-request
        url: '{{#start.api_base_url#}}/api/generate_report'
        variables:
        - value_selector:
          - http-generate-report
          - body
          - success
          variable: success
        - value_selector:
          - http-generate-report
          - body
          - report_content
          variable: report_content
        - value_selector:
          - http-generate-report
          - body
          - error
          variable: error_message
      height: 54
      id: http-generate-report
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: {}
          type: no-auth
        body:
          data: |-
            {
              "report_content": "{{#http-generate-report.report_content#}}",
              "report_date": "{{#start.report_date#}}",
              "system_name": "{{#start.system_name#}}",
              "save_path": "{{#start.save_path#}}"
            }
          type: json
        desc: 调用Flask API导出Word文档
        headers: |-
          Content-Type: application/json
          Accept: application/json
        method: post
        selected: false
        timeout:
          connect: 30
          read: 60
          write: 60
        title: 导出Word文档
        type: http-request
        url: '{{#start.api_base_url#}}/api/export_word'
        variables:
        - value_selector:
          - http-export-word
          - body
          - success
          variable: export_success
        - value_selector:
          - http-export-word
          - body
          - file_path
          variable: file_path
        - value_selector:
          - http-export-word
          - body
          - file_name
          variable: file_name
        - value_selector:
          - http-export-word
          - body
          - message
          variable: export_message
        - value_selector:
          - http-export-word
          - body
          - note
          variable: export_note
      height: 54
      id: http-export-word
      position:
        x: 688
        y: 282
      positionAbsolute:
        x: 688
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |-
          # 容量报告生成完成

          ## 📊 报告内容

          {{#http-generate-report.report_content#}}

          ---

          ## 📁 Word文档导出结果

          **导出状态：** {{#http-export-word.export_success#}}

          **文件信息：**
          - 文件名：{{#http-export-word.file_name#}}
          - 文件路径：{{#http-export-word.file_path#}}
          - 导出说明：{{#http-export-word.export_note#}}

          **使用提示：**
          1. 生成的是真正的Word文档(.docx格式)
          2. 可以直接用Microsoft Word打开
          3. 包含完整的表格和专业格式
        desc: 显示生成的容量报告和Word导出结果
        selected: false
        title: 报告输出
        type: answer
        variables: []
      height: 107
      id: answer
      position:
        x: 992
        y: 282
      positionAbsolute:
        x: 992
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
  meta:
    created_by: user
    created_at: 1640995200
    updated_by: user
    updated_at: 1640995200
