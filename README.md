# 容量报告自动化生成系统

## 🎯 项目简介

这是一个完整的容量报告自动化生成系统，包含Flask API服务和Dify工作流两个部分。系统能够自动获取存储、数据库、容器(TAP)、虚拟化四个维度的容量数据，并生成标准格式的容量报告。

## 🏗️ 系统架构

```
┌─────────────────┐    HTTP请求    ┌─────────────────┐    API调用    ┌─────────────────┐
│                 │ ──────────────→ │                 │ ──────────────→ │                 │
│   Dify工作流    │                │   Flask API     │                │   容量数据源    │
│                 │ ←────────────── │                 │ ←────────────── │                 │
└─────────────────┘    JSON数据    └─────────────────┘    数据响应    └─────────────────┘
         │                                                                        
         ▼                                                                        
┌─────────────────┐                                                              
│                 │                                                              
│   容量报告      │                                                              
│   (Markdown)    │                                                              
└─────────────────┘                                                              
```

## 📁 项目文件

### Flask API服务
- `app.py` - Flask应用主文件，提供四个容量API接口
- `requirements.txt` - Python依赖包列表
- `start.bat` - Windows启动脚本
- `test_api.py` - API接口测试脚本

### Dify工作流
- `dify_compatible_workflow.json` - 兼容版工作流配置（推荐）
- `dify_simple_api_workflow.yml` - 代码节点版工作流
- `dify_capacity_report_app.yml` - 原始HTTP请求版工作流
- `Dify测试参数.txt` - 测试参数示例
- `Dify导入问题解决指南.md` - 导入问题解决方案

### 文档
- `Flask_API_项目说明.md` - 详细的项目说明文档
- `新格式说明.md` - 报告格式说明
- `快速部署指南.md` - 部署指南
- `README.md` - 本文件

## 🚀 快速开始

### 1. 启动Flask API服务

```bash
# 方法1：使用启动脚本（Windows）
start.bat

# 方法2：手动启动
pip install -r requirements.txt
python app.py
```

服务启动后访问：http://localhost:5000

### 2. 测试API接口

```bash
# 使用测试脚本
python test_api.py

# 或手动测试
curl http://localhost:5000/api/health
curl http://localhost:5000/api/storage
curl http://localhost:5000/api/database
curl http://localhost:5000/api/container
curl http://localhost:5000/api/virtualization
```

### 3. 导入Dify工作流

**推荐使用兼容版配置文件：**

1. 访问Dify平台：http://************:980
2. 创建新应用 → 导入DSL
3. 上传 `dify_compatible_workflow.json`（推荐）
4. 保存配置

**如果导入失败，请参考：** `Dify导入问题解决指南.md`

### 4. 运行容量报告生成

在Dify应用中输入：
- **报告日期**：2024-01-15
- **系统名称**：生产环境运维资源容量检查报告
- **API服务地址**：http://localhost:5000

点击运行，即可生成完整的容量报告。

## 📊 API接口说明

### 存储容量接口
- **URL**: `GET /api/storage`
- **返回**: 5个存储池的容量信息
- **包含**: 嘉兴、后沙峪的虚拟化、数据库、高端存储池

### 数据库容量接口
- **URL**: `GET /api/database`
- **返回**: 7个数据库实例的容量信息
- **包含**: Oracle、MySQL、PostgreSQL数据库

### 容器容量接口
- **URL**: `GET /api/container`
- **返回**: 4个容器集群的资源信息
- **包含**: K8S生产/测试集群、TAP容器集群

### 虚拟化容量接口
- **URL**: `GET /api/virtualization`
- **返回**: 4个虚拟化集群的资源信息
- **包含**: vSphere、Hyper-V集群

## 📋 生成的报告格式

报告包含四个标准部分：

### 1. 存储资源容量及健康度排查
- 存储池容量表格
- 健康度说明（绿色<90%，黄色90-95%，红色>95%）
- 今日状态分析
- 问题详情和应对措施

### 2. 数据库资源容量及健康度排查
- 数据库实例容量表格
- 健康度说明（绿色<85%，黄色85-95%，红色>95%）
- 状态分析和措施建议

### 3. 容器资源容量及健康度排查
- 容器集群资源表格（CPU/内存/存储）
- 健康度说明（CPU/内存<80%，存储<90%为绿色）
- 集群状态分析

### 4. 虚拟化资源容量及健康度排查
- 虚拟化集群资源表格
- 健康度说明（CPU/内存<75%，存储<90%为绿色）
- 集群状态分析

## 🔧 自定义配置

### 修改容量数据
编辑 `app.py` 中的数据结构：
```python
storage_pools = [
    {
        "pool_name": "自定义存储池名称",
        "total_capacity_gb": 1000000,
        "used_capacity_gb": 750000,
        # ... 其他字段
    }
]
```

### 调整健康度阈值
在 `app.py` 中修改阈值设置：
```python
# 存储健康度阈值
storage_health = get_health_status(usage_rate, {'green': 90, 'yellow': 95})

# 数据库健康度阈值
db_health = get_health_status(usage_rate, {'green': 85, 'yellow': 95})
```

### 修改报告格式
编辑 `dify_capacity_report_app.yml` 中的LLM提示词，调整：
- 表格结构和列数
- 健康度标准描述
- 状态分析模板
- 输出格式要求

## 🔍 故障排除

### Flask服务问题
- 检查端口5000是否被占用
- 确认Python环境和依赖包安装
- 查看控制台错误信息

### API调用问题
- 确认Flask服务正在运行
- 检查防火墙和网络设置
- 验证API地址配置正确

### Dify工作流问题
- 检查HTTP请求节点配置
- 确认API服务地址正确
- 查看Dify执行日志
- 验证LLM模型配置

## 🚀 扩展功能

### 集成真实数据源
- 连接存储管理系统API
- 集成数据库监控工具
- 对接容器平台API
- 连接虚拟化管理平台

### 增强功能
- 添加历史数据对比
- 实现趋势分析
- 支持多环境切换
- 添加告警功能
- 生成图表和可视化

### 部署优化
- 使用Docker容器化部署
- 添加负载均衡
- 实现高可用配置
- 添加监控和日志

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看相关文档：`Flask_API_项目说明.md`
2. 检查API测试：运行 `python test_api.py`
3. 验证服务状态：访问 `http://localhost:5000/api/health`
4. 查看Dify执行日志确认错误信息

---

**版本**: 1.0.0  
**更新时间**: 2024-01-15  
**兼容性**: Python 3.7+, Dify 0.1.5+
