app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: 编排
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.7@8b9d2f57d314120744c245b6fe4f8701e1a7490a500d9fb74e9e9dceeaea5f70
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1742180748448-source-1742180767253-target
      selected: false
      source: '1742180748448'
      sourceHandle: source
      target: '1742180767253'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: 1742180767253-source-1742180931895-target
      selected: false
      source: '1742180767253'
      sourceHandle: source
      target: '1742180931895'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: end
      id: 1742180931895-source-1742180775020-target
      selected: false
      source: '1742180931895'
      sourceHandle: source
      target: '1742180775020'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: question
          max_length: 10000
          options: []
          required: true
          type: paragraph
          variable: question
      height: 90
      id: '1742180748448'
      position:
        x: 73
        y: 291
      positionAbsolute:
        x: 73
        y: 291
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2-VL-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: b2170808-35b5-4d22-a306-7c656cab43d5
          role: system
          text: '# 你首先会获取用户提出的问题{{#1742180748448.question#}}

            # 根据用户提供的问题你需要做出操作步骤编排。

            # 你会用编号标注出需要执行的步骤。你不会解释内容。

            # 对于不确定的你需要增加确认步骤。该场景包括但不限于“打开某个文件但是你不确定该文件路径因此需要先通过搜素获取路径”等

            # 你不能编排如”删除“功能相关的步骤


            # 可参考的示例如下：

            <exmaples>

            <example>

            Q: 获取aa.txt 内容

            A: 1.全局查找aa.txt所在的目录位置\n2.在查询到的目录中使用cat指令

            </example>

            </examples>

            '
        selected: true
        title: LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1742180767253'
      position:
        x: 384
        y: 285
      positionAbsolute:
        x: 384
        y: 285
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1742180931895'
          - return_exec
          variable: exec_return
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1742180775020'
      position:
        x: 968.3103419071247
        y: 291
      positionAbsolute:
        x: 968.3103419071247
        y: 291
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: '# 你会提取{{#1742180767253.text#}}中生成的流程编排。

          ## 该内容包含例如：1. XXXXXX\n2. XXXXX\n3. XXXX格式的内容。

          ### 不同编号内容用“\n”分割，每条编号中不应该出现“\n”，如果包含"\n"需要删除掉。

          ### 提取该内容转为为形如“1. XXXXXX\n2. XXXXX\n3. XXXX"的string格式。

          # 你的输出应该是一个sting。

          '
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen2-VL-72B-Instruct
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: '提取段落文字中编号部分，例如：

            1. XXXXX

            2. XXXXX

            3. XXXXX'
          name: return_exec
          required: false
          type: string
        query:
        - '1742180767253'
        - text
        reasoning_mode: prompt
        selected: false
        title: 参数提取器
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1742180931895'
      position:
        x: 680.929400345181
        y: 406.7658464052639
      positionAbsolute:
        x: 680.929400345181
        y: 406.7658464052639
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -1.6339025479594511
      y: 21.863531889209753
      zoom: 0.8705505632961251
