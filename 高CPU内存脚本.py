import sys
import time
import threading
import random
import os

# 全局变量用于保持内存占用
memory_hog = []

def memory_eater():
    """内存占用线程：不断生成大对象"""
    try:
        while True:
            # 生成一个包含100万个整数的列表
            data = [random.randint(0, 1000) for _ in range(1_000_000)]
            memory_hog.append(data)  # 防止被垃圾回收
            time.sleep(0.5)  # 控制内存增长速度
            
            # 可选：限制内存大小（避免系统崩溃）
            # if sys.getsizeof(memory_hog) > 1024 * 1024 * 1024:  # 1GB限制
            #     memory_hog.pop(0)

    except KeyboardInterrupt:
        print("内存占用线程已停止")

def cpu_stress():
    """CPU占用线程：执行计算密集型任务"""
    try:
        while True:
            # 示例：计算平方根（可替换为更复杂的计算）
            for _ in range(1000000):
                a = 2 ** 0.5  # 计算平方根
            time.sleep(0.001)  # 避免100% CPU占用（可调整）

    except KeyboardInterrupt:
        print("CPU占用线程已停止")

def file_writer(target_dir):
    """文件写入线程：持续生成随机数据到文件"""
    try:
        while True:
            file_path = os.path.join(target_dir, f"stress_{int(time.time())}.dat")
            with open(file_path, 'wb') as f:
                # 写入1MB随机数据
                f.write(os.urandom(1024 * 1024))
                f.flush()
                os.fsync(f.fileno())  # 确保写入磁盘
            
            time.sleep(0.5)  # 控制写入频率
            
    except KeyboardInterrupt:
        print("文件写入线程已停止")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法：python stress_test.py <目标目录>")
        sys.exit(1)

    target_dir = sys.argv[1]

    # 启动线程
    threads = [
        threading.Thread(target=memory_eater, daemon=True),
        threading.Thread(target=cpu_stress, daemon=True),
        threading.Thread(target=file_writer, args=(target_dir,), daemon=True),
    ]

    for t in threads:
        t.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n所有线程已终止")