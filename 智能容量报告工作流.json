{"version": "0.1.0", "kind": "app", "data": {"title": "智能容量报告生成器", "description": "通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告", "icon": "📊", "icon_background": "#3B82F6", "mode": "workflow", "workflow": {"graph": {"nodes": [{"id": "start", "data": {"type": "start", "title": "开始", "variables": [{"variable": "api_base_url", "type": "text-input", "label": "API服务地址", "required": true, "default": "http://************:5000"}, {"variable": "report_date", "type": "text-input", "label": "报告日期", "required": true, "default": "2024-01-15"}, {"variable": "system_name", "type": "text-input", "label": "系统名称", "required": true, "default": "生产环境运维资源容量检查报告"}]}, "position": {"x": 100, "y": 200}}, {"id": "generate_report", "data": {"type": "http-request", "title": "生成智能报告", "method": "post", "url": "{{#start.api_base_url#}}/api/generate_smart_report", "headers": "Content-Type: application/json", "body": {"type": "json", "data": "{\n  \"report_date\": \"{{#start.report_date#}}\",\n  \"system_name\": \"{{#start.system_name#}}\"\n}"}, "timeout": {"read": 60}}, "position": {"x": 400, "y": 200}}, {"id": "export_word", "data": {"type": "http-request", "title": "导出Word文档", "method": "post", "url": "{{#start.api_base_url#}}/api/export_word", "headers": "Content-Type: application/json", "body": {"type": "json", "data": "{\n  \"report_content\": \"{{#generate_report.body.report_content#}}\",\n  \"report_date\": \"{{#generate_report.body.report_date#}}\",\n  \"system_name\": \"{{#generate_report.body.system_name#}}\",\n  \"save_path\": \"./reports/\"\n}"}, "timeout": {"read": 30}}, "position": {"x": 700, "y": 200}}, {"id": "end", "data": {"type": "end", "title": "完成", "outputs": [{"variable": "success", "type": "boolean", "value_selector": ["generate_report", "body", "success"]}, {"variable": "file_path", "type": "string", "value_selector": ["export_word", "body", "file_path"]}, {"variable": "file_size", "type": "string", "value_selector": ["export_word", "body", "file_size"]}]}, "position": {"x": 1000, "y": 200}}], "edges": [{"source": "start", "target": "generate_report"}, {"source": "generate_report", "target": "export_word"}, {"source": "export_word", "target": "end"}]}}, "model_config": {"opening_statement": "欢迎使用智能容量报告生成器！\n\n本工具将：\n🔍 自动获取系统容量数据\n🧠 使用LLM进行智能分析\n📊 生成专业的容量报告\n📄 导出为Word文档格式\n\n请填写参数开始生成报告：", "suggested_questions": ["生成今日的生产环境容量报告", "使用LLM分析生成智能容量报告", "生成包含风险评估的容量报告", "导出专业格式的Word容量报告"]}}}