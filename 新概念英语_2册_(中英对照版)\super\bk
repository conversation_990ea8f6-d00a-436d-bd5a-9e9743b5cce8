import requests
import json
import subprocess
import dify_api
import dify_workflow
import office
import re
import os
import time


# 编排
def superflow(user,question):
    # 编排入口
    user=user
    make_plan_ans=dify_workflow.make_plan(user=user,question=question)
    # 流程string转list
    list_plan=split_plan(make_plan_ans)
    # 遍历整个list（遍历步骤）

    command_log_all=''
    # [
    #     {"ls": "file1.txt\nfile2.txt\n"},
    #     {"pwd": "/home/<USER>"},
    #     {"cat test.txt": "Hello, World!\n"}
    # ]
    for i in list_plan:
        # 获取单条流程转shell
        print("【切割内容】\n"+i)
        shell_command=dify_workflow.get_shell(user=user,question=i)
        # 执行shell返回的结果
        print("【执行command】")
        shell_result=exec_command(shell_command)
        print("【command结束】")
        # 记录文字
        command_log='执行 '+shell_command+' 生成结果如下\n'+shell_result+'\n\n'
        command_log_all+=command_log
    
    
    # 用户的问题 question
    # 流程 make_plan_ans
    # 执行指令的log command_log_all

    
    summarize_all='【用户的问题】\n'+question+'\n'+'【流程】\n'+make_plan_ans+'\n'+'【执行过程】\n'+command_log_all+'\n'
    print("-----------------【summarize_all-----------------】")
    print("\n"+summarize_all)
    summarize_report=dify_workflow.get_summary(user=user,question=summarize_all)
    dock_download_link=office.markdown_to_docx(md_text=summarize_report)
    # 返回包含报告和下载链接的字典
    return {
        "report": summarize_report,
        "download_link": dock_download_link
    }
    #return summarize_all
    


def exec_command(command: str, timeout: int = 5) -> str:
    """
    执行 shell 命令并返回输出，保留换行符，设置超时时间。
    
    :param command: 需要执行的单一 shell 命令，例如 'ls', 'pwd', 'cat XXX'
    :param timeout: 命令运行的最大时间（秒），超时则终止
    :return: 命令执行的完整输出，或错误代码 2887 和错误信息
    """
    try:
        result = subprocess.run(command, shell=True, text=True, capture_output=True, check=True, timeout=timeout)
        return result.stdout  # 返回标准输出，保留换行符
    except subprocess.TimeoutExpired:
        return "Error Code: 2887\nError Message: Command timed out after {} seconds.".format(timeout)
    except subprocess.CalledProcessError as e:
        return f"Error Code: 2887\nError Message: {e.stderr.strip()}"  # 发生错误时返回错误代码和错误信息

def split_plan(text):
    """
    将形如 "1. XXX\n2. XXX" 的字符串按编号拆分为列表。
    
    :param text: 包含编号和命令的字符串（编号和换行符作为标识）
    :return: 拆分后的列表，形如 ['1. XXX', '2. XXX']
    """
    pattern = re.compile(r'(\d+\.\s*.+?)(?=\n\d+\.|$)', re.DOTALL)
    matches = pattern.findall(text)
    
    return [match.strip() for match in matches]
