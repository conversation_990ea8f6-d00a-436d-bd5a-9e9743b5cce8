Dify工作流测试参数
===================

## 推荐使用的配置文件

由于原始的HTTP请求节点可能存在兼容性问题，建议使用以下配置文件：

1. **dify_compatible_workflow.json** - 兼容版（推荐）
2. **dify_simple_api_workflow.yml** - 代码节点版
3. **dify_capacity_report_app.yml** - 原始版（可能有兼容性问题）

## 测试参数

在Dify应用中输入以下参数进行测试：

**报告日期：**
2024-01-15

**系统名称：**
生产环境运维资源容量检查报告

**API服务地址：**
http://localhost:5000

**备用数据（如API不可用时使用）：**

存储容量信息：
嘉兴中端虚拟化存储池 JX-M-VM-Prod：总容量 769034GB，使用率 89.11%，对比前日变化 -0.72%，无异常波动
后沙峪中端虚拟化存储池 HSY-M-VM-Prod：总容量 5200518GB，使用率 67.24%，对比前日变化 +2.10%，无异常波动

数据库容量信息：
嘉兴Oracle生产库 JX-ORA-PROD01：总容量 8192000GB，使用率 77.5%，对比前日变化 +0.8%，无异常波动
后沙峪MySQL集群主库 HSY-MYSQL-MASTER：总容量 4096000GB，使用率 70.0%，对比前日变化 +0.5%，无异常波动

容器资源信息：
嘉兴K8S生产集群 JX-K8S-PROD：CPU使用率 65%，内存使用率 70%，存储使用率 70%，对比前日变化 CPU+1.2% 内存+0.8% 存储+2.1%，无异常波动

虚拟化资源信息：
嘉兴vSphere生产集群 JX-VSPHERE-PROD：CPU使用率 60%，内存使用率 75%，存储使用率 70%，对比前日变化 CPU+0.8% 内存+1.2% 存储+1.5%，无异常波动

## 测试步骤

### 方案1：使用API（推荐）
1. 启动Flask服务：`python app.py`
2. 测试API：`python test_api.py`
3. 在Dify中导入 `dify_compatible_workflow.json`
4. 输入上述参数（主要是API地址）
5. 运行工作流

### 方案2：手动输入数据
1. 在Dify中导入 `dify_compatible_workflow.json`
2. 输入基本信息和备用数据
3. API地址可以留空或填写无效地址
4. 运行工作流（将使用手动输入的数据）

## 故障排除

### 如果导入失败
1. 尝试使用 `dify_compatible_workflow.json`（JSON格式更兼容）
2. 检查Dify版本是否支持相关节点类型
3. 尝试手动创建工作流而不是导入

### 如果API调用失败
1. 确认Flask服务正在运行
2. 检查API地址是否正确
3. 使用手动输入的备用数据

### 如果报告生成不完整
1. 检查LLM模型配置
2. 确认输入数据格式正确
3. 查看Dify执行日志

## 预期结果

- 生成包含四个部分的标准格式容量报告
- 每部分包含表格、健康度说明、状态分析等内容
- 如果使用API，数据会更加详细和准确
- 如果使用手动输入，也能生成基本的报告格式
