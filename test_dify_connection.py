#!/usr/bin/env python3
"""
测试Dify工作流连接性
"""

import requests
import json
from datetime import datetime

def test_api_connectivity():
    """测试API连接性"""
    
    # 可能的API地址列表
    api_urls = [
        "http://localhost:5000",
        "http://127.0.0.1:5000", 
        "http://************:5000",
        "http://************:5000"
    ]
    
    print("🔍 测试API连接性...")
    print("=" * 50)
    
    working_url = None
    
    for url in api_urls:
        try:
            print(f"测试 {url}...")
            response = requests.get(f"{url}/api/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} - 连接成功")
                working_url = url
                break
            else:
                print(f"❌ {url} - HTTP {response.status_code}")
        except requests.exceptions.ConnectTimeout:
            print(f"❌ {url} - 连接超时")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - 连接失败")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
    
    if working_url:
        print(f"\n🎉 找到可用的API地址: {working_url}")
        return working_url
    else:
        print("\n❌ 没有找到可用的API地址")
        return None

def test_smart_report_api(base_url):
    """测试智能报告生成API"""
    
    print(f"\n📊 测试智能报告生成API...")
    print("-" * 40)
    
    try:
        # 测试数据
        test_data = {
            "report_date": "2024-01-15",
            "system_name": "Dify工作流测试报告"
        }
        
        print("发送请求到智能报告生成接口...")
        response = requests.post(
            f"{base_url}/api/generate_smart_report",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 智能报告生成成功")
                print(f"   报告类型: {result.get('report_type', 'N/A')}")
                print(f"   数据来源: {result.get('data_source', 'N/A')}")
                print(f"   LLM启用: {result.get('llm_enabled', False)}")
                print(f"   报告长度: {len(result.get('report_content', ''))} 字符")
                
                return result
            else:
                print(f"❌ 智能报告生成失败: {result.get('error', 'Unknown error')}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_word_export_api(base_url, report_data):
    """测试Word导出API"""
    
    print(f"\n📄 测试Word导出API...")
    print("-" * 40)
    
    try:
        # 导出数据
        export_data = {
            "report_content": report_data.get('report_content', ''),
            "report_date": report_data.get('report_date', ''),
            "system_name": report_data.get('system_name', ''),
            "save_path": "./reports/"
        }
        
        print("发送请求到Word导出接口...")
        response = requests.post(
            f"{base_url}/api/export_word",
            json=export_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Word文档导出成功")
                print(f"   文件路径: {result.get('file_path', 'N/A')}")
                print(f"   文件大小: {result.get('file_size', 'N/A')}")
                print(f"   文件类型: {result.get('file_type', 'N/A')}")
                return result
            else:
                print(f"❌ Word导出失败: {result.get('error', 'Unknown error')}")
                return None
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def generate_dify_config(api_url):
    """生成适合的Dify配置"""
    
    print(f"\n⚙️ 生成Dify工作流配置...")
    print("-" * 40)
    
    # 简化的Dify工作流配置
    dify_config = {
        "api_base_url": api_url,
        "workflow_steps": [
            {
                "step": 1,
                "name": "开始节点",
                "type": "start",
                "variables": [
                    {
                        "name": "api_base_url",
                        "type": "text-input",
                        "label": "API服务地址",
                        "default": api_url,
                        "required": True
                    },
                    {
                        "name": "report_date", 
                        "type": "text-input",
                        "label": "报告日期",
                        "default": "2024-01-15",
                        "required": True
                    },
                    {
                        "name": "system_name",
                        "type": "text-input", 
                        "label": "系统名称",
                        "default": "生产环境运维资源容量检查报告",
                        "required": True
                    }
                ]
            },
            {
                "step": 2,
                "name": "生成智能报告",
                "type": "http-request",
                "method": "POST",
                "url": f"{api_url}/api/generate_smart_report",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "report_date": "{{#start.report_date#}}",
                    "system_name": "{{#start.system_name#}}"
                },
                "timeout": 60
            },
            {
                "step": 3,
                "name": "导出Word文档",
                "type": "http-request",
                "method": "POST", 
                "url": f"{api_url}/api/export_word",
                "headers": {
                    "Content-Type": "application/json"
                },
                "body": {
                    "report_content": "{{#生成智能报告.body.report_content#}}",
                    "report_date": "{{#生成智能报告.body.report_date#}}",
                    "system_name": "{{#生成智能报告.body.system_name#}}",
                    "save_path": "./reports/"
                },
                "timeout": 30
            },
            {
                "step": 4,
                "name": "结束节点",
                "type": "end",
                "outputs": [
                    "success",
                    "file_path", 
                    "file_size",
                    "report_type",
                    "data_source"
                ]
            }
        ]
    }
    
    # 保存配置到文件
    with open('dify_config_generated.json', 'w', encoding='utf-8') as f:
        json.dump(dify_config, f, ensure_ascii=False, indent=2)
    
    print("✅ Dify配置已生成: dify_config_generated.json")
    
    return dify_config

def main():
    """主函数"""
    
    print("🚀 Dify工作流连接性测试")
    print("=" * 60)
    
    # 1. 测试API连接性
    working_url = test_api_connectivity()
    
    if not working_url:
        print("\n❌ 无法连接到API服务，请检查：")
        print("1. Flask服务是否正在运行")
        print("2. 防火墙设置是否正确")
        print("3. 网络连接是否正常")
        return
    
    # 2. 测试智能报告生成
    report_data = test_smart_report_api(working_url)
    
    if not report_data:
        print("\n❌ 智能报告生成失败，请检查API实现")
        return
    
    # 3. 测试Word导出
    word_result = test_word_export_api(working_url, report_data)
    
    if not word_result:
        print("\n❌ Word导出失败，请检查导出功能")
        return
    
    # 4. 生成Dify配置
    dify_config = generate_dify_config(working_url)
    
    # 5. 输出总结
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"✅ API服务地址: {working_url}")
    print("✅ 智能报告生成: 正常")
    print("✅ Word文档导出: 正常")
    print("✅ Dify配置文件: 已生成")
    
    print("\n🎯 Dify工作流配置建议:")
    print(f"1. 在Dify中使用API地址: {working_url}")
    print("2. 手动创建工作流节点（推荐）")
    print("3. 或导入生成的配置文件")
    print("4. 参考 '智能容量报告工作流配置说明.md' 文档")
    
    print("\n💡 下一步操作:")
    print("1. 在Dify平台创建新的工作流应用")
    print("2. 按照配置说明手动添加节点")
    print(f"3. 使用API地址: {working_url}")
    print("4. 测试工作流执行")

if __name__ == "__main__":
    main()
