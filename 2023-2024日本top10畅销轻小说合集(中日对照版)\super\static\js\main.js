// 配置marked选项
marked.setOptions({
    highlight: function(code, lang) {
        if (window.hljs && lang && hljs.getLanguage(lang)) {
            return hljs.highlight(code, { language: lang }).value;
        }
        return code;
    },
    breaks: true
});

// 对话管理
let conversations = [];
let currentConversationId = "";
let currentConversationIndex = -1;

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 加载本地存储的对话
    loadConversations();
    
    // 如果没有对话，创建一个新对话
    if (conversations.length === 0) {
        createNewConversation();
    } else {
        // 显示最近的对话
        switchToConversation(conversations.length - 1);
    }
    
    // 绑定新建对话按钮事件
    document.getElementById('new-chat-btn').addEventListener('click', createNewConversation);
});

// 加载保存的对话
function loadConversations() {
    const savedConversations = localStorage.getItem('conversations');
    if (savedConversations) {
        conversations = JSON.parse(savedConversations);
        renderConversationsList();
    }
}

// 保存对话到本地存储
function saveConversations() {
    localStorage.setItem('conversations', JSON.stringify(conversations));
}

// 创建新对话
function createNewConversation() {
    const timestamp = new Date().toLocaleString();
    const newConversation = {
        id: "", // 空ID表示新对话
        title: `新对话 (${timestamp})`,
        messages: [],
        createdAt: timestamp
    };
    
    conversations.push(newConversation);
    currentConversationIndex = conversations.length - 1;
    currentConversationId = "";
    
    renderConversationsList();
    clearMessages();
    saveConversations();
}

// 渲染对话列表
function renderConversationsList() {
    const listContainer = document.getElementById('conversations-list');
    listContainer.innerHTML = '';
    
    conversations.forEach((conversation, index) => {
        const item = document.createElement('div');
        item.className = `conversation-item ${index === currentConversationIndex ? 'active' : ''}`;
        
        item.innerHTML = `
            <div class="conversation-title">${conversation.title}</div>
            <div class="conversation-date">${conversation.createdAt}</div>
            <button class="delete-conversation" data-index="${index}">×</button>
        `;
        
        item.addEventListener('click', (e) => {
            // 如果点击的不是删除按钮，切换到该对话
            if (!e.target.classList.contains('delete-conversation')) {
                switchToConversation(index);
            }
        });
        
        listContainer.appendChild(item);
    });
    
    // 绑定删除按钮事件
    document.querySelectorAll('.delete-conversation').forEach(button => {
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const index = parseInt(e.target.getAttribute('data-index'));
            deleteConversation(index);
        });
    });
}

// 切换到指定对话
function switchToConversation(index) {
    if (index >= 0 && index < conversations.length) {
        currentConversationIndex = index;
        currentConversationId = conversations[index].id;
        renderConversationsList();
        clearMessages();
        
        // 加载对话消息
        conversations[index].messages.forEach(msg => {
            addMessage(msg.content, msg.isUser);
        });
    }
}

// 删除对话
function deleteConversation(index) {
    if (confirm('确定要删除这个对话吗？')) {
        conversations.splice(index, 1);
        
        // 如果删除当前对话，则切换到最新对话
        if (index === currentConversationIndex) {
            if (conversations.length > 0) {
                switchToConversation(conversations.length - 1);
            } else {
                createNewConversation();
            }
        } else if (index < currentConversationIndex) {
            currentConversationIndex--;
            renderConversationsList();
        } else {
            renderConversationsList();
        }
        
        saveConversations();
    }
}

// 清空消息区域
function clearMessages() {
    document.getElementById('messages').innerHTML = '';
}

// 自动调整文本框高度
const textarea = document.getElementById('question');
textarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// 添加消息到界面和当前对话
function addMessage(content, isUser = false) {
    const messagesDiv = document.getElementById('messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
    
    if (isUser) {
        messageDiv.textContent = content;
    } else {
        try {
            // 检查内容类型
            console.log("Response content type:", typeof content);
            
            // 处理对象类型的响应
            if (typeof content === 'object') {
                // 直接访问report和download_link
                const report = content.report || (content.output && content.output.report);
                const downloadLink = content.download_link || (content.output && content.output.download_link);
                
                if (report) {
                    // 确保report是字符串
                    const reportStr = typeof report === 'string' ? report : JSON.stringify(report);
                    const markdownContent = marked.parse(reportStr);
                    let htmlContent = `<div class="markdown-body">${markdownContent}</div>`;
                    
                    // 添加下载链接
                    if (downloadLink) {
                        htmlContent += `
                            <div class="download-section">
                                <a href="${downloadLink}" target="_blank" class="download-link">
                                    📄 下载完整报告(DOCX)
                                </a>
                            </div>
                        `;
                    }
                    
                    messageDiv.innerHTML = htmlContent;
                } else {
                    // 如果没有report字段，尝试将整个对象转换为字符串
                    messageDiv.innerHTML = `<div class="markdown-body">${JSON.stringify(content, null, 2)}</div>`;
                }
            } else {
                // 处理字符串类型的响应
                const markdownContent = marked.parse(String(content));
                messageDiv.innerHTML = `<div class="markdown-body">${markdownContent}</div>`;
            }
            
            // 应用代码高亮
            if (window.hljs) {
                messageDiv.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            }
        } catch (error) {
            console.error('Error processing message:', error);
            messageDiv.innerHTML = `<div class="markdown-body error-message">处理消息时发生错误: ${error.message}</div>`;
        }
    }
    
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
    
    // 保存消息到当前对话
    if (currentConversationIndex >= 0) {
        conversations[currentConversationIndex].messages.push({
            content: content,
            isUser: isUser,
            timestamp: new Date().toISOString()
        });
        
        // 如果是第一条用户消息，用它来更新对话标题
        if (isUser && conversations[currentConversationIndex].messages.length === 1) {
            const title = content.length > 20 ? content.substring(0, 20) + '...' : content;
            conversations[currentConversationIndex].title = title;
            renderConversationsList();
        }
        
        saveConversations();
    }
}

// 发送问题
async function sendQuestion() {
    const question = document.getElementById('question').value.trim();
    if (!question) return;

    // 添加用户问题到界面
    addMessage(question, true);

    // 显示加载动画
    document.getElementById('loading').style.display = 'block';

    try {
        const response = await fetch('/get_answer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                question: question,
                conversation_id: currentConversationId
            })
        });
        const data = await response.json();

        // 隐藏加载动画
        document.getElementById('loading').style.display = 'none';

        if (data.error) {
            addMessage(`错误: ${data.error}`);
        } else {
            // 更新conversation_id
            if (data.conversation_id) {
                currentConversationId = data.conversation_id;
                if (currentConversationIndex >= 0) {
                    conversations[currentConversationIndex].id = currentConversationId;
                    saveConversations();
                }
            }
            
            addMessage(data.output);
        }
    } catch (error) {
        // 隐藏加载动画
        document.getElementById('loading').style.display = 'none';
        console.error('Error:', error);
        addMessage('发生错误，请稍后重试');
    }

    // 清空输入框并重置高度
    document.getElementById('question').value = '';
    document.getElementById('question').style.height = '24px';
}

// 添加回车发送功能
textarea.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendQuestion();
    }
}); 