<!DOCTYPE html>
<html>
<head>
    <title>AI 对话系统</title>
    <meta charset="UTF-8">
    <!-- 添加 Markdown 渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 添加代码高亮 -->
    <link href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f7f8;
        }
        .chat-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .messages-container {
            flex-grow: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 20px;
        }
        .message {
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
        }
        .user-message {
            background-color: #fff;
        }
        .assistant-message {
            background-color: #f7f7f8;
        }
        .input-area {
            position: sticky;
            bottom: 0;
            background-color: #f7f7f8;
            padding: 20px;
            border-top: 1px solid #e5e5e5;
            display: flex;
            gap: 10px;
        }
        #question {
            flex: 1;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            font-size: 16px;
            resize: none;
            height: 24px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        /* Markdown 样式 */
        .markdown-body {
            font-size: 16px;
            line-height: 1.6;
        }
        .markdown-body h1, 
        .markdown-body h2, 
        .markdown-body h3 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        .markdown-body code {
            background-color: #f6f8fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: monospace;
        }
        .markdown-body pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
        }
        .markdown-body pre code {
            background-color: transparent;
            padding: 0;
        }
        .chat-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e5e5e5;
            background: linear-gradient(120deg, #155799, #159957);
            color: white;
            margin-bottom: 20px;
        }
        .loading-animation {
            display: none;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            background-color: #f7f7f8;
        }
        .loading-dots {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .dot {
            width: 8px;
            height: 8px;
            background-color: #007bff;
            border-radius: 50%;
            animation: pulse 1.5s infinite ease-in-out;
        }
        .dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        .dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes pulse {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }
        .message {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.5s ease forwards;
        }
        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        button {
            background: linear-gradient(120deg, #155799, #159957);
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        #question {
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        #question:focus {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #159957;
        }
        .assistant-message {
            border-left: 4px solid #159957;
        }
        .user-message {
            border-left: 4px solid #155799;
        }
        @media (max-width: 768px) {
            .chat-container {
                padding: 10px;
            }
            .input-area {
                padding: 10px;
            }
        }
        .messages-container::-webkit-scrollbar {
            width: 8px;
        }
        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .messages-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="animated-background"></div>
            <div class="header-content">
                <h1>AI 智能助手</h1>
                <p>基于大模型的智能对话系统</p>
            </div>
        </div>
        <div class="messages-container" id="messages">
            <!-- 消息将在这里动态添加 -->
        </div>
        <div class="loading-animation" id="loading">
            <div class="loading-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </div>
        <div class="input-area">
            <textarea id="question" placeholder="请输入您的问题..." rows="1"></textarea>
            <button onclick="sendQuestion()">发送</button>
        </div>
    </div>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html> 