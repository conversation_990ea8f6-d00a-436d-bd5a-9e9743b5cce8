#!/usr/bin/env python3
"""
LLM容量分析模块
通过大语言模型分析容量数据，生成智能化的容量报告
"""

import json
import requests
from datetime import datetime
from typing import Dict, List, Any

class CapacityAnalyzer:
    """容量数据LLM分析器"""
    
    def __init__(self, llm_api_url=None, llm_api_key=None):
        """
        初始化分析器
        
        Args:
            llm_api_url: LLM API地址，如果为None则使用本地分析
            llm_api_key: LLM API密钥
        """
        self.llm_api_url = llm_api_url
        self.llm_api_key = llm_api_key
        self.use_llm = bool(llm_api_url)
    
    def analyze_capacity_data(self, storage_data: Dict, database_data: Dict, 
                            container_data: Dict, virtualization_data: Dict,
                            report_date: str, system_name: str) -> Dict[str, Any]:
        """
        分析容量数据并生成智能报告
        
        Args:
            storage_data: 存储容量数据
            database_data: 数据库容量数据
            container_data: 容器容量数据
            virtualization_data: 虚拟化容量数据
            report_date: 报告日期
            system_name: 系统名称
            
        Returns:
            包含分析结果的字典
        """
        
        # 1. 数据预处理和统计
        analysis_data = self._preprocess_data(
            storage_data, database_data, container_data, virtualization_data
        )
        
        # 2. 风险评估
        risk_assessment = self._assess_risks(analysis_data)
        
        # 3. 趋势分析
        trend_analysis = self._analyze_trends(analysis_data)
        
        # 4. 生成建议
        recommendations = self._generate_recommendations(analysis_data, risk_assessment)
        
        # 5. 如果配置了LLM，使用LLM进行深度分析
        if self.use_llm:
            llm_insights = self._get_llm_insights(analysis_data, risk_assessment, trend_analysis)
        else:
            llm_insights = self._get_local_insights(analysis_data, risk_assessment, trend_analysis)
        
        return {
            'analysis_data': analysis_data,
            'risk_assessment': risk_assessment,
            'trend_analysis': trend_analysis,
            'recommendations': recommendations,
            'llm_insights': llm_insights,
            'report_date': report_date,
            'system_name': system_name,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def _preprocess_data(self, storage_data: Dict, database_data: Dict, 
                        container_data: Dict, virtualization_data: Dict) -> Dict:
        """预处理和统计容量数据"""
        
        analysis = {
            'storage': {
                'total_pools': 0,
                'high_usage_pools': [],
                'critical_pools': [],
                'total_capacity_tb': 0,
                'total_used_tb': 0,
                'average_usage': 0
            },
            'database': {
                'total_instances': 0,
                'high_usage_instances': [],
                'critical_instances': [],
                'total_capacity_tb': 0,
                'total_used_tb': 0,
                'average_usage': 0
            },
            'container': {
                'total_clusters': 0,
                'high_cpu_clusters': [],
                'high_memory_clusters': [],
                'high_storage_clusters': [],
                'total_cpu_cores': 0,
                'total_memory_gb': 0,
                'total_storage_tb': 0
            },
            'virtualization': {
                'total_clusters': 0,
                'high_cpu_clusters': [],
                'high_memory_clusters': [],
                'high_storage_clusters': [],
                'total_cpu_cores': 0,
                'total_memory_gb': 0,
                'total_storage_tb': 0
            }
        }
        
        # 分析存储数据
        if storage_data and 'data' in storage_data:
            pools = storage_data['data'].get('storage_pools', [])
            analysis['storage']['total_pools'] = len(pools)
            
            total_capacity = sum(pool.get('total_capacity_gb', 0) for pool in pools)
            total_used = sum(pool.get('used_capacity_gb', 0) for pool in pools)
            
            analysis['storage']['total_capacity_tb'] = round(total_capacity / 1024, 2)
            analysis['storage']['total_used_tb'] = round(total_used / 1024, 2)
            analysis['storage']['average_usage'] = round((total_used / total_capacity * 100) if total_capacity > 0 else 0, 2)
            
            for pool in pools:
                usage = pool.get('usage_rate', 0)
                if usage >= 95:
                    analysis['storage']['critical_pools'].append({
                        'name': pool.get('pool_name', 'Unknown'),
                        'usage': usage,
                        'capacity_gb': pool.get('total_capacity_gb', 0)
                    })
                elif usage >= 85:
                    analysis['storage']['high_usage_pools'].append({
                        'name': pool.get('pool_name', 'Unknown'),
                        'usage': usage,
                        'capacity_gb': pool.get('total_capacity_gb', 0)
                    })
        
        # 分析数据库数据
        if database_data and 'data' in database_data:
            instances = database_data['data'].get('database_instances', [])
            analysis['database']['total_instances'] = len(instances)
            
            total_capacity = sum(db.get('total_capacity_gb', 0) for db in instances)
            total_used = sum(db.get('used_capacity_gb', 0) for db in instances)
            
            analysis['database']['total_capacity_tb'] = round(total_capacity / 1024, 2)
            analysis['database']['total_used_tb'] = round(total_used / 1024, 2)
            analysis['database']['average_usage'] = round((total_used / total_capacity * 100) if total_capacity > 0 else 0, 2)
            
            for db in instances:
                usage = db.get('usage_rate', 0)
                if usage >= 90:
                    analysis['database']['critical_instances'].append({
                        'name': db.get('db_name', 'Unknown'),
                        'usage': usage,
                        'capacity_gb': db.get('total_capacity_gb', 0)
                    })
                elif usage >= 80:
                    analysis['database']['high_usage_instances'].append({
                        'name': db.get('db_name', 'Unknown'),
                        'usage': usage,
                        'capacity_gb': db.get('total_capacity_gb', 0)
                    })
        
        # 分析容器数据
        if container_data and 'data' in container_data:
            clusters = container_data['data'].get('container_clusters', [])
            analysis['container']['total_clusters'] = len(clusters)
            
            analysis['container']['total_cpu_cores'] = sum(c.get('cpu_total_cores', 0) for c in clusters)
            analysis['container']['total_memory_gb'] = sum(c.get('memory_total_gb', 0) for c in clusters)
            analysis['container']['total_storage_tb'] = round(sum(c.get('storage_total_gb', 0) for c in clusters) / 1024, 2)
            
            for cluster in clusters:
                cpu_usage = cluster.get('cpu_usage_rate', 0)
                memory_usage = cluster.get('memory_usage_rate', 0)
                storage_usage = cluster.get('storage_usage_rate', 0)
                
                if cpu_usage >= 80:
                    analysis['container']['high_cpu_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': cpu_usage
                    })
                if memory_usage >= 80:
                    analysis['container']['high_memory_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': memory_usage
                    })
                if storage_usage >= 85:
                    analysis['container']['high_storage_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': storage_usage
                    })
        
        # 分析虚拟化数据
        if virtualization_data and 'data' in virtualization_data:
            clusters = virtualization_data['data'].get('vm_clusters', [])
            analysis['virtualization']['total_clusters'] = len(clusters)
            
            analysis['virtualization']['total_cpu_cores'] = sum(c.get('cpu_total_cores', 0) for c in clusters)
            analysis['virtualization']['total_memory_gb'] = sum(c.get('memory_total_gb', 0) for c in clusters)
            analysis['virtualization']['total_storage_tb'] = round(sum(c.get('storage_total_gb', 0) for c in clusters) / 1024, 2)
            
            for cluster in clusters:
                cpu_usage = cluster.get('cpu_usage_rate', 0)
                memory_usage = cluster.get('memory_usage_rate', 0)
                storage_usage = cluster.get('storage_usage_rate', 0)
                
                if cpu_usage >= 75:
                    analysis['virtualization']['high_cpu_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': cpu_usage
                    })
                if memory_usage >= 75:
                    analysis['virtualization']['high_memory_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': memory_usage
                    })
                if storage_usage >= 85:
                    analysis['virtualization']['high_storage_clusters'].append({
                        'name': cluster.get('cluster_name', 'Unknown'),
                        'usage': storage_usage
                    })
        
        return analysis
    
    def _assess_risks(self, analysis_data: Dict) -> Dict:
        """评估风险等级"""
        
        risks = {
            'overall_risk_level': 'LOW',
            'critical_issues': [],
            'warning_issues': [],
            'risk_score': 0
        }
        
        risk_score = 0
        
        # 存储风险评估
        storage = analysis_data['storage']
        if storage['critical_pools']:
            risks['critical_issues'].append(f"存储池严重告警：{len(storage['critical_pools'])}个存储池使用率超过95%")
            risk_score += len(storage['critical_pools']) * 10
        
        if storage['high_usage_pools']:
            risks['warning_issues'].append(f"存储池使用率告警：{len(storage['high_usage_pools'])}个存储池使用率超过85%")
            risk_score += len(storage['high_usage_pools']) * 5
        
        # 数据库风险评估
        database = analysis_data['database']
        if database['critical_instances']:
            risks['critical_issues'].append(f"数据库严重告警：{len(database['critical_instances'])}个数据库使用率超过90%")
            risk_score += len(database['critical_instances']) * 8
        
        if database['high_usage_instances']:
            risks['warning_issues'].append(f"数据库使用率告警：{len(database['high_usage_instances'])}个数据库使用率超过80%")
            risk_score += len(database['high_usage_instances']) * 4
        
        # 容器风险评估
        container = analysis_data['container']
        high_usage_count = len(container['high_cpu_clusters']) + len(container['high_memory_clusters']) + len(container['high_storage_clusters'])
        if high_usage_count > 0:
            risks['warning_issues'].append(f"容器集群资源告警：{high_usage_count}个集群资源使用率较高")
            risk_score += high_usage_count * 3
        
        # 虚拟化风险评估
        vm = analysis_data['virtualization']
        high_usage_count = len(vm['high_cpu_clusters']) + len(vm['high_memory_clusters']) + len(vm['high_storage_clusters'])
        if high_usage_count > 0:
            risks['warning_issues'].append(f"虚拟化集群资源告警：{high_usage_count}个集群资源使用率较高")
            risk_score += high_usage_count * 3
        
        # 确定整体风险等级
        if risk_score >= 50:
            risks['overall_risk_level'] = 'CRITICAL'
        elif risk_score >= 20:
            risks['overall_risk_level'] = 'HIGH'
        elif risk_score >= 10:
            risks['overall_risk_level'] = 'MEDIUM'
        else:
            risks['overall_risk_level'] = 'LOW'
        
        risks['risk_score'] = risk_score
        
        return risks
    
    def _analyze_trends(self, analysis_data: Dict) -> Dict:
        """分析趋势（基于模拟数据）"""
        
        trends = {
            'storage_trend': 'STABLE',
            'database_trend': 'STABLE',
            'container_trend': 'GROWING',
            'virtualization_trend': 'STABLE',
            'overall_trend': 'STABLE',
            'trend_summary': []
        }
        
        # 基于平均使用率判断趋势
        storage_usage = analysis_data['storage']['average_usage']
        if storage_usage > 80:
            trends['storage_trend'] = 'GROWING'
            trends['trend_summary'].append("存储使用率持续增长，需要关注容量规划")
        
        database_usage = analysis_data['database']['average_usage']
        if database_usage > 75:
            trends['database_trend'] = 'GROWING'
            trends['trend_summary'].append("数据库使用率呈上升趋势，建议优化数据存储")
        
        # 容器通常增长较快
        if analysis_data['container']['total_clusters'] > 0:
            trends['trend_summary'].append("容器化应用持续增长，资源需求不断上升")
        
        return trends
    
    def _generate_recommendations(self, analysis_data: Dict, risk_assessment: Dict) -> List[str]:
        """生成建议"""
        
        recommendations = []
        
        # 基于风险等级生成建议
        if risk_assessment['overall_risk_level'] == 'CRITICAL':
            recommendations.append("🚨 立即采取行动：系统存在严重容量风险，需要紧急扩容或优化")
        elif risk_assessment['overall_risk_level'] == 'HIGH':
            recommendations.append("⚠️ 高度关注：系统容量压力较大，建议在1-2周内制定扩容计划")
        elif risk_assessment['overall_risk_level'] == 'MEDIUM':
            recommendations.append("📊 持续监控：系统容量使用率偏高，建议加强监控并制定预案")
        
        # 存储建议
        storage = analysis_data['storage']
        if storage['critical_pools']:
            recommendations.append(f"💾 存储扩容：{len(storage['critical_pools'])}个存储池急需扩容")
        elif storage['high_usage_pools']:
            recommendations.append(f"💾 存储优化：{len(storage['high_usage_pools'])}个存储池建议进行数据清理或扩容")
        
        # 数据库建议
        database = analysis_data['database']
        if database['critical_instances']:
            recommendations.append(f"🗄️ 数据库优化：{len(database['critical_instances'])}个数据库需要立即优化或扩容")
        elif database['high_usage_instances']:
            recommendations.append(f"🗄️ 数据库监控：{len(database['high_usage_instances'])}个数据库建议加强监控")
        
        # 容器建议
        container = analysis_data['container']
        high_usage_count = len(container['high_cpu_clusters']) + len(container['high_memory_clusters'])
        if high_usage_count > 0:
            recommendations.append(f"🐳 容器优化：{high_usage_count}个容器集群建议进行资源调优")
        
        # 虚拟化建议
        vm = analysis_data['virtualization']
        high_usage_count = len(vm['high_cpu_clusters']) + len(vm['high_memory_clusters'])
        if high_usage_count > 0:
            recommendations.append(f"🖥️ 虚拟化优化：{high_usage_count}个虚拟化集群建议进行资源平衡")
        
        # 通用建议
        recommendations.extend([
            "📈 建议建立容量预测模型，提前规划资源需求",
            "🔄 建议定期进行容量评估，确保系统稳定运行",
            "📋 建议制定容量管理流程，规范资源申请和分配"
        ])
        
        return recommendations
    
    def _get_llm_insights(self, analysis_data: Dict, risk_assessment: Dict, trend_analysis: Dict) -> Dict:
        """通过LLM获取深度分析洞察"""
        
        # 构建LLM提示词
        prompt = self._build_llm_prompt(analysis_data, risk_assessment, trend_analysis)
        
        try:
            # 调用LLM API
            response = requests.post(
                self.llm_api_url,
                headers={
                    'Authorization': f'Bearer {self.llm_api_key}',
                    'Content-Type': 'application/json'
                },
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [
                        {'role': 'system', 'content': '你是一个专业的IT基础设施容量分析专家。'},
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 1000,
                    'temperature': 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    'source': 'LLM',
                    'insights': result.get('choices', [{}])[0].get('message', {}).get('content', ''),
                    'success': True
                }
            else:
                return self._get_local_insights(analysis_data, risk_assessment, trend_analysis)
                
        except Exception as e:
            print(f"LLM分析失败，使用本地分析: {str(e)}")
            return self._get_local_insights(analysis_data, risk_assessment, trend_analysis)
    
    def _get_local_insights(self, analysis_data: Dict, risk_assessment: Dict, trend_analysis: Dict) -> Dict:
        """本地分析洞察"""
        
        insights = []
        
        # 整体评估
        risk_level = risk_assessment['overall_risk_level']
        if risk_level == 'CRITICAL':
            insights.append("🔴 系统容量风险等级：严重。多个资源池接近或超过安全阈值，存在服务中断风险。")
        elif risk_level == 'HIGH':
            insights.append("🟡 系统容量风险等级：较高。部分资源使用率偏高，需要密切关注。")
        elif risk_level == 'MEDIUM':
            insights.append("🟠 系统容量风险等级：中等。整体运行正常，但需要持续监控。")
        else:
            insights.append("🟢 系统容量风险等级：较低。各项资源使用率在合理范围内。")
        
        # 存储分析
        storage = analysis_data['storage']
        insights.append(f"💾 存储资源总览：共{storage['total_pools']}个存储池，总容量{storage['total_capacity_tb']}TB，平均使用率{storage['average_usage']}%。")
        
        # 数据库分析
        database = analysis_data['database']
        insights.append(f"🗄️ 数据库资源总览：共{database['total_instances']}个数据库实例，总容量{database['total_capacity_tb']}TB，平均使用率{database['average_usage']}%。")
        
        # 容器分析
        container = analysis_data['container']
        insights.append(f"🐳 容器资源总览：共{container['total_clusters']}个容器集群，总CPU{container['total_cpu_cores']}核心，总内存{container['total_memory_gb']}GB。")
        
        # 虚拟化分析
        vm = analysis_data['virtualization']
        insights.append(f"🖥️ 虚拟化资源总览：共{vm['total_clusters']}个虚拟化集群，总CPU{vm['total_cpu_cores']}核心，总内存{vm['total_memory_gb']}GB。")
        
        # 趋势分析
        if trend_analysis['trend_summary']:
            insights.append("📈 趋势分析：" + "；".join(trend_analysis['trend_summary']))
        
        return {
            'source': 'LOCAL',
            'insights': "\n\n".join(insights),
            'success': True
        }
    
    def _build_llm_prompt(self, analysis_data: Dict, risk_assessment: Dict, trend_analysis: Dict) -> str:
        """构建LLM分析提示词"""
        
        prompt = f"""
请分析以下IT基础设施容量数据，并提供专业的分析报告：

## 容量数据概览
- 存储：{analysis_data['storage']['total_pools']}个存储池，总容量{analysis_data['storage']['total_capacity_tb']}TB，平均使用率{analysis_data['storage']['average_usage']}%
- 数据库：{analysis_data['database']['total_instances']}个实例，总容量{analysis_data['database']['total_capacity_tb']}TB，平均使用率{analysis_data['database']['average_usage']}%
- 容器：{analysis_data['container']['total_clusters']}个集群，总CPU{analysis_data['container']['total_cpu_cores']}核心
- 虚拟化：{analysis_data['virtualization']['total_clusters']}个集群，总CPU{analysis_data['virtualization']['total_cpu_cores']}核心

## 风险评估
- 整体风险等级：{risk_assessment['overall_risk_level']}
- 风险评分：{risk_assessment['risk_score']}
- 严重问题：{len(risk_assessment['critical_issues'])}个
- 警告问题：{len(risk_assessment['warning_issues'])}个

## 趋势分析
- 存储趋势：{trend_analysis['storage_trend']}
- 数据库趋势：{trend_analysis['database_trend']}
- 容器趋势：{trend_analysis['container_trend']}
- 虚拟化趋势：{trend_analysis['virtualization_trend']}

请提供：
1. 整体容量健康度评估
2. 主要风险点分析
3. 容量规划建议
4. 优化措施建议

请用专业、简洁的语言回答，控制在500字以内。
"""
        
        return prompt.strip()

# 创建全局分析器实例
capacity_analyzer = CapacityAnalyzer()
