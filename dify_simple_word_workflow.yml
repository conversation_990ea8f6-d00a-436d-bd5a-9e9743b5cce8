app:
  description: '简化版容量报告生成器，支持Word文档导出'
  icon: 📄
  icon_background: '#F3F4F6'
  mode: workflow
  name: 容量报告生成器-Word导出版
  use_icon_as_answer_icon: false
dependencies: []
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      enabled: false
    opening_statement: '欢迎使用容量报告生成器！我将为您生成专业的容量分析报告并保存为Word文档。'
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 'API服务地址应该怎么填写？'
    - 'Word文件保存在哪里？'
    - '如何查看生成的Word文档？'
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: start-llm
      source: start
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: code
      id: llm-code
      source: llm
      sourceHandle: source
      target: word-export
      targetHandle: target
      type: custom
    - data:
        sourceType: code
        targetType: answer
      id: code-answer
      source: word-export
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: '输入报告基本信息'
        selected: false
        title: 开始
        type: start
        variables:
        - label: 报告日期
          max_length: 50
          options: []
          required: true
          type: text-input
          variable: report_date
        - label: 系统名称
          max_length: 100
          options: []
          required: true
          type: text-input
          variable: system_name
        - label: API服务地址
          max_length: 200
          options: []
          required: false
          type: text-input
          variable: api_base_url
          default: "http://localhost:5000"
        - label: Word文件保存路径
          max_length: 500
          options: []
          required: false
          type: text-input
          variable: save_path
          default: "D:/work/LLM/reports/"
        - label: 存储容量信息
          max_length: 2000
          options: []
          required: false
          type: paragraph
          variable: storage_info
          default: "嘉兴中端虚拟化存储池：总容量769034GB，使用率89.11%，日变化-0.72%，状态正常"
        - label: 数据库容量信息
          max_length: 2000
          options: []
          required: false
          type: paragraph
          variable: database_info
          default: "嘉兴Oracle生产库：总容量8192000GB，使用率77.5%，日变化+0.8%，状态正常"
        - label: 容器资源信息
          max_length: 2000
          options: []
          required: false
          type: paragraph
          variable: container_info
          default: "嘉兴K8S生产集群：CPU使用率65%，内存使用率70%，存储使用率70%，状态正常"
        - label: 虚拟化资源信息
          max_length: 2000
          options: []
          required: false
          type: paragraph
          variable: vm_info
          default: "嘉兴vSphere生产集群：CPU使用率60%，内存使用率75%，存储使用率70%，状态正常"
      height: 89
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: '生成容量报告内容'
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: gpt-4
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。
            你需要根据提供的数据生成包含存储、数据库、容器、虚拟化四个维度的详细容量分析报告。
        - id: user-prompt
          role: user
          text: |
            请根据以下信息生成一份专业的容量报告：

            **基本信息：**
            - 报告日期：{{#start.report_date#}}
            - 系统名称：{{#start.system_name#}}
            - API服务地址：{{#start.api_base_url#}}

            **容量数据：**
            - 存储容量信息：{{#start.storage_info#}}
            - 数据库容量信息：{{#start.database_info#}}
            - 容器资源信息：{{#start.container_info#}}
            - 虚拟化资源信息：{{#start.vm_info#}}

            请严格按照以下格式生成报告：

            # {{#start.system_name#}}

            数据来源：手动输入

            ## 1. 存储资源容量及健康度排查

            存储资源池本次排查情况如下：

            | 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|-------------|------------|-------------------|------------------------|----------|
            [根据存储容量信息填写表格，至少包含3行数据]

            **健康度说明：**
            - 绿色：正常值 （存储使用率<90%）运行良好。
            - 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [分析存储池状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]

            ## 2. 数据库资源容量及健康度排查

            数据库资源池本次排查情况如下：

            | 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|-------------|------------|-------------------|------------------------|----------|
            [根据数据库容量信息填写表格，至少包含2行数据]

            **健康度说明：**
            - 绿色：正常值 （数据库使用率<85%）运行良好。
            - 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [分析数据库状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]

            ## 3. 容器资源容量及健康度排查

            容器资源池本次排查情况如下：

            | 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据容器资源信息填写表格，至少包含2行数据]

            **健康度说明：**
            - 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。
            - 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [分析容器集群状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]

            ## 4. 虚拟化资源容量及健康度排查

            虚拟化资源池本次排查情况如下：

            | 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据虚拟化资源信息填写表格，至少包含2行数据]

            **健康度说明：**
            - 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。
            - 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [分析虚拟化集群状态]

            **发现问题详情：** [如无问题则说明"今日未发现问题"]

            **应对措施和预案：** [如无问题则说明"不涉及"]

            ---

            **要求：**
            1. 严格按照上述格式输出
            2. 表格数据必须根据提供的信息准确填写
            3. 健康度评估要根据使用率阈值准确判断
            4. 状态描述要具体明确
            5. 输出格式为Markdown
            6. 确保表格格式正确，每行数据都要完整
        selected: false
        title: 生成容量报告
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: llm
      position:
        x: 400
        y: 282
      positionAbsolute:
        x: 400
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: |
          import os
          import re
          from datetime import datetime

          def markdown_to_word_simple(markdown_content, report_date, system_name, save_path):
              """
              简化版Markdown到Word转换（不依赖python-docx）
              生成HTML格式，可以用Word打开
              """
              # 确保保存路径存在
              if not save_path:
                  save_path = "D:/work/LLM/reports/"

              os.makedirs(save_path, exist_ok=True)

              # 生成文件名
              safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
              filename = f"{safe_name}_{report_date.replace('-', '')}.html"
              filepath = os.path.join(save_path, filename)

              # HTML模板
              html_content = f"""<!DOCTYPE html>
          <html>
          <head>
              <meta charset="UTF-8">
              <title>{system_name}</title>
              <style>
                  body {{
                      font-family: "Microsoft YaHei", Arial, sans-serif;
                      line-height: 1.6;
                      margin: 40px;
                      color: #333;
                  }}
                  h1 {{
                      text-align: center;
                      color: #2c3e50;
                      border-bottom: 3px solid #3498db;
                      padding-bottom: 10px;
                  }}
                  h2 {{
                      color: #34495e;
                      border-left: 4px solid #3498db;
                      padding-left: 10px;
                      margin-top: 30px;
                  }}
                  table {{
                      width: 100%;
                      border-collapse: collapse;
                      margin: 20px 0;
                      font-size: 14px;
                  }}
                  th, td {{
                      border: 1px solid #ddd;
                      padding: 12px;
                      text-align: center;
                  }}
                  th {{
                      background-color: #f8f9fa;
                      font-weight: bold;
                      color: #2c3e50;
                  }}
                  tr:nth-child(even) {{
                      background-color: #f8f9fa;
                  }}
                  .info-section {{
                      background-color: #ecf0f1;
                      padding: 15px;
                      border-radius: 5px;
                      margin: 20px 0;
                  }}
                  .health-info {{
                      background-color: #e8f5e8;
                      padding: 10px;
                      border-left: 4px solid #27ae60;
                      margin: 15px 0;
                  }}
                  .status-info {{
                      background-color: #fff3cd;
                      padding: 10px;
                      border-left: 4px solid #ffc107;
                      margin: 15px 0;
                  }}
                  .report-header {{
                      text-align: center;
                      margin-bottom: 30px;
                      padding: 20px;
                      background-color: #f8f9fa;
                      border-radius: 5px;
                  }}
              </style>
          </head>
          <body>
              <div class="report-header">
                  <h1>{system_name}</h1>
                  <p><strong>报告日期：</strong>{report_date}</p>
                  <p><strong>生成时间：</strong>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
              </div>
          """

              # 转换Markdown内容为HTML
              lines = markdown_content.split('\n')
              in_table = False
              table_html = ""

              for line in lines:
                  line = line.strip()
                  if not line:
                      continue

                  # 处理标题
                  if line.startswith('# '):
                      continue  # 跳过主标题，已在header中处理
                  elif line.startswith('## '):
                      html_content += f"<h2>{line[3:]}</h2>\n"
                  elif line.startswith('### '):
                      html_content += f"<h3>{line[4:]}</h3>\n"

                  # 处理表格
                  elif line.startswith('|') and '|' in line:
                      if not in_table:
                          in_table = True
                          table_html = "<table>\n"

                      cells = [cell.strip() for cell in line.split('|')[1:-1]]

                      # 检查是否是分隔行
                      if all(cell.startswith('-') for cell in cells):
                          continue

                      # 判断是否是表头
                      if '资源池' in line:
                          table_html += "<tr>"
                          for cell in cells:
                              table_html += f"<th>{cell}</th>"
                          table_html += "</tr>\n"
                      else:
                          table_html += "<tr>"
                          for cell in cells:
                              table_html += f"<td>{cell}</td>"
                          table_html += "</tr>\n"
                  else:
                      # 结束表格
                      if in_table:
                          table_html += "</table>\n"
                          html_content += table_html
                          in_table = False
                          table_html = ""

                      # 处理其他内容
                      if line.startswith('**') and line.endswith('**'):
                          if '健康度说明' in line:
                              html_content += f'<div class="health-info"><strong>{line[2:-2]}</strong></div>\n'
                          elif '今日状态' in line or '发现问题详情' in line or '应对措施和预案' in line:
                              html_content += f'<div class="status-info"><strong>{line[2:-2]}</strong></div>\n'
                          else:
                              html_content += f"<p><strong>{line[2:-2]}</strong></p>\n"
                      elif line.startswith('- '):
                          html_content += f"<ul><li>{line[2:]}</li></ul>\n"
                      elif line.startswith('数据来源：'):
                          html_content += f'<div class="info-section"><p><strong>{line}</strong></p></div>\n'
                      else:
                          if line and not line.startswith('---'):
                              html_content += f"<p>{line}</p>\n"

              # 结束最后的表格
              if in_table:
                  table_html += "</table>\n"
                  html_content += table_html

              html_content += """
              </body>
              </html>
              """

              # 保存HTML文件
              with open(filepath, 'w', encoding='utf-8') as f:
                  f.write(html_content)

              return filepath

          # 获取输入参数
          markdown_report = "{{#llm.text#}}"
          report_date = "{{#start.report_date#}}"
          system_name = "{{#start.system_name#}}"
          save_path = "{{#start.save_path#}}"

          try:
              # 转换并保存文档
              saved_file = markdown_to_word_simple(markdown_report, report_date, system_name, save_path)

              result = {
                  "success": True,
                  "message": "报告文档生成成功",
                  "file_path": saved_file,
                  "file_name": os.path.basename(saved_file),
                  "file_size": f"{os.path.getsize(saved_file) / 1024:.2f} KB",
                  "file_type": "HTML (可用Word打开)",
                  "note": "生成的HTML文件可以直接用Microsoft Word打开并另存为.docx格式"
              }

          except Exception as e:
              result = {
                  "success": False,
                  "message": f"文档生成失败: {str(e)}",
                  "error": str(e)
              }

          return result
        code_language: python3
        desc: '将报告保存为Word文档'
        outputs:
          result:
            type: object
        selected: false
        title: 导出Word文档
        type: code
        variables:
        - value_selector:
          - llm
          - text
          variable: markdown_report
        - value_selector:
          - start
          - report_date
          variable: report_date
        - value_selector:
          - start
          - system_name
          variable: system_name
        - value_selector:
          - start
          - save_path
          variable: save_path
      height: 89
      id: word-export
      position:
        x: 720
        y: 282
      positionAbsolute:
        x: 720
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: |
          ## 容量报告生成完成

          **报告内容：**
          {{#llm.text#}}

          ---

          **Word文档导出结果：**
          {{#word-export.result#}}
        desc: '输出生成的容量报告和Word文档信息'
        selected: false
        title: 报告输出
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1040
        y: 282
      positionAbsolute:
        x: 1040
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 0
      y: 0
      zoom: 1
