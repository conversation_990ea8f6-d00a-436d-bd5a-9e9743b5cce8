# MCP 服务器 (SSE 传输方式)

这是一个使用Server-Sent Events (SSE) 传输方式的Model Context Protocol (MCP) 服务器实现。

## 功能

- 提供天气信息查询工具 (`get_weather`)
- 提供系统命令执行工具 (`execute_command`)
- 通过SSE传输方式实时连接大模型

## 文件结构

```
mcp_server/
├── mcp.py                # 主服务器文件
├── get_weather.py        # 天气查询工具实现
├── execute_command.py    # 命令执行工具实现
└── README.md             # 本文档
```

## 安装依赖

```bash
# 安装MCP SDK和其他依赖
pip install "mcp[cli]==0.4.6" uvicorn starlette
```

## 运行服务器

```bash
# 在mcp_server目录下执行
python mcp.py
```

服务器将在 http://localhost:6274 启动，并提供以下端点：
- `/sse` - SSE连接端点
- `/messages/` - 消息发送端点

## 连接到MCP服务器

大模型可以通过配置与此服务器建立连接。以下是一个示例配置：

```json
{
  "mcpServers": {
    "tools_server": {
      "url": "http://localhost:6274/sse"
    }
  }
}
```

## 工具说明

### 1. 获取天气信息 (get_weather)

参数：
- `city`: 城市名称

示例调用:
```
get_weather(city="北京")
```

返回：
当前城市的天气信息字符串

### 2. 执行命令 (execute_command)

参数：
- `command`: 要执行的系统命令

示例调用:
```
execute_command(command="ls -la")
```

返回：
命令执行的输出结果字符串

## 注意事项

- 确保在生产环境中添加适当的安全措施，特别是对于命令执行工具
- 默认端口为6274，可以通过修改代码中的`start_server()`参数来更改 