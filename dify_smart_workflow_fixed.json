{"version": "0.1.0", "kind": "app", "data": {"title": "智能容量报告生成器", "description": "通过API获取容量数据，使用LLM分析后生成专业的Word格式容量报告", "default_language": "zh-Hans", "icon": "📊", "icon_background": "#3B82F6", "mode": "workflow", "workflow": {"conversation_variables": [], "environment_variables": [], "graph": {"edges": [{"id": "start-generate_smart_report", "source": "start", "target": "generate_smart_report"}, {"id": "generate_smart_report-export_word", "source": "generate_smart_report", "target": "export_word"}, {"id": "export_word-end", "source": "export_word", "target": "end"}], "nodes": [{"data": {"desc": "", "selected": false, "title": "开始", "type": "start", "variables": [{"label": "API服务地址", "max_length": 256, "options": [], "required": true, "type": "text-input", "variable": "api_base_url", "description": "Flask API服务的基础URL", "default": "http://localhost:5000"}, {"label": "报告日期", "max_length": 256, "options": [], "required": true, "type": "text-input", "variable": "report_date", "description": "容量报告的日期，格式：YYYY-MM-DD", "default": "2024-01-15"}, {"label": "系统名称", "max_length": 256, "options": [], "required": true, "type": "text-input", "variable": "system_name", "description": "容量报告的系统名称", "default": "生产环境运维资源容量检查报告"}, {"label": "LLM API地址", "max_length": 256, "options": [], "required": false, "type": "text-input", "variable": "llm_api_url", "description": "LLM服务的API地址（可选）", "default": ""}, {"label": "LLM API密钥", "max_length": 256, "options": [], "required": false, "type": "text-input", "variable": "llm_api_key", "description": "LLM服务的API密钥（可选）", "default": ""}, {"label": "保存路径", "max_length": 256, "options": [], "required": true, "type": "text-input", "variable": "save_path", "description": "Word文档保存路径", "default": "./reports/"}]}, "height": 116, "id": "start", "position": {"x": 80, "y": 282}, "positionAbsolute": {"x": 80, "y": 282}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "custom", "width": 244}, {"data": {"authorization": {"config": null, "type": "no-auth"}, "body": {"data": "{\n  \"report_date\": \"{{#start.report_date#}}\",\n  \"system_name\": \"{{#start.system_name#}}\",\n  \"llm_config\": {\n    \"api_url\": \"{{#start.llm_api_url#}}\",\n    \"api_key\": \"{{#start.llm_api_key#}}\"\n  }\n}", "type": "json"}, "desc": "", "headers": "Content-Type: application/json", "method": "post", "params": "", "selected": false, "timeout": {"connect": 10, "read": 60, "write": 10}, "title": "生成智能容量报告", "type": "http-request", "url": "{{#start.api_base_url#}}/api/generate_smart_report"}, "height": 116, "id": "generate_smart_report", "position": {"x": 384, "y": 282}, "positionAbsolute": {"x": 384, "y": 282}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "custom", "width": 244}, {"data": {"authorization": {"config": null, "type": "no-auth"}, "body": {"data": "{\n  \"report_content\": \"{{#generate_smart_report.body.report_content#}}\",\n  \"report_date\": \"{{#generate_smart_report.body.report_date#}}\",\n  \"system_name\": \"{{#generate_smart_report.body.system_name#}}\",\n  \"save_path\": \"{{#start.save_path#}}\"\n}", "type": "json"}, "desc": "", "headers": "Content-Type: application/json", "method": "post", "params": "", "selected": false, "timeout": {"connect": 10, "read": 30, "write": 10}, "title": "导出Word文档", "type": "http-request", "url": "{{#start.api_base_url#}}/api/export_word"}, "height": 116, "id": "export_word", "position": {"x": 688, "y": 282}, "positionAbsolute": {"x": 688, "y": 282}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "custom", "width": 244}, {"data": {"desc": "", "outputs": [{"value_selector": ["generate_smart_report", "body", "success"], "variable": "report_success", "type": "boolean"}, {"value_selector": ["generate_smart_report", "body", "report_type"], "variable": "report_type", "type": "string"}, {"value_selector": ["generate_smart_report", "body", "data_source"], "variable": "data_source", "type": "string"}, {"value_selector": ["generate_smart_report", "body", "llm_enabled"], "variable": "llm_enabled", "type": "boolean"}, {"value_selector": ["export_word", "body", "file_path"], "variable": "word_file_path", "type": "string"}, {"value_selector": ["export_word", "body", "file_size"], "variable": "word_file_size", "type": "string"}, {"value_selector": ["export_word", "body", "file_type"], "variable": "word_file_type", "type": "string"}], "selected": false, "title": "完成", "type": "end"}, "height": 116, "id": "end", "position": {"x": 992, "y": 282}, "positionAbsolute": {"x": 992, "y": 282}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "custom", "width": 244}], "viewport": {"x": 0, "y": 0, "zoom": 1}}}, "model_config": {"agent_mode": {"enabled": false, "max_iteration": 5, "strategy": "function_call", "tools": []}, "annotation_reply": {"enabled": false}, "chat_prompt_config": {}, "completion_prompt_config": {}, "dataset_configs": {"datasets": {"datasets": [], "retrieval_model": "single"}, "external_retrieval_model": {"enabled": false}, "reranking_model": {"enabled": false, "mode": "", "model": ""}}, "dataset_query_variable": "", "file_upload": {"image": {"detail": "high", "enabled": false, "number_limits": 3, "transfer_methods": ["remote_url", "local_file"]}}, "model": {"completion_params": {}, "mode": "", "name": "", "provider": ""}, "more_like_this": {"enabled": false}, "opening_statement": "欢迎使用智能容量报告生成器！\n\n本工具将：\n1. 🔍 自动获取系统容量数据\n2. 🧠 使用LLM进行智能分析\n3. 📊 生成专业的容量报告\n4. 📄 导出为Word文档格式\n\n请填写以下参数开始生成报告：", "pre_prompt": "", "prompt_type": "simple", "retriever_resource": {"enabled": false}, "sensitive_word_avoidance": {"enabled": false, "type": "", "configs": []}, "speech_to_text": {"enabled": false}, "suggested_questions": ["生成今日的生产环境容量报告", "使用LLM分析生成智能容量报告", "生成包含风险评估的容量报告", "导出专业格式的Word容量报告"], "suggested_questions_after_answer": {"enabled": false}, "text_to_speech": {"enabled": false, "language": "", "voice": ""}, "user_input_form": [{"text-input": {"default": "http://localhost:5000", "label": "API服务地址", "max_length": 256, "variable": "api_base_url"}}, {"text-input": {"default": "2024-01-15", "label": "报告日期", "max_length": 256, "variable": "report_date"}}, {"text-input": {"default": "生产环境运维资源容量检查报告", "label": "系统名称", "max_length": 256, "variable": "system_name"}}, {"text-input": {"default": "", "label": "LLM API地址（可选）", "max_length": 256, "variable": "llm_api_url"}}, {"text-input": {"default": "", "label": "LLM API密钥（可选）", "max_length": 256, "variable": "llm_api_key"}}, {"text-input": {"default": "./reports/", "label": "保存路径", "max_length": 256, "variable": "save_path"}}]}}}