import requests
import json
import subprocess
import dify_api

# 编排
def make_plan(user,question):
    print("---------------【编排被调用】---------------")
    print("\n"+question)
    
    token="app-V9K1FxhJ6gNkr1yDQ0LIYbs7"
    make_plan_ans = dify_api.run_workflow(user=user,question=question,token=token)
    print("---------------【编排结束】---------------")
    return make_plan_ans

# 生成单一shell指令
def get_shell(user,question):
    print("---------------【shell_exec被调用】---------------")
    print("\n"+question)
    
    token="app-R6iM4fofBSFaO5qGNq3D9eFF"
    get_shell_ans=dify_api.run_workflow(user=user,question=question,token=token)
    
    print("\n【指令】"+get_shell_ans+"\n")
    print("---------------【shell_exec结束】---------------")
    return get_shell_ans

# 总结分析报告
def get_summary(user,question):
    print("---------------【分析被调用】---------------")
    print("\n"+question)
    
    token="app-aDlJWRNAZCm1TcD5XYqBWKAo"
    get_summary_ans=dify_api.run_workflow(user=user,question=question,token=token)
    print("---------------【分析结束】---------------")
    return get_summary_ans